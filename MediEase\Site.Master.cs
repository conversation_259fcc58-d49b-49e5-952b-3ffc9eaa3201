using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using MediEase.Utilities;
using MediEase.DAL;

namespace MediEase
{
    public partial class SiteMaster : MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SetupUserInterface();
                LoadCartCount();
            }
        }

        private void SetupUserInterface()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            
            if (currentUser != null)
            {
                // User is authenticated
                phAuthenticated.Visible = true;
                phGuest.Visible = false;
                
                litUserName.Text = currentUser.FullName;
                
                // Show appropriate menu based on role
                switch (currentUser.Role.ToLower())
                {
                    case "admin":
                        phAdminMenu.Visible = true;
                        phPharmacistMenu.Visible = true; // Admin can access pharmacist features
                        phCustomerMenu.Visible = true;   // Admin can access customer features
                        break;
                    case "pharmacist":
                        phPharmacistMenu.Visible = true;
                        phCustomerMenu.Visible = false;
                        break;
                    case "customer":
                        phCustomerMenu.Visible = true;
                        phPharmacistMenu.Visible = false;
                        break;
                }
            }
            else
            {
                // User is not authenticated
                phAuthenticated.Visible = false;
                phGuest.Visible = true;
            }
        }

        private void LoadCartCount()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser != null && currentUser.Role.ToLower() == "customer")
            {
                try
                {
                    using (var db = new MediEaseContext())
                    {
                        var cartItemCount = db.CartItems
                            .Where(c => c.UserId == currentUser.UserId)
                            .Sum(c => (int?)c.Quantity) ?? 0;
                        
                        cartCount.InnerText = cartItemCount.ToString();
                        
                        if (cartItemCount == 0)
                        {
                            cartCount.Style["display"] = "none";
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error loading cart count");
                    cartCount.InnerText = "0";
                    cartCount.Style["display"] = "none";
                }
            }
            else
            {
                cartCount.Style["display"] = "none";
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            var searchTerm = txtSearch.Text.Trim();
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var currentUser = SecurityHelper.GetCurrentUser();

                if (currentUser != null)
                {
                    // Authenticated user - redirect to full medicines page
                    Response.Redirect($"~/Medicines.aspx?search={Server.UrlEncode(searchTerm)}");
                }
                else
                {
                    // Guest user - redirect to guest search page
                    Response.Redirect($"~/GuestSearch.aspx?search={Server.UrlEncode(searchTerm)}");
                }
            }
        }

        protected void lnkLogout_Click(object sender, EventArgs e)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser != null)
                {
                    ErrorLogger.LogUserActivity($"User logged out", currentUser.UserId);
                }

                SecurityHelper.SignOut();
                Response.Redirect("~/Default.aspx");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error during logout");
                // Still try to redirect even if logging fails
                SecurityHelper.SignOut();
                Response.Redirect("~/Default.aspx");
            }
        }

        public void UpdateCartCount()
        {
            LoadCartCount();
        }

        public void ShowSuccessMessage(string message)
        {
            ShowMessage(message, "success");
        }

        public void ShowErrorMessage(string message)
        {
            ShowMessage(message, "danger");
        }

        public void ShowWarningMessage(string message)
        {
            ShowMessage(message, "warning");
        }

        public void ShowInfoMessage(string message)
        {
            ShowMessage(message, "info");
        }

        private void ShowMessage(string message, string type)
        {
            var script = $@"
                $(document).ready(function() {{
                    showAlert('{SecurityHelper.SanitizeInput(message)}', '{type}');
                }});
            ";
            
            ScriptManager.RegisterStartupScript(this, GetType(), "ShowMessage", script, true);
        }

        public void SetPageTitle(string title)
        {
            Page.Title = title;
        }

        public void AddMetaDescription(string description)
        {
            var meta = new HtmlMeta
            {
                Name = "description",
                Content = description
            };
            Page.Header.Controls.Add(meta);
        }

        public void AddMetaKeywords(string keywords)
        {
            var meta = new HtmlMeta
            {
                Name = "keywords",
                Content = keywords
            };
            Page.Header.Controls.Add(meta);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Add any last-minute UI updates
            UpdateCartCount();
        }
    }
}
