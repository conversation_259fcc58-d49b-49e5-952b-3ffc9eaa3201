using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using MediEase.Models;

namespace MediEase
{
    public partial class SiteMaster : MasterPage
    {
        // Database connection string
        private string ConnectionString
        {
            get
            {
                string connStr = ConfigurationManager.ConnectionStrings["MediEaseConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(connStr))
                {
                    throw new InvalidOperationException("Database connection string 'MediEaseConnectionString' is not configured in Web.config.");
                }
                return connStr;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SetupUserInterface();
                LoadCartCount();
            }
        }

        // Self-contained user methods
        private User GetCurrentUser()
        {
            if (!HttpContext.Current.User.Identity.IsAuthenticated)
                return null;

            try
            {
                var ticket = ((FormsIdentity)HttpContext.Current.User.Identity).Ticket;
                var userData = ticket.UserData.Split('|');

                if (userData.Length >= 3)
                {
                    return new User
                    {
                        UserId = int.Parse(userData[0]),
                        Role = userData[1],
                        FirstName = userData[2],
                        LastName = userData.Length > 3 ? userData[3] : "",
                        Email = ticket.Name
                    };
                }
            }
            catch
            {
                // Return null if unable to parse user data
            }

            return null;
        }

        private void SetupUserInterface()
        {
            var currentUser = GetCurrentUser();

            if (currentUser != null)
            {
                // User is authenticated
                phAuthenticated.Visible = true;
                phGuest.Visible = false;

                litUserName.Text = $"{currentUser.FirstName} {currentUser.LastName}";

                // Show appropriate menu based on role
                switch (currentUser.Role.ToLower())
                {
                    case "admin":
                        phAdminMenu.Visible = true;
                        phPharmacistMenu.Visible = true; // Admin can access pharmacist features
                        phCustomerMenu.Visible = true;   // Admin can access customer features
                        break;
                    case "pharmacist":
                        phPharmacistMenu.Visible = true;
                        phCustomerMenu.Visible = false;
                        break;
                    case "customer":
                        phCustomerMenu.Visible = true;
                        phPharmacistMenu.Visible = false;
                        break;
                }
            }
            else
            {
                // User is not authenticated
                phAuthenticated.Visible = false;
                phGuest.Visible = true;
            }
        }

        private void LoadCartCount()
        {
            var currentUser = GetCurrentUser();
            if (currentUser != null && currentUser.Role.ToLower() == "customer")
            {
                try
                {
                    using (var connection = new SqlConnection(ConnectionString))
                    {
                        connection.Open();
                        string query = "SELECT ISNULL(SUM(Quantity), 0) FROM CartItems WHERE UserId = @UserId";
                        using (var command = new SqlCommand(query, connection))
                        {
                            command.Parameters.AddWithValue("@UserId", currentUser.UserId);
                            var cartItemCount = (int)command.ExecuteScalar();

                            cartCount.InnerText = cartItemCount.ToString();

                            if (cartItemCount == 0)
                            {
                                cartCount.Style["display"] = "none";
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogError(ex, "Error loading cart count");
                    cartCount.InnerText = "0";
                    cartCount.Style["display"] = "none";
                }
            }
            else
            {
                cartCount.Style["display"] = "none";
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            var searchTerm = txtSearch.Text.Trim();
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var currentUser = GetCurrentUser();

                if (currentUser != null)
                {
                    // Authenticated user - redirect to full medicines page
                    Response.Redirect($"~/Medicines.aspx?search={Server.UrlEncode(searchTerm)}");
                }
                else
                {
                    // Guest user - redirect to guest search page
                    Response.Redirect($"~/GuestSearch.aspx?search={Server.UrlEncode(searchTerm)}");
                }
            }
        }

        protected void lnkLogout_Click(object sender, EventArgs e)
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (currentUser != null)
                {
                    LogUserActivity($"User logged out", currentUser.UserId);
                }

                SignOut();
                Response.Redirect("~/Default.aspx");
            }
            catch (Exception ex)
            {
                LogError(ex, "Error during logout");
                // Still try to redirect even if logging fails
                SignOut();
                Response.Redirect("~/Default.aspx");
            }
        }

        private void SignOut()
        {
            FormsAuthentication.SignOut();
            HttpContext.Current.Session.Clear();
            HttpContext.Current.Session.Abandon();
        }

        public void UpdateCartCount()
        {
            LoadCartCount();
        }

        public void ShowSuccessMessage(string message)
        {
            ShowMessage(message, "success");
        }

        public void ShowErrorMessage(string message)
        {
            ShowMessage(message, "danger");
        }

        public void ShowWarningMessage(string message)
        {
            ShowMessage(message, "warning");
        }

        public void ShowInfoMessage(string message)
        {
            ShowMessage(message, "info");
        }

        private void ShowMessage(string message, string type)
        {
            var script = $@"
                $(document).ready(function() {{
                    showAlert('{SanitizeInput(message)}', '{type}');
                }});
            ";

            ScriptManager.RegisterStartupScript(this, GetType(), "ShowMessage", script, true);
        }

        // Self-contained utility methods
        private string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return HttpUtility.HtmlEncode(input.Trim());
        }

        // Self-contained logging methods
        private void LogError(Exception ex, string message)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {message} - {ex.Message}\r\n{ex.StackTrace}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void LogUserActivity(string activity, int userId)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] USER ACTIVITY [UserId: {userId}]: {activity}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"activity_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        public void SetPageTitle(string title)
        {
            Page.Title = title;
        }

        public void AddMetaDescription(string description)
        {
            var meta = new HtmlMeta
            {
                Name = "description",
                Content = description
            };
            Page.Header.Controls.Add(meta);
        }

        public void AddMetaKeywords(string keywords)
        {
            var meta = new HtmlMeta
            {
                Name = "keywords",
                Content = keywords
            };
            Page.Header.Controls.Add(meta);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Add any last-minute UI updates
            UpdateCartCount();
        }
    }
}
