//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase
{
    public partial class OrderConfirmation
    {
        /// <summary>
        /// lblOrderNumber control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblOrderNumber;

        /// <summary>
        /// lblOrderDate control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblOrderDate;

        /// <summary>
        /// lblCustomerName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblCustomerName;

        /// <summary>
        /// lblCustomerEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblCustomerEmail;

        /// <summary>
        /// lblShippingAddress control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblShippingAddress;

        /// <summary>
        /// gvOrderItems control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvOrderItems;

        /// <summary>
        /// lblSubtotal control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblSubtotal;

        /// <summary>
        /// lblDiscount control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblDiscount;

        /// <summary>
        /// lblTax control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTax;

        /// <summary>
        /// lblShipping control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblShipping;

        /// <summary>
        /// lblTotal control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotal;

        /// <summary>
        /// lblPaymentMethod control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPaymentMethod;

        /// <summary>
        /// lblPaymentStatus control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPaymentStatus;

        /// <summary>
        /// pnlPrescriptionNotice control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlPrescriptionNotice;

        /// <summary>
        /// lblExpectedDelivery control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblExpectedDelivery;

        /// <summary>
        /// lnkContinueShopping control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkContinueShopping;

        /// <summary>
        /// lnkViewOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.LinkButton lnkViewOrders;
    }
}
