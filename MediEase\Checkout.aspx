<%@ Page Title="Checkout" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Checkout.aspx.cs" Inherits="MediEase.Checkout" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="mb-0">Checkout</h2>
                <p class="text-muted">Complete your order</p>
            </div>
        </div>

        <!-- Checkout Steps -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="progress" style="height: 3px;">
                    <div class="progress-bar" role="progressbar" style="width: 75%"></div>
                </div>
                <div class="d-flex justify-content-between mt-2">
                    <small class="text-success">✓ Cart</small>
                    <small class="text-success">✓ Information</small>
                    <small class="text-primary font-weight-bold">Payment</small>
                    <small class="text-muted">Complete</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Checkout Form -->
            <div class="col-lg-8">
                <!-- Customer Information -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Customer Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">First Name *</label>
                                <asp:TextBox ID="txtFirstName" runat="server" CssClass="form-control" Required="true"></asp:TextBox>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Last Name *</label>
                                <asp:TextBox ID="txtLastName" runat="server" CssClass="form-control" Required="true"></asp:TextBox>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email *</label>
                                <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" TextMode="Email" Required="true"></asp:TextBox>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number *</label>
                                <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" Required="true"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Address -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Shipping Address</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label">Address *</label>
                                <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control" Required="true"></asp:TextBox>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">City *</label>
                                <asp:TextBox ID="txtCity" runat="server" CssClass="form-control" Required="true"></asp:TextBox>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">State *</label>
                                <asp:DropDownList ID="ddlState" runat="server" CssClass="form-select" Required="true">
                                    <asp:ListItem Value="">Select State</asp:ListItem>
                                    <asp:ListItem Value="CA">California</asp:ListItem>
                                    <asp:ListItem Value="NY">New York</asp:ListItem>
                                    <asp:ListItem Value="TX">Texas</asp:ListItem>
                                    <asp:ListItem Value="FL">Florida</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">ZIP Code *</label>
                                <asp:TextBox ID="txtZipCode" runat="server" CssClass="form-control" Required="true"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Payment Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label">Payment Method *</label>
                                <asp:RadioButtonList ID="rblPaymentMethod" runat="server" CssClass="form-check" RepeatDirection="Horizontal">
                                    <asp:ListItem Value="CreditCard" Selected="True">Credit Card</asp:ListItem>
                                    <asp:ListItem Value="PayPal">PayPal</asp:ListItem>
                                    <asp:ListItem Value="CashOnDelivery">Cash on Delivery</asp:ListItem>
                                </asp:RadioButtonList>
                            </div>
                        </div>

                        <asp:Panel ID="pnlCreditCard" runat="server">
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label">Card Number *</label>
                                    <asp:TextBox ID="txtCardNumber" runat="server" CssClass="form-control" placeholder="1234 5678 9012 3456" MaxLength="19"></asp:TextBox>
                                    <div class="mt-2">
                                        <img src="https://img.icons8.com/color/24/000000/visa.png" alt="Visa" class="me-2">
                                        <img src="https://img.icons8.com/color/24/000000/mastercard.png" alt="Mastercard" class="me-2">
                                        <img src="https://img.icons8.com/color/24/000000/amex.png" alt="American Express" class="me-2">
                                        <img src="https://img.icons8.com/color/24/000000/discover.png" alt="Discover">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Expiry Date *</label>
                                    <asp:TextBox ID="txtExpiryDate" runat="server" CssClass="form-control" placeholder="MM/YY" MaxLength="5"></asp:TextBox>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">CVV *</label>
                                    <asp:TextBox ID="txtCVV" runat="server" CssClass="form-control" placeholder="123" MaxLength="4"></asp:TextBox>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label">Cardholder Name *</label>
                                    <asp:TextBox ID="txtCardholderName" runat="server" CssClass="form-control"></asp:TextBox>
                                </div>
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <asp:CheckBox ID="chkSaveCard" runat="server" CssClass="form-check-input" />
                                        <label class="form-check-label">Save card for future purchases (secure)</label>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>

                        <asp:Panel ID="pnlPayPal" runat="server" Visible="false">
                            <div class="text-center py-4">
                                <img src="https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-200px.png" alt="PayPal" style="max-width: 150px;">
                                <p class="mt-3">You will be redirected to PayPal to complete your payment securely.</p>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    PayPal provides buyer protection and secure payment processing.
                                </div>
                            </div>
                        </asp:Panel>

                        <asp:Panel ID="pnlCashOnDelivery" runat="server" Visible="false">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-money-bill-wave me-2"></i>Cash on Delivery</h6>
                                <ul class="mb-0">
                                    <li>Pay when your order is delivered</li>
                                    <li>Additional COD charges: $2.99</li>
                                    <li>Please keep exact change ready</li>
                                    <li>COD available for orders under $500</li>
                                </ul>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- Prescription Upload -->
                <asp:Panel ID="pnlPrescriptionUpload" runat="server" Visible="false">
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Prescription Upload</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Your cart contains prescription medicines. Please upload a valid prescription.
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Upload Prescription *</label>
                                <asp:FileUpload ID="fuPrescription" runat="server" CssClass="form-control" Accept=".jpg,.jpeg,.png,.pdf" />
                                <small class="form-text text-muted">Accepted formats: JPG, PNG, PDF (Max 5MB)</small>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <!-- Order Notes -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Order Notes (Optional)</h5>
                    </div>
                    <div class="card-body">
                        <asp:TextBox ID="txtOrderNotes" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                            placeholder="Any special instructions for your order..."></asp:TextBox>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card shadow sticky-top" style="top: 20px;">
                    <div class="card-header">
                        <h5 class="mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <!-- Order Items -->
                        <asp:Repeater ID="rptOrderItems" runat="server">
                            <ItemTemplate>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <small class="fw-bold"><%# Eval("Medicine.Name") %></small><br>
                                        <small class="text-muted">Qty: <%# Eval("Quantity") %></small>
                                    </div>
                                    <small>$<%# Eval("TotalPrice", "{0:N2}") %></small>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>

                        <hr>

                        <!-- Order Totals -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>$<asp:Label ID="lblSubtotal" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Discount:</span>
                            <span class="text-success">-$<asp:Label ID="lblDiscount" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span>$<asp:Label ID="lblTax" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Shipping:</span>
                            <span>$<asp:Label ID="lblShipping" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total:</strong>
                            <strong>$<asp:Label ID="lblTotal" runat="server" Text="0.00"></asp:Label></strong>
                        </div>

                        <!-- Place Order Button -->
                        <asp:Button ID="btnPlaceOrder" runat="server" Text="Place Order" CssClass="btn btn-success btn-lg w-100 mb-3" OnClick="btnPlaceOrder_Click" />

                        <!-- Security Info -->
                        <div class="text-center">
                            <i class="fas fa-shield-alt text-success me-2"></i>
                            <small class="text-muted">Secure 256-bit SSL encryption</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
