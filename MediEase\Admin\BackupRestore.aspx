<%@ Page Title="Backup & Restore" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="BackupRestore.aspx.cs" Inherits="MediEase.Admin.BackupRestore" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-database me-2 text-primary"></i>Backup & Restore System</h2>
                        <p class="text-muted">Manage database backups and system restoration</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" onclick="createBackup()">
                            <i class="fas fa-save me-2"></i>Create Backup
                        </button>
                        <button type="button" class="btn btn-warning" onclick="scheduleBackup()">
                            <i class="fas fa-clock me-2"></i>Schedule Backup
                        </button>
                        <button type="button" class="btn btn-info" onclick="downloadBackup()">
                            <i class="fas fa-download me-2"></i>Download Latest
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-archive fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalBackups" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Backups</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblSuccessfulBackups" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Successful</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblLastBackupDate" runat="server" Text="Never"></asp:Label></h4>
                        <p class="mb-0">Last Backup</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-hdd fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalSize" runat="server" Text="0 MB"></asp:Label></h4>
                        <p class="mb-0">Total Size</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Backup Section -->
            <div class="col-lg-8">
                <!-- Create Backup -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Create New Backup</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Backup Type</label>
                                    <asp:DropDownList ID="ddlBackupType" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="full">Full Database Backup</asp:ListItem>
                                        <asp:ListItem Value="data">Data Only</asp:ListItem>
                                        <asp:ListItem Value="schema">Schema Only</asp:ListItem>
                                        <asp:ListItem Value="differential">Differential Backup</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Backup Name</label>
                                    <asp:TextBox ID="txtBackupName" runat="server" CssClass="form-control" placeholder="Auto-generated if empty"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <asp:TextBox ID="txtBackupDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                        placeholder="Optional backup description..."></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <asp:CheckBox ID="chkCompressBackup" runat="server" CssClass="form-check-input" Checked="true" />
                                        <label class="form-check-label" for="<%= chkCompressBackup.ClientID %>">
                                            Compress backup file
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <asp:CheckBox ID="chkIncludeFiles" runat="server" CssClass="form-check-input" />
                                        <label class="form-check-label" for="<%= chkIncludeFiles.ClientID %>">
                                            Include uploaded files (prescriptions, images)
                                        </label>
                                    </div>
                                </div>
                                <asp:Button ID="btnCreateBackup" runat="server" CssClass="btn btn-success" Text="Create Backup Now" OnClick="btnCreateBackup_Click" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Backup History -->
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Backup History</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="refreshBackupList()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="cleanupOldBackups()">
                                <i class="fas fa-trash"></i> Cleanup Old
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <asp:GridView ID="gvBackupHistory" runat="server" CssClass="table table-hover" 
                                AutoGenerateColumns="false" EmptyDataText="No backups found." OnRowCommand="gvBackupHistory_RowCommand"
                                AllowPaging="true" PageSize="10" OnPageIndexChanging="gvBackupHistory_PageIndexChanging">
                                <Columns>
                                    <asp:TemplateField HeaderText="Backup Details">
                                        <ItemTemplate>
                                            <div>
                                                <strong><%# Eval("BackupName") %></strong><br>
                                                <small class="text-muted"><%# Eval("Description") %></small><br>
                                                <span class="badge bg-info"><%# Eval("BackupType") %></span>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Date & Time">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <strong><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd, yyyy") %></strong><br>
                                                <small class="text-muted"><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("hh:mm tt") %></small>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Size & Status">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <strong><%# FormatFileSize(Convert.ToInt64(Eval("FileSize"))) %></strong><br>
                                                <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %>">
                                                    <%# Eval("Status") %>
                                                </span>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Duration">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <%# GetBackupDuration(Eval("StartTime"), Eval("EndTime")) %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Actions">
                                        <ItemTemplate>
                                            <div class="btn-group-vertical btn-group-sm">
                                                <asp:LinkButton runat="server" CssClass="btn btn-outline-info btn-sm" 
                                                    CommandName="Download" CommandArgument='<%# Eval("BackupId") %>'>
                                                    <i class="fas fa-download me-1"></i>Download
                                                </asp:LinkButton>
                                                <asp:LinkButton runat="server" CssClass="btn btn-outline-warning btn-sm mt-1" 
                                                    CommandName="Restore" CommandArgument='<%# Eval("BackupId") %>'
                                                    OnClientClick="return confirm('Are you sure you want to restore from this backup? This will overwrite current data.');">
                                                    <i class="fas fa-undo me-1"></i>Restore
                                                </asp:LinkButton>
                                                <asp:LinkButton runat="server" CssClass="btn btn-outline-danger btn-sm mt-1" 
                                                    CommandName="Delete" CommandArgument='<%# Eval("BackupId") %>'
                                                    OnClientClick="return confirm('Are you sure you want to delete this backup?');">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </asp:LinkButton>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                                <PagerStyle CssClass="pagination-ys" />
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Scheduled Backups -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Scheduled Backups</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Backup Frequency</label>
                            <asp:DropDownList ID="ddlScheduleFrequency" runat="server" CssClass="form-select">
                                <asp:ListItem Value="disabled">Disabled</asp:ListItem>
                                <asp:ListItem Value="daily">Daily</asp:ListItem>
                                <asp:ListItem Value="weekly">Weekly</asp:ListItem>
                                <asp:ListItem Value="monthly">Monthly</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Backup Time</label>
                            <asp:TextBox ID="txtScheduleTime" runat="server" CssClass="form-control" TextMode="Time" Text="02:00"></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Retention Days</label>
                            <asp:TextBox ID="txtRetentionDays" runat="server" CssClass="form-control" TextMode="Number" placeholder="30"></asp:TextBox>
                        </div>
                        <asp:Button ID="btnSaveSchedule" runat="server" CssClass="btn btn-warning w-100" Text="Save Schedule" OnClick="btnSaveSchedule_Click" />
                    </div>
                </div>

                <!-- Restore Options -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-upload me-2"></i>Restore from File</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Select Backup File</label>
                            <asp:FileUpload ID="fuRestoreFile" runat="server" CssClass="form-control" accept=".bak,.sql,.zip" />
                            <small class="text-muted">Supported formats: .bak, .sql, .zip</small>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkOverwriteData" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkOverwriteData.ClientID %>">
                                    Overwrite existing data
                                </label>
                            </div>
                        </div>
                        <asp:Button ID="btnRestoreFromFile" runat="server" CssClass="btn btn-info w-100" Text="Restore from File" 
                            OnClick="btnRestoreFromFile_Click" OnClientClick="return confirm('Are you sure you want to restore from this file? This action cannot be undone.');" />
                    </div>
                </div>

                <!-- System Health -->
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-heartbeat me-2"></i>System Health</h6>
                    </div>
                    <div class="card-body">
                        <div class="health-item d-flex justify-content-between mb-2">
                            <span>Database Size:</span>
                            <span class="fw-bold"><asp:Label ID="lblDatabaseSize" runat="server" Text="0 MB"></asp:Label></span>
                        </div>
                        <div class="health-item d-flex justify-content-between mb-2">
                            <span>Available Space:</span>
                            <span class="fw-bold"><asp:Label ID="lblAvailableSpace" runat="server" Text="0 GB"></asp:Label></span>
                        </div>
                        <div class="health-item d-flex justify-content-between mb-2">
                            <span>Last Backup:</span>
                            <span class="fw-bold"><asp:Label ID="lblLastBackup" runat="server" Text="Never"></asp:Label></span>
                        </div>
                        <div class="health-item d-flex justify-content-between mb-3">
                            <span>Backup Status:</span>
                            <span class="badge bg-success"><asp:Label ID="lblBackupStatus" runat="server" Text="Healthy"></asp:Label></span>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="checkSystemHealth()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh Health Check
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Progress Modal -->
    <div class="modal fade" id="backupProgressModal" tabindex="-1" aria-labelledby="backupProgressModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="backupProgressModalLabel">Backup in Progress</h5>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-spinner fa-spin fa-3x text-primary"></i>
                    </div>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="backupProgressBar">
                            <span id="backupProgressText">0%</span>
                        </div>
                    </div>
                    <div id="backupStatus" class="text-center">
                        <p class="mb-0">Initializing backup process...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="cancelBackup()" id="btnCancelBackup">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function createBackup() {
            $('#backupProgressModal').modal('show');
            simulateBackupProgress();
            __doPostBack('<%= btnCreateBackup.UniqueID %>', '');
        }

        function scheduleBackup() {
            // Focus on schedule section
            $('html, body').animate({
                scrollTop: $("#ddlScheduleFrequency").offset().top - 100
            }, 500);
        }

        function downloadBackup() {
            // Download latest backup
            window.open('DownloadBackup.aspx?latest=true', '_blank');
        }

        function refreshBackupList() {
            location.reload();
        }

        function cleanupOldBackups() {
            if (confirm('Are you sure you want to delete old backups? This action cannot be undone.')) {
                $.post('BackupRestore.aspx/CleanupOldBackups', {}, function(response) {
                    if (response.d.success) {
                        alert('Old backups cleaned up successfully!');
                        location.reload();
                    } else {
                        alert('Error cleaning up backups: ' + response.d.message);
                    }
                });
            }
        }

        function checkSystemHealth() {
            $.post('BackupRestore.aspx/CheckSystemHealth', {}, function(response) {
                if (response.d.success) {
                    location.reload();
                } else {
                    alert('Error checking system health: ' + response.d.message);
                }
            });
        }

        function simulateBackupProgress() {
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    $('#backupProgressModal').modal('hide');
                }
                
                $('#backupProgressBar').css('width', progress + '%');
                $('#backupProgressText').text(Math.round(progress) + '%');
                
                if (progress < 20) {
                    $('#backupStatus').html('<p class="mb-0">Preparing database...</p>');
                } else if (progress < 50) {
                    $('#backupStatus').html('<p class="mb-0">Backing up tables...</p>');
                } else if (progress < 80) {
                    $('#backupStatus').html('<p class="mb-0">Compressing backup file...</p>');
                } else if (progress < 100) {
                    $('#backupStatus').html('<p class="mb-0">Finalizing backup...</p>');
                } else {
                    $('#backupStatus').html('<p class="mb-0 text-success">Backup completed successfully!</p>');
                }
            }, 500);
        }

        function cancelBackup() {
            if (confirm('Are you sure you want to cancel the backup process?')) {
                $('#backupProgressModal').modal('hide');
                // Cancel backup logic here
            }
        }
    </script>
</asp:Content>
