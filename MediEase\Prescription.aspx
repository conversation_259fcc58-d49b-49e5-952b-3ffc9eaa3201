<%@ Page Title="Upload Prescription" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Prescription.aspx.cs" Inherits="MediEase.PrescriptionUpload" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-prescription me-2"></i>Upload Prescription</h3>
                        <p class="mb-0">Upload your prescription and get medicines delivered to your doorstep</p>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlUpload" runat="server">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-upload me-2 text-primary"></i>Upload Prescription</h5>
                                    <p class="text-muted">Upload a clear image or PDF of your prescription</p>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Patient Name *</label>
                                        <asp:TextBox ID="txtPatientName" runat="server" CssClass="form-control" placeholder="Enter patient name" required="true" />
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Doctor Name</label>
                                        <asp:TextBox ID="txtDoctorName" runat="server" CssClass="form-control" placeholder="Enter doctor name" />
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Prescription File *</label>
                                        <asp:FileUpload ID="fuPrescription" runat="server" CssClass="form-control" accept=".jpg,.jpeg,.png,.pdf" required="true" />
                                        <div class="form-text">Supported formats: JPG, PNG, PDF (Max 5MB)</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Additional Notes</label>
                                        <asp:TextBox ID="txtNotes" runat="server" TextMode="MultiLine" Rows="3" CssClass="form-control" placeholder="Any special instructions or notes" />
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Delivery Address *</label>
                                        <asp:TextBox ID="txtDeliveryAddress" runat="server" TextMode="MultiLine" Rows="3" CssClass="form-control" placeholder="Enter complete delivery address" required="true" />
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Phone Number *</label>
                                        <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" placeholder="Enter contact number" required="true" />
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <asp:CheckBox ID="chkUrgent" runat="server" CssClass="form-check-input" />
                                        <label class="form-check-label">
                                            Mark as urgent (additional charges may apply)
                                        </label>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <asp:Button ID="btnUpload" runat="server" CssClass="btn btn-primary btn-lg" Text="Upload Prescription" OnClick="btnUpload_Click" />
                                        <asp:Button ID="btnClear" runat="server" CssClass="btn btn-outline-secondary" Text="Clear Form" OnClick="btnClear_Click" />
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h5><i class="fas fa-info-circle me-2 text-info"></i>How it Works</h5>
                                    <div class="timeline">
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary">1</div>
                                            <div class="timeline-content">
                                                <h6>Upload Prescription</h6>
                                                <p>Upload a clear photo or PDF of your prescription</p>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-success">2</div>
                                            <div class="timeline-content">
                                                <h6>Pharmacist Review</h6>
                                                <p>Our licensed pharmacist will review and validate your prescription</p>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-info">3</div>
                                            <div class="timeline-content">
                                                <h6>Order Confirmation</h6>
                                                <p>You'll receive a call to confirm medicines and delivery details</p>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-warning">4</div>
                                            <div class="timeline-content">
                                                <h6>Fast Delivery</h6>
                                                <p>Medicines delivered to your doorstep within 24-48 hours</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info mt-4">
                                        <h6><i class="fas fa-shield-alt me-2"></i>Privacy & Security</h6>
                                        <ul class="mb-0">
                                            <li>All prescriptions are encrypted and secure</li>
                                            <li>Only licensed pharmacists can access your data</li>
                                            <li>We comply with all healthcare privacy regulations</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>
                        
                        <asp:Panel ID="pnlSuccess" runat="server" Visible="false">
                            <div class="text-center">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                <h3 class="text-success mt-3">Prescription Uploaded Successfully!</h3>
                                <p class="lead">Your prescription has been received and is being reviewed by our pharmacist.</p>
                                <div class="alert alert-success">
                                    <strong>Reference ID: </strong><asp:Literal ID="litReferenceId" runat="server" />
                                </div>
                                <p>You will receive a confirmation call within 2-4 hours. Our team will contact you at <strong><asp:Literal ID="litContactNumber" runat="server" /></strong></p>
                                <div class="d-grid gap-2 d-md-block">
                                    <asp:Button ID="btnUploadAnother" runat="server" CssClass="btn btn-primary" Text="Upload Another Prescription" OnClick="btnUploadAnother_Click" />
                                    <a href="~/Medicines.aspx" class="btn btn-outline-primary">Browse Medicines</a>
                                </div>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
                
                <!-- Recent Prescriptions for Logged-in Users -->
                <asp:Panel ID="pnlRecentPrescriptions" runat="server" Visible="false" CssClass="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-history me-2"></i>Your Recent Prescriptions</h5>
                        </div>
                        <div class="card-body">
                            <asp:Repeater ID="rptRecentPrescriptions" runat="server">
                                <ItemTemplate>
                                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                        <div>
                                            <strong><%# Eval("PrescriptionNumber") %></strong>
                                            <br><small class="text-muted">Created: <%# Eval("CreatedDate", "{0:MMM dd, yyyy}") %></small>
                                        </div>
                                        <div>
                                            <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %>"><%# Eval("Status") %></span>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </asp:Panel>
            </div>
        </div>
    </div>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-marker {
            position: absolute;
            left: -30px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        .timeline-content h6 {
            margin-bottom: 5px;
            color: #333;
        }
        .timeline-content p {
            margin-bottom: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</asp:Content>
