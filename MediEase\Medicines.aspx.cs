using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class Medicines : Page
    {
        private const int PageSize = 12;
        private int CurrentPage => ViewState["CurrentPage"] as int? ?? 1;
        private string CurrentSort => ViewState["CurrentSort"] as string ?? "name";
        private string SearchTerm => ViewState["SearchTerm"] as string ?? "";

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                InitializePage();
                LoadFilters();
                LoadMedicines();
            }
        }

        private void InitializePage()
        {
            // Check for search parameter from URL
            var searchParam = Request.QueryString["search"];
            if (!string.IsNullOrEmpty(searchParam))
            {
                txtSearchMedicine.Text = searchParam;
                ViewState["SearchTerm"] = searchParam;
            }

            SetPageMetadata();
        }

        private void SetPageMetadata()
        {
            Page.Title = "Medicines - MediEase Pharmacy";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Browse our comprehensive collection of medicines and health products. Find prescription and over-the-counter medications with detailed information and competitive prices.");
                master.AddMetaKeywords("medicines, pharmacy, prescription drugs, over-the-counter, health products, medical supplies");
            }
        }

        private void LoadFilters()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Load categories
                    var categories = db.Categories
                        .Where(c => c.IsActive)
                        .OrderBy(c => c.Name)
                        .Select(c => new { Text = c.Name, Value = c.Name })
                        .ToList();

                    ddlCategory.DataSource = categories;
                    ddlCategory.DataTextField = "Text";
                    ddlCategory.DataValueField = "Value";
                    ddlCategory.DataBind();
                    ddlCategory.Items.Insert(0, new ListItem("All Categories", ""));

                    // Load brands
                    var brands = db.Brands
                        .Where(b => b.IsActive)
                        .OrderBy(b => b.Name)
                        .Select(b => new { Text = b.Name, Value = b.Name })
                        .ToList();

                    ddlBrand.DataSource = brands;
                    ddlBrand.DataTextField = "Text";
                    ddlBrand.DataValueField = "Value";
                    ddlBrand.DataBind();
                    ddlBrand.Items.Insert(0, new ListItem("All Brands", ""));
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading filters");
                ShowErrorMessage("Error loading filter options.");
            }
        }

        private void LoadMedicines()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var query = db.Medicines.Where(m => m.IsActive);

                    // Apply search filter
                    if (!string.IsNullOrEmpty(SearchTerm))
                    {
                        var searchLower = SearchTerm.ToLower();
                        query = query.Where(m => 
                            m.Name.ToLower().Contains(searchLower) ||
                            m.GenericName.ToLower().Contains(searchLower) ||
                            m.Description.ToLower().Contains(searchLower) ||
                            m.Indications.ToLower().Contains(searchLower) ||
                            m.Category.ToLower().Contains(searchLower) ||
                            m.Brand.ToLower().Contains(searchLower));
                    }

                    // Apply category filter
                    if (!string.IsNullOrEmpty(ddlCategory.SelectedValue))
                    {
                        query = query.Where(m => m.Category == ddlCategory.SelectedValue);
                    }

                    // Apply brand filter
                    if (!string.IsNullOrEmpty(ddlBrand.SelectedValue))
                    {
                        query = query.Where(m => m.Brand == ddlBrand.SelectedValue);
                    }

                    // Apply advanced filters
                    ApplyAdvancedFilters(ref query);

                    // Apply sorting
                    query = ApplySorting(query);

                    // Get total count for pagination
                    var totalCount = query.Count();

                    // Apply pagination
                    var medicines = query
                        .Skip((CurrentPage - 1) * PageSize)
                        .Take(PageSize)
                        .Select(m => new
                        {
                            m.MedicineId,
                            m.Name,
                            m.GenericName,
                            m.Brand,
                            m.Category,
                            m.Strength,
                            m.PackSize,
                            m.Unit,
                            m.Price,
                            m.DiscountAmount,
                            m.DiscountPercentage,
                            m.StockQuantity,
                            m.ImagePath,
                            m.PrescriptionRequired,
                            m.AverageRating,
                            m.ReviewCount,
                            FinalPrice = m.Price - (m.DiscountAmount > 0 ? m.DiscountAmount : 
                                       m.DiscountPercentage > 0 ? m.Price * (m.DiscountPercentage / 100) : 0)
                        })
                        .ToList();

                    // Bind data
                    rptMedicines.DataSource = medicines;
                    rptMedicines.DataBind();

                    // Show/hide no results panel
                    pnlNoResults.Visible = !medicines.Any();

                    // Update results count
                    UpdateResultsCount(totalCount);

                    // Generate pagination
                    GeneratePagination(totalCount);

                    // Update sort label
                    UpdateSortLabel();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading medicines");
                ShowErrorMessage("Error loading medicines. Please try again.");
            }
        }

        private void ApplyAdvancedFilters(ref IQueryable<Medicine> query)
        {
            // Price range filter
            if (!string.IsNullOrEmpty(txtMinPrice.Text) && decimal.TryParse(txtMinPrice.Text, out decimal minPrice))
            {
                query = query.Where(m => m.Price >= minPrice);
            }

            if (!string.IsNullOrEmpty(txtMaxPrice.Text) && decimal.TryParse(txtMaxPrice.Text, out decimal maxPrice))
            {
                query = query.Where(m => m.Price <= maxPrice);
            }

            // In stock only filter
            if (chkInStockOnly.Checked)
            {
                query = query.Where(m => m.StockQuantity > 0);
            }

            // Prescription required filter
            if (chkPrescriptionRequired.Checked)
            {
                query = query.Where(m => m.PrescriptionRequired);
            }

            // On sale only filter
            if (chkOnSaleOnly.Checked)
            {
                query = query.Where(m => m.DiscountAmount > 0 || m.DiscountPercentage > 0);
            }
        }

        private IQueryable<Medicine> ApplySorting(IQueryable<Medicine> query)
        {
            switch (CurrentSort.ToLower())
            {
                case "name":
                    return query.OrderBy(m => m.Name);
                case "price":
                    return query.OrderBy(m => m.Price);
                case "rating":
                    return query.OrderByDescending(m => m.AverageRating).ThenByDescending(m => m.ReviewCount);
                case "popular":
                    return query.OrderByDescending(m => m.PurchaseCount).ThenByDescending(m => m.ViewCount);
                default:
                    return query.OrderBy(m => m.Name);
            }
        }

        private void UpdateResultsCount(int totalCount)
        {
            var startItem = (CurrentPage - 1) * PageSize + 1;
            var endItem = Math.Min(CurrentPage * PageSize, totalCount);

            if (totalCount == 0)
            {
                lblResultsCount.Text = "No medicines found";
            }
            else
            {
                lblResultsCount.Text = $"Showing {startItem}-{endItem} of {totalCount} medicines";
            }
        }

        private void UpdateSortLabel()
        {
            string sortText;
            switch (CurrentSort.ToLower())
            {
                case "name":
                    sortText = "Sorted by Name (A-Z)";
                    break;
                case "price":
                    sortText = "Sorted by Price (Low to High)";
                    break;
                case "rating":
                    sortText = "Sorted by Rating (Highest First)";
                    break;
                case "popular":
                    sortText = "Sorted by Popularity";
                    break;
                default:
                    sortText = "Default Sort";
                    break;
            }

            lblCurrentSort.Text = sortText;
        }

        private void GeneratePagination(int totalCount)
        {
            var totalPages = (int)Math.Ceiling((double)totalCount / PageSize);
            var paginationItems = new List<object>();

            if (totalPages <= 1)
            {
                rptPagination.DataSource = paginationItems;
                rptPagination.DataBind();
                return;
            }

            // Previous button
            if (CurrentPage > 1)
            {
                paginationItems.Add(new { PageNumber = CurrentPage - 1, Text = "Previous", CssClass = "" });
            }

            // Page numbers
            var startPage = Math.Max(1, CurrentPage - 2);
            var endPage = Math.Min(totalPages, CurrentPage + 2);

            for (int i = startPage; i <= endPage; i++)
            {
                paginationItems.Add(new 
                { 
                    PageNumber = i, 
                    Text = i.ToString(), 
                    CssClass = i == CurrentPage ? "active" : "" 
                });
            }

            // Next button
            if (CurrentPage < totalPages)
            {
                paginationItems.Add(new { PageNumber = CurrentPage + 1, Text = "Next", CssClass = "" });
            }

            rptPagination.DataSource = paginationItems;
            rptPagination.DataBind();
        }

        protected void btnSearchMedicine_Click(object sender, EventArgs e)
        {
            ViewState["SearchTerm"] = txtSearchMedicine.Text.Trim();
            ViewState["CurrentPage"] = 1;
            LoadMedicines();
        }

        protected void ddlCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            ViewState["CurrentPage"] = 1;
            LoadMedicines();
        }

        protected void ddlBrand_SelectedIndexChanged(object sender, EventArgs e)
        {
            ViewState["CurrentPage"] = 1;
            LoadMedicines();
        }

        protected void lnkSort_Click(object sender, EventArgs e)
        {
            var linkButton = sender as LinkButton;
            ViewState["CurrentSort"] = linkButton.CommandArgument;
            ViewState["CurrentPage"] = 1;
            LoadMedicines();
        }

        protected void btnApplyFilters_Click(object sender, EventArgs e)
        {
            ViewState["CurrentPage"] = 1;
            LoadMedicines();
        }

        protected void btnClearFilters_Click(object sender, EventArgs e)
        {
            // Clear all filters
            txtSearchMedicine.Text = "";
            ddlCategory.SelectedIndex = 0;
            ddlBrand.SelectedIndex = 0;
            txtMinPrice.Text = "";
            txtMaxPrice.Text = "";
            chkInStockOnly.Checked = false;
            chkPrescriptionRequired.Checked = false;
            chkOnSaleOnly.Checked = false;

            ViewState["SearchTerm"] = "";
            ViewState["CurrentPage"] = 1;
            ViewState["CurrentSort"] = "name";

            LoadMedicines();
        }

        protected void rptMedicines_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            if (e.CommandName == "ViewDetails")
            {
                var medicineId = Convert.ToInt32(e.CommandArgument);
                Response.Redirect($"~/MedicineDetails.aspx?id={medicineId}");
            }
        }

        protected void rptPagination_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            if (e.CommandName == "Page")
            {
                ViewState["CurrentPage"] = Convert.ToInt32(e.CommandArgument);
                LoadMedicines();
            }
        }

        // Helper methods for data binding
        protected string GenerateStarRating(decimal rating)
        {
            var stars = "";
            var fullStars = (int)Math.Floor(rating);
            var hasHalfStar = rating - fullStars >= 0.5m;

            for (int i = 0; i < fullStars; i++)
            {
                stars += "<i class='fas fa-star text-warning'></i>";
            }

            if (hasHalfStar)
            {
                stars += "<i class='fas fa-star-half-alt text-warning'></i>";
                fullStars++;
            }

            for (int i = fullStars; i < 5; i++)
            {
                stars += "<i class='far fa-star text-muted'></i>";
            }

            return stars;
        }

        protected string GetStockStatusClass(int stockQuantity)
        {
            if (stockQuantity <= 0) return "out";
            if (stockQuantity <= 10) return "low";
            return "in";
        }

        protected string GetStockStatusText(int stockQuantity)
        {
            if (stockQuantity <= 0) return "Out of Stock";
            if (stockQuantity <= 10) return "Low Stock";
            return "In Stock";
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        [System.Web.Services.WebMethod]
        public static object AddToCart(int medicineId, int quantity)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();

                using (var db = new MediEaseContext())
                {
                    var medicine = db.Medicines.Find(medicineId);
                    if (medicine == null || !medicine.IsActive)
                    {
                        return new { success = false, message = "Medicine not found or not available." };
                    }

                    if (medicine.StockQuantity < quantity)
                    {
                        return new { success = false, message = "Insufficient stock available." };
                    }

                    if (currentUser == null)
                    {
                        // Handle guest cart (session-based)
                        var guestCart = HttpContext.Current.Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
                        if (guestCart == null)
                        {
                            guestCart = new System.Collections.Generic.List<CartItem>();
                            HttpContext.Current.Session["GuestCart"] = guestCart;
                        }

                        var existingItem = guestCart.FirstOrDefault(c => c.MedicineId == medicineId);
                        if (existingItem != null)
                        {
                            existingItem.Quantity += quantity;
                            existingItem.TotalPrice = existingItem.UnitPrice * existingItem.Quantity;
                        }
                        else
                        {
                            var cartItem = new CartItem
                            {
                                CartItemId = guestCart.Count + 1, // Temporary ID for guest cart
                                MedicineId = medicineId,
                                Quantity = quantity,
                                UnitPrice = medicine.FinalPrice,
                                TotalPrice = medicine.FinalPrice * quantity,
                                CreatedDate = DateTime.Now
                            };
                            guestCart.Add(cartItem);
                        }
                    }
                    else
                    {
                        // Handle logged in user cart
                        var existingCartItem = db.CartItems.FirstOrDefault(c => c.UserId == currentUser.UserId && c.MedicineId == medicineId);

                        if (existingCartItem != null)
                        {
                            existingCartItem.Quantity += quantity;
                            existingCartItem.TotalPrice = existingCartItem.UnitPrice * existingCartItem.Quantity;
                            existingCartItem.ModifiedDate = DateTime.Now;
                        }
                        else
                        {
                            var cartItem = new CartItem
                            {
                                UserId = currentUser.UserId,
                                MedicineId = medicineId,
                                Quantity = quantity,
                                UnitPrice = medicine.FinalPrice,
                                TotalPrice = medicine.FinalPrice * quantity,
                                CreatedDate = DateTime.Now
                            };
                            db.CartItems.Add(cartItem);
                        }

                        db.SaveChanges();
                    }

                    return new { success = true, message = "Medicine added to cart successfully!" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error adding medicine to cart");
                return new { success = false, message = "Error adding to cart. Please try again." };
            }
        }

        [System.Web.Services.WebMethod]
        public static int GetCartCount()
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();

                if (currentUser == null)
                {
                    // Guest cart count
                    var guestCart = HttpContext.Current.Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
                    return guestCart?.Sum(c => c.Quantity) ?? 0;
                }
                else
                {
                    // Logged in user cart count
                    using (var db = new MediEaseContext())
                    {
                        return db.CartItems.Where(c => c.UserId == currentUser.UserId).Sum(c => c.Quantity);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting cart count");
                return 0;
            }
        }
    }
}
