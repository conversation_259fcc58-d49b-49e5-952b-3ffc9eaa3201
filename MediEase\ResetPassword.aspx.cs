using System;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Utilities;
using BCrypt.Net;

namespace MediEase
{
    public partial class ResetPassword : Page
    {
        private string ResetToken => Request.QueryString["token"];

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SetPageMetadata();
                
                // Check if user is already logged in
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser != null)
                {
                    Response.Redirect("~/Default.aspx");
                    return;
                }

                // Validate reset token
                if (string.IsNullOrEmpty(ResetToken))
                {
                    ShowErrorMessage("Invalid or missing reset token. Please request a new password reset.");
                    pnlResetForm.Visible = false;
                    return;
                }

                if (!IsValidResetToken(ResetToken))
                {
                    ShowErrorMessage("This password reset link is invalid or has expired. Please request a new password reset.");
                    pnlResetForm.Visible = false;
                    return;
                }
            }
        }

        protected void btnUpdatePassword_Click(object sender, EventArgs e)
        {
            if (Page.IsValid && !string.IsNullOrEmpty(ResetToken))
            {
                try
                {
                    using (var db = new MediEaseContext())
                    {
                        // Find the password reset record
                        var passwordReset = db.PasswordResets
                            .FirstOrDefault(pr => pr.ResetToken == ResetToken && 
                                                 !pr.IsUsed && 
                                                 pr.ExpiryDate > DateTime.Now);

                        if (passwordReset == null)
                        {
                            ShowErrorMessage("This password reset link is invalid or has expired.");
                            return;
                        }

                        // Find the user
                        var user = db.Users.Find(passwordReset.UserId);
                        if (user == null || !user.IsActive)
                        {
                            ShowErrorMessage("User account not found or inactive.");
                            return;
                        }

                        // Update password
                        var newPassword = txtNewPassword.Text;
                        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                        user.ModifiedDate = DateTime.Now;

                        // Mark reset token as used
                        passwordReset.IsUsed = true;
                        passwordReset.UsedDate = DateTime.Now;
                        passwordReset.UsedIPAddress = Request.UserHostAddress;

                        // Save changes
                        db.SaveChanges();

                        // Log the password reset
                        ErrorLogger.LogUserActivity($"Password reset completed for user: {user.Email}", user.UserId);

                        // Show success message
                        ShowSuccessMessage();

                        // Clear form
                        ClearForm();
                    }
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error resetting password");
                    ShowErrorMessage("An error occurred while resetting your password. Please try again.");
                }
            }
        }

        private bool IsValidResetToken(string token)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var passwordReset = db.PasswordResets
                        .FirstOrDefault(pr => pr.ResetToken == token && 
                                             !pr.IsUsed && 
                                             pr.ExpiryDate > DateTime.Now);

                    return passwordReset != null;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error validating reset token");
                return false;
            }
        }

        private void ShowSuccessMessage()
        {
            pnlResetForm.Visible = false;
            pnlSuccess.Visible = true;
            pnlSuccessActions.Visible = true;
            pnlError.Visible = false;
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            pnlSuccessActions.Visible = false;
            lblError.Text = message;
        }

        private void ClearForm()
        {
            txtNewPassword.Text = "";
            txtConfirmPassword.Text = "";
        }

        private void SetPageMetadata()
        {
            Page.Title = "Reset Password - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Create a new password for your MediEase account. Secure password reset with validation.");
                master.AddMetaKeywords("reset password, new password, MediEase account, password change, secure reset");
            }
        }
    }
}
