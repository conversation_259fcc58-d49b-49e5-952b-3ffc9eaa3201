using System;
using System.Web.UI;
using MediEase.Utilities;

namespace MediEase.Customer
{
    public partial class FeedbackReviews : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check customer authorization
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (currentUser.Role != "Customer" && currentUser.Role != "Admin"))
                {
                    Response.Redirect("~/Login.aspx");
                    return;
                }

                LoadFeedbackHistory();
            }
        }

        private void LoadFeedbackHistory()
        {
            try
            {
                // Implementation for loading feedback history
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading feedback history");
                ShowErrorMessage("Error loading feedback history.");
            }
        }

        protected void btnSubmitFeedback_Click(object sender, EventArgs e)
        {
            try
            {
                // Implementation for submitting feedback
                ShowSuccessMessage("Feedback submitted successfully.");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error submitting feedback");
                ShowErrorMessage("Error submitting feedback.");
            }
        }

        private void ShowSuccessMessage(string message)
        {
            // Implementation for showing success message
        }

        private void ShowErrorMessage(string message)
        {
            // Implementation for showing error message
        }
    }
}
