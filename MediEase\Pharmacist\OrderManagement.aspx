<%@ Page Title="Order Management" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="OrderManagement.aspx.cs" Inherits="MediEase.Pharmacist.OrderManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-shopping-cart me-2"></i>Order Management</h2>
                        <p class="text-muted">Process and manage customer orders</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="badge bg-warning fs-6">
                            <asp:Label ID="lblPendingOrders" runat="server" Text="0"></asp:Label> Pending
                        </span>
                        <span class="badge bg-info fs-6">
                            <asp:Label ID="lblProcessingOrders" runat="server" Text="0"></asp:Label> Processing
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTodayOrders" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Today's Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblCompletedToday" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Completed Today</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblUrgentOrders" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Urgent Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>$<asp:Label ID="lblTodayRevenue" runat="server" Text="0.00"></asp:Label></h4>
                        <p class="mb-0">Today's Revenue</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Orders</asp:ListItem>
                                    <asp:ListItem Value="Pending" Selected="True">Pending</asp:ListItem>
                                    <asp:ListItem Value="Processing">Processing</asp:ListItem>
                                    <asp:ListItem Value="Verified">Verified</asp:ListItem>
                                    <asp:ListItem Value="Packed">Packed</asp:ListItem>
                                    <asp:ListItem Value="Shipped">Shipped</asp:ListItem>
                                    <asp:ListItem Value="Delivered">Delivered</asp:ListItem>
                                    <asp:ListItem Value="Cancelled">Cancelled</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Priority</label>
                                <asp:DropDownList ID="ddlPriorityFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlPriorityFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Priorities</asp:ListItem>
                                    <asp:ListItem Value="Urgent">Urgent</asp:ListItem>
                                    <asp:ListItem Value="Normal">Normal</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Date Range</label>
                                <asp:DropDownList ID="ddlDateFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlDateFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="today" Selected="True">Today</asp:ListItem>
                                    <asp:ListItem Value="yesterday">Yesterday</asp:ListItem>
                                    <asp:ListItem Value="week">This Week</asp:ListItem>
                                    <asp:ListItem Value="month">This Month</asp:ListItem>
                                    <asp:ListItem Value="all">All Time</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search by order number, customer name..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <asp:Button ID="btnRefresh" runat="server" CssClass="btn btn-outline-secondary" Text="Refresh" OnClick="btnRefresh_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders List -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Orders</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="toggleView('list')">
                                <i class="fas fa-list"></i> List View
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="toggleView('kanban')">
                                <i class="fas fa-columns"></i> Kanban View
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- List View -->
                        <div id="listView">
                            <div class="table-responsive">
                                <asp:GridView ID="gvOrders" runat="server" CssClass="table table-hover" 
                                    AutoGenerateColumns="false" EmptyDataText="No orders found." OnRowCommand="gvOrders_RowCommand">
                                    <Columns>
                                        <asp:TemplateField HeaderText="Order Info">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("OrderNumber") %></strong>
                                                    <%# Convert.ToBoolean(Eval("IsUrgent")) ? 
                                                        "<span class=\"badge bg-danger ms-2\">URGENT</span>" : "" %>
                                                    <br>
                                                    <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd, yyyy hh:mm tt") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Customer">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("CustomerName") %></strong><br>
                                                    <small class="text-muted"><%# Eval("CustomerEmail") %></small><br>
                                                    <small class="text-muted"><%# Eval("CustomerPhone") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Items">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("ItemCount") %> items</strong><br>
                                                    <small class="text-muted"><%# Eval("ItemSummary") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Amount">
                                            <ItemTemplate>
                                                <div class="text-center">
                                                    <strong class="fs-6">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></strong><br>
                                                    <small class="text-muted"><%# Eval("PaymentMethod") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Status">
                                            <ItemTemplate>
                                                <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %> fs-6">
                                                    <%# Eval("Status") %>
                                                </span>
                                                <%# Convert.ToBoolean(Eval("RequiresPrescription")) ? 
                                                    "<br><span class=\"badge bg-info mt-1\">Prescription Required</span>" : "" %>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Actions">
                                            <ItemTemplate>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <%# GetActionButtons(Eval("Status").ToString(), Eval("OrderId")) %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>

                        <!-- Kanban View (Hidden by default) -->
                        <div id="kanbanView" style="display: none;">
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <div class="card">
                                        <div class="card-header bg-warning text-white text-center">
                                            <h6 class="mb-0">Pending</h6>
                                        </div>
                                        <div class="card-body p-2" id="pendingColumn">
                                            <asp:Repeater ID="rptPendingOrders" runat="server">
                                                <ItemTemplate>
                                                    <div class="card mb-2 order-card" data-order-id='<%# Eval("OrderId") %>' data-status="Pending">
                                                        <div class="card-body p-2">
                                                            <h6 class="card-title small"><%# Eval("OrderNumber") %></h6>
                                                            <p class="card-text small mb-1"><%# Eval("CustomerName") %></p>
                                                            <p class="card-text small mb-1">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></p>
                                                            <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd") %></small>
                                                        </div>
                                                    </div>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card">
                                        <div class="card-header bg-info text-white text-center">
                                            <h6 class="mb-0">Processing</h6>
                                        </div>
                                        <div class="card-body p-2" id="processingColumn">
                                            <asp:Repeater ID="rptProcessingOrders" runat="server">
                                                <ItemTemplate>
                                                    <div class="card mb-2 order-card" data-order-id='<%# Eval("OrderId") %>' data-status="Processing">
                                                        <div class="card-body p-2">
                                                            <h6 class="card-title small"><%# Eval("OrderNumber") %></h6>
                                                            <p class="card-text small mb-1"><%# Eval("CustomerName") %></p>
                                                            <p class="card-text small mb-1">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></p>
                                                            <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd") %></small>
                                                        </div>
                                                    </div>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white text-center">
                                            <h6 class="mb-0">Verified</h6>
                                        </div>
                                        <div class="card-body p-2" id="verifiedColumn">
                                            <asp:Repeater ID="rptVerifiedOrders" runat="server">
                                                <ItemTemplate>
                                                    <div class="card mb-2 order-card" data-order-id='<%# Eval("OrderId") %>' data-status="Verified">
                                                        <div class="card-body p-2">
                                                            <h6 class="card-title small"><%# Eval("OrderNumber") %></h6>
                                                            <p class="card-text small mb-1"><%# Eval("CustomerName") %></p>
                                                            <p class="card-text small mb-1">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></p>
                                                            <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd") %></small>
                                                        </div>
                                                    </div>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card">
                                        <div class="card-header bg-secondary text-white text-center">
                                            <h6 class="mb-0">Packed</h6>
                                        </div>
                                        <div class="card-body p-2" id="packedColumn">
                                            <asp:Repeater ID="rptPackedOrders" runat="server">
                                                <ItemTemplate>
                                                    <div class="card mb-2 order-card" data-order-id='<%# Eval("OrderId") %>' data-status="Packed">
                                                        <div class="card-body p-2">
                                                            <h6 class="card-title small"><%# Eval("OrderNumber") %></h6>
                                                            <p class="card-text small mb-1"><%# Eval("CustomerName") %></p>
                                                            <p class="card-text small mb-1">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></p>
                                                            <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd") %></small>
                                                        </div>
                                                    </div>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card">
                                        <div class="card-header bg-dark text-white text-center">
                                            <h6 class="mb-0">Shipped</h6>
                                        </div>
                                        <div class="card-body p-2" id="shippedColumn">
                                            <asp:Repeater ID="rptShippedOrders" runat="server">
                                                <ItemTemplate>
                                                    <div class="card mb-2 order-card" data-order-id='<%# Eval("OrderId") %>' data-status="Shipped">
                                                        <div class="card-body p-2">
                                                            <h6 class="card-title small"><%# Eval("OrderNumber") %></h6>
                                                            <p class="card-text small mb-1"><%# Eval("CustomerName") %></p>
                                                            <p class="card-text small mb-1">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></p>
                                                            <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd") %></small>
                                                        </div>
                                                    </div>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card">
                                        <div class="card-header bg-success text-white text-center">
                                            <h6 class="mb-0">Delivered</h6>
                                        </div>
                                        <div class="card-body p-2" id="deliveredColumn">
                                            <asp:Repeater ID="rptDeliveredOrders" runat="server">
                                                <ItemTemplate>
                                                    <div class="card mb-2 order-card" data-order-id='<%# Eval("OrderId") %>' data-status="Delivered">
                                                        <div class="card-body p-2">
                                                            <h6 class="card-title small"><%# Eval("OrderNumber") %></h6>
                                                            <p class="card-text small mb-1"><%# Eval("CustomerName") %></p>
                                                            <p class="card-text small mb-1">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></p>
                                                            <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd") %></small>
                                                        </div>
                                                    </div>
                                                </ItemTemplate>
                                            </asp:Repeater>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Orders Message -->
        <asp:Panel ID="pnlNoOrders" runat="server" Visible="false">
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h4>No orders found</h4>
                <p class="text-muted">No orders match your current filter criteria.</p>
            </div>
        </asp:Panel>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderModal" tabindex="-1" aria-labelledby="orderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderModalLabel">Order Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="orderDetails">
                        <!-- Order details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="orderActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .order-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .kanban-column {
            min-height: 400px;
            background-color: #f8f9fa;
        }
    </style>

    <script>
        function toggleView(viewType) {
            const listView = document.getElementById('listView');
            const kanbanView = document.getElementById('kanbanView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'list') {
                listView.style.display = 'block';
                kanbanView.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                listView.style.display = 'none';
                kanbanView.style.display = 'block';
                buttons[1].classList.add('active');
            }
        }

        // Order card click handler
        $(document).on('click', '.order-card', function() {
            const orderId = $(this).data('order-id');
            loadOrderDetails(orderId);
        });

        function loadOrderDetails(orderId) {
            $.ajax({
                type: 'POST',
                url: 'OrderManagement.aspx/GetOrderDetails',
                data: JSON.stringify({ orderId: orderId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#orderDetails').html(response.d.html);
                        $('#orderActions').html(response.d.actions);
                        $('#orderModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading order details.');
                }
            });
        }

        function updateOrderStatus(orderId, newStatus) {
            $.ajax({
                type: 'POST',
                url: 'OrderManagement.aspx/UpdateOrderStatus',
                data: JSON.stringify({ orderId: orderId, newStatus: newStatus }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Order status updated successfully!');
                        location.reload();
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error updating order status.');
                }
            });
        }
    </script>
</asp:Content>
