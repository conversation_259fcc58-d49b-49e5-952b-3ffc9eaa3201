<%@ Page Title="Prescription Validation" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="PrescriptionValidation.aspx.cs" Inherits="MediEase.Pharmacist.PrescriptionValidation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-prescription me-2"></i>Prescription Validation</h2>
                        <p class="text-muted">Review and validate customer prescriptions</p>
                    </div>
                    <div>
                        <span class="badge bg-warning fs-6">
                            <asp:Label ID="lblPendingCount" runat="server" Text="0"></asp:Label> Pending
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Status Filter</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Prescriptions</asp:ListItem>
                                    <asp:ListItem Value="Pending" Selected="True">Pending Validation</asp:ListItem>
                                    <asp:ListItem Value="Validated">Validated</asp:ListItem>
                                    <asp:ListItem Value="Rejected">Rejected</asp:ListItem>
                                    <asp:ListItem Value="Expired">Expired</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Priority</label>
                                <asp:DropDownList ID="ddlPriorityFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlPriorityFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Priorities</asp:ListItem>
                                    <asp:ListItem Value="Emergency">Emergency</asp:ListItem>
                                    <asp:ListItem Value="Urgent">Urgent</asp:ListItem>
                                    <asp:ListItem Value="Normal">Normal</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search by patient name, doctor, or prescription number..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <asp:Button ID="btnRefresh" runat="server" CssClass="btn btn-outline-secondary" Text="Refresh" OnClick="btnRefresh_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prescriptions List -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Prescriptions for Validation</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="toggleView('list')">
                                <i class="fas fa-list"></i> List View
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="toggleView('card')">
                                <i class="fas fa-th"></i> Card View
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- List View -->
                        <div id="listView">
                            <div class="table-responsive">
                                <asp:GridView ID="gvPrescriptions" runat="server" CssClass="table table-hover" 
                                    AutoGenerateColumns="false" EmptyDataText="No prescriptions found." OnRowCommand="gvPrescriptions_RowCommand">
                                    <Columns>
                                        <asp:TemplateField HeaderText="Priority">
                                            <ItemTemplate>
                                                <span class="badge bg-<%# GetPriorityColor(Eval("Priority").ToString()) %>">
                                                    <%# Eval("Priority") %>
                                                </span>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Prescription Info">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("PrescriptionNumber") %></strong><br>
                                                    <small class="text-muted">Uploaded: <%# Convert.ToDateTime(Eval("UploadDate")).ToString("MMM dd, yyyy hh:mm tt") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Patient">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("PatientName") %></strong><br>
                                                    <small class="text-muted">DOB: <%# Convert.ToDateTime(Eval("PatientDOB")).ToString("MM/dd/yyyy") %></small><br>
                                                    <small class="text-muted">Phone: <%# Eval("PatientPhone") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Doctor">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("DoctorName") %></strong><br>
                                                    <small class="text-muted">License: <%# Eval("DoctorLicense") %></small><br>
                                                    <small class="text-muted"><%# Eval("HospitalClinic") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Prescription Details">
                                            <ItemTemplate>
                                                <div>
                                                    <strong>Date:</strong> <%# Convert.ToDateTime(Eval("PrescriptionDate")).ToString("MM/dd/yyyy") %><br>
                                                    <strong>Valid Until:</strong> <%# Convert.ToDateTime(Eval("ValidUntil")).ToString("MM/dd/yyyy") %><br>
                                                    <%# !string.IsNullOrEmpty(Eval("Diagnosis")?.ToString()) ? 
                                                        "<strong>Diagnosis:</strong> " + Eval("Diagnosis") + "<br>" : "" %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Status">
                                            <ItemTemplate>
                                                <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %> fs-6">
                                                    <%# Eval("Status") %>
                                                </span>
                                                <%# Convert.ToBoolean(Eval("IsEmergency")) ? 
                                                    "<br><span class=\"badge bg-danger mt-1\">EMERGENCY</span>" : "" %>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Actions">
                                            <ItemTemplate>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <asp:LinkButton ID="btnValidate" runat="server" CssClass="btn btn-success btn-sm" 
                                                        CommandName="Validate" CommandArgument='<%# Eval("PrescriptionId") %>'
                                                        Visible='<%# Eval("Status").ToString() == "Pending" %>'>
                                                        <i class="fas fa-check me-1"></i>Validate
                                                    </asp:LinkButton>
                                                    <asp:LinkButton ID="btnReject" runat="server" CssClass="btn btn-danger btn-sm mt-1" 
                                                        CommandName="Reject" CommandArgument='<%# Eval("PrescriptionId") %>'
                                                        Visible='<%# Eval("Status").ToString() == "Pending" %>'>
                                                        <i class="fas fa-times me-1"></i>Reject
                                                    </asp:LinkButton>
                                                    <button type="button" class="btn btn-info btn-sm mt-1 view-prescription" 
                                                        data-prescription-id='<%# Eval("PrescriptionId") %>'>
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>

                        <!-- Card View (Hidden by default) -->
                        <div id="cardView" class="row g-4" style="display: none;">
                            <asp:Repeater ID="rptPrescriptionsCard" runat="server">
                                <ItemTemplate>
                                    <div class="col-lg-6 col-xl-4">
                                        <div class="card prescription-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span class="fw-bold"><%# Eval("PrescriptionNumber") %></span>
                                                <span class="badge bg-<%# GetPriorityColor(Eval("Priority").ToString()) %>">
                                                    <%# Eval("Priority") %>
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <div class="row mb-3">
                                                    <div class="col-6">
                                                        <h6 class="text-muted">Patient</h6>
                                                        <p class="mb-1 fw-bold"><%# Eval("PatientName") %></p>
                                                        <small class="text-muted"><%# Convert.ToDateTime(Eval("PatientDOB")).ToString("MM/dd/yyyy") %></small>
                                                    </div>
                                                    <div class="col-6">
                                                        <h6 class="text-muted">Doctor</h6>
                                                        <p class="mb-1 fw-bold"><%# Eval("DoctorName") %></p>
                                                        <small class="text-muted"><%# Eval("DoctorLicense") %></small>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <h6 class="text-muted">Prescription Date</h6>
                                                    <p class="mb-1"><%# Convert.ToDateTime(Eval("PrescriptionDate")).ToString("MMM dd, yyyy") %></p>
                                                    <small class="text-muted">Valid until: <%# Convert.ToDateTime(Eval("ValidUntil")).ToString("MMM dd, yyyy") %></small>
                                                </div>
                                                
                                                <%# Convert.ToBoolean(Eval("IsEmergency")) ? 
                                                    "<div class=\"alert alert-danger py-2 mb-3\"><i class=\"fas fa-exclamation-triangle me-1\"></i>EMERGENCY PRESCRIPTION</div>" : "" %>
                                                
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %> fs-6">
                                                        <%# Eval("Status") %>
                                                    </span>
                                                    <small class="text-muted">
                                                        <%# Convert.ToDateTime(Eval("UploadDate")).ToString("MMM dd, hh:mm tt") %>
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-grid gap-2">
                                                    <%# Eval("Status").ToString() == "Pending" ? 
                                                        "<div class=\"btn-group w-100\">" +
                                                        "<button class=\"btn btn-success validate-prescription\" data-prescription-id=\"" + Eval("PrescriptionId") + "\">" +
                                                        "<i class=\"fas fa-check me-1\"></i>Validate</button>" +
                                                        "<button class=\"btn btn-danger reject-prescription\" data-prescription-id=\"" + Eval("PrescriptionId") + "\">" +
                                                        "<i class=\"fas fa-times me-1\"></i>Reject</button></div>" : "" %>
                                                    <button class="btn btn-outline-info view-prescription" data-prescription-id='<%# Eval("PrescriptionId") %>'>
                                                        <i class="fas fa-eye me-1"></i>View Details
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Prescriptions Message -->
        <asp:Panel ID="pnlNoPrescriptions" runat="server" Visible="false">
            <div class="text-center py-5">
                <i class="fas fa-prescription fa-3x text-muted mb-3"></i>
                <h4>No prescriptions found</h4>
                <p class="text-muted">No prescriptions match your current filter criteria.</p>
            </div>
        </asp:Panel>
    </div>

    <!-- Prescription Details Modal -->
    <div class="modal fade" id="prescriptionModal" tabindex="-1" aria-labelledby="prescriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="prescriptionModalLabel">Prescription Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="prescriptionDetails">
                        <!-- Prescription details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" id="btnModalValidate">
                        <i class="fas fa-check me-1"></i>Validate
                    </button>
                    <button type="button" class="btn btn-danger" id="btnModalReject">
                        <i class="fas fa-times me-1"></i>Reject
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .prescription-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .prescription-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
    </style>

    <script>
        function toggleView(viewType) {
            const listView = document.getElementById('listView');
            const cardView = document.getElementById('cardView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'list') {
                listView.style.display = 'block';
                cardView.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                listView.style.display = 'none';
                cardView.style.display = 'flex';
                buttons[1].classList.add('active');
            }
        }

        // View prescription details
        $(document).on('click', '.view-prescription', function() {
            const prescriptionId = $(this).data('prescription-id');
            loadPrescriptionDetails(prescriptionId);
        });

        // Validate prescription
        $(document).on('click', '.validate-prescription', function() {
            const prescriptionId = $(this).data('prescription-id');
            if (confirm('Are you sure you want to validate this prescription?')) {
                validatePrescription(prescriptionId);
            }
        });

        // Reject prescription
        $(document).on('click', '.reject-prescription', function() {
            const prescriptionId = $(this).data('prescription-id');
            const reason = prompt('Please enter the reason for rejection:');
            if (reason) {
                rejectPrescription(prescriptionId, reason);
            }
        });

        function loadPrescriptionDetails(prescriptionId) {
            $.ajax({
                type: 'POST',
                url: 'PrescriptionValidation.aspx/GetPrescriptionDetails',
                data: JSON.stringify({ prescriptionId: prescriptionId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#prescriptionDetails').html(response.d.html);
                        $('#prescriptionModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading prescription details.');
                }
            });
        }

        function validatePrescription(prescriptionId) {
            $.ajax({
                type: 'POST',
                url: 'PrescriptionValidation.aspx/ValidatePrescription',
                data: JSON.stringify({ prescriptionId: prescriptionId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Prescription validated successfully!');
                        location.reload();
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error validating prescription.');
                }
            });
        }

        function rejectPrescription(prescriptionId, reason) {
            $.ajax({
                type: 'POST',
                url: 'PrescriptionValidation.aspx/RejectPrescription',
                data: JSON.stringify({ prescriptionId: prescriptionId, reason: reason }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Prescription rejected successfully!');
                        location.reload();
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error rejecting prescription.');
                }
            });
        }
    </script>
</asp:Content>
