using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using MediEase.Models;

namespace MediEase
{
    public partial class Login : Page
    {
        // Database connection string
        private string ConnectionString
        {
            get
            {
                string connStr = ConfigurationManager.ConnectionStrings["MediEaseConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(connStr))
                {
                    throw new InvalidOperationException("Database connection string 'MediEaseConnectionString' is not configured in Web.config.");
                }
                return connStr;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (GetCurrentUser() != null)
                {
                    RedirectToUserDashboard();
                    return;
                }

                // Check for return URL
                var returnUrl = Request.QueryString["ReturnUrl"];
                if (!string.IsNullOrEmpty(returnUrl))
                {
                    ViewState["ReturnUrl"] = returnUrl;
                }

                // Check for success message (e.g., from registration)
                var successMessage = Request.QueryString["message"];
                if (!string.IsNullOrEmpty(successMessage))
                {
                    ShowSuccessMessage(successMessage);
                }

                SetPageMetadata();
            }
        }

        private void SetPageMetadata()
        {
            Page.Title = "Login - MediEase Pharmacy";

            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Login to your MediEase account to access pharmacy services, order medicines, and manage your health records.");
                master.AddMetaKeywords("login, pharmacy account, MediEase login, pharmacy services");
            }
        }

        // Self-contained security methods
        private bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch
            {
                return false;
            }
        }

        private User GetCurrentUser()
        {
            if (!HttpContext.Current.User.Identity.IsAuthenticated)
                return null;

            try
            {
                var ticket = ((FormsIdentity)HttpContext.Current.User.Identity).Ticket;
                var userData = ticket.UserData.Split('|');

                if (userData.Length >= 3)
                {
                    return new User
                    {
                        UserId = int.Parse(userData[0]),
                        Role = userData[1],
                        FirstName = userData[2],
                        Email = ticket.Name
                    };
                }
            }
            catch
            {
                // Return null if unable to parse user data
            }

            return null;
        }

        private bool IsAccountLocked(User user)
        {
            return user.AccountLockedUntil.HasValue && user.AccountLockedUntil.Value > DateTime.Now;
        }

        private void SetCurrentUser(User user)
        {
            try
            {
                if (HttpContext.Current?.Session != null)
                {
                    HttpContext.Current.Session["CurrentUser"] = user;
                }
            }
            catch (Exception ex)
            {
                LogError(ex, "Error setting current user in session");
            }
        }

        private void SignInUser(User user, bool rememberMe = false)
        {
            try
            {
                var ticket = new FormsAuthenticationTicket(
                    1,
                    user.Email,
                    DateTime.Now,
                    DateTime.Now.AddMinutes(rememberMe ? 43200 : 30), // 30 days or 30 minutes
                    rememberMe,
                    user.Role,
                    FormsAuthentication.FormsCookiePath);

                var encryptedTicket = FormsAuthentication.Encrypt(ticket);
                var authCookie = new HttpCookie(FormsAuthentication.FormsCookieName, encryptedTicket)
                {
                    HttpOnly = true,
                    Secure = HttpContext.Current.Request.IsSecureConnection
                };

                if (rememberMe)
                {
                    authCookie.Expires = DateTime.Now.AddDays(30);
                }

                HttpContext.Current.Response.Cookies.Add(authCookie);
            }
            catch (Exception ex)
            {
                LogError(ex, "Error signing in user");
            }
        }

        private string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return HttpUtility.HtmlEncode(input.Trim());
        }

        // Self-contained database methods
        private User GetUserByEmail(string email)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        SELECT UserId, FirstName, LastName, Email, PasswordHash, PhoneNumber,
                               Address, City, State, PostalCode, Country, Role, DateOfBirth,
                               Gender, IsActive, LoyaltyPoints, CreatedDate, IsEmailVerified,
                               LastLoginDate, EmailVerificationToken, PasswordResetToken
                        FROM Users
                        WHERE Email = @Email AND IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new User
                                {
                                    UserId = (int)reader["UserId"],
                                    FirstName = reader["FirstName"] as string,
                                    LastName = reader["LastName"] as string,
                                    Email = reader["Email"] as string,
                                    PasswordHash = reader["PasswordHash"] as string,
                                    PhoneNumber = reader["PhoneNumber"] as string,
                                    Address = reader["Address"] as string,
                                    City = reader["City"] as string,
                                    State = reader["State"] as string,
                                    PostalCode = reader["PostalCode"] as string,
                                    Country = reader["Country"] as string,
                                    Role = (reader["Role"] as string) ?? "Customer",
                                    DateOfBirth = reader["DateOfBirth"] as DateTime?,
                                    Gender = reader["Gender"] as string,
                                    IsActive = (bool)reader["IsActive"],
                                    LoyaltyPoints = reader["LoyaltyPoints"] != DBNull.Value ? (int)reader["LoyaltyPoints"] : 0,
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    IsEmailVerified = (bool)reader["IsEmailVerified"],
                                    LastLogin = reader["LastLoginDate"] as DateTime?,
                                    EmailVerificationToken = reader["EmailVerificationToken"] as string,
                                    PasswordResetToken = reader["PasswordResetToken"] as string,
                                    FailedLoginAttempts = 0,
                                    IsPhoneVerified = false
                                };
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogError(ex, "GetUserByEmail");
                throw;
            }
        }

        private void UpdateUserLogin(int userId, DateTime? lastLogin = null, int? failedAttempts = null, DateTime? lockedUntil = null)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        UPDATE Users SET
                            LastLoginDate = COALESCE(@LastLogin, LastLoginDate),
                            ModifiedDate = GETDATE()
                        WHERE UserId = @UserId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@LastLogin", lastLogin ?? (object)DBNull.Value);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, "UpdateUserLogin");
                throw;
            }
        }

        protected void btnLogin_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                var email = txtEmail.Text.Trim().ToLower();
                var password = txtPassword.Text;

                // Validate input
                if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password))
                {
                    ShowErrorMessage("Please enter both email and password.");
                    return;
                }

                // Get user using direct database access
                var user = GetUserByEmail(email);

                if (user == null)
                {
                    ShowErrorMessage("Invalid email or password.");
                    LogFailedLoginAttempt(email, "User not found");
                    return;
                }

                    // Check if account is locked
                    if (IsAccountLocked(user))
                    {
                        var lockoutTime = user.AccountLockedUntil?.ToString("yyyy-MM-dd HH:mm:ss");
                        ShowErrorMessage($"Account is locked due to multiple failed login attempts. Please try again after {lockoutTime}.");
                        return;
                    }

                    // Check if account is active
                    if (!user.IsActive)
                    {
                        ShowErrorMessage("Your account has been deactivated. Please contact support.");
                        LogFailedLoginAttempt(email, "Account deactivated");
                        return;
                    }

                    // Verify password
                    if (!VerifyPassword(password, user.PasswordHash))
                    {
                        user.FailedLoginAttempts++;

                        // Lock account after 5 failed attempts
                        if (user.FailedLoginAttempts >= 5)
                        {
                            user.AccountLockedUntil = DateTime.Now.AddMinutes(30);
                        }

                        UpdateUserLogin(user.UserId, null, user.FailedLoginAttempts, user.AccountLockedUntil);

                        var remainingAttempts = 5 - user.FailedLoginAttempts;
                        if (remainingAttempts > 0)
                        {
                            ShowErrorMessage($"Invalid email or password. {remainingAttempts} attempts remaining before account lockout.");
                        }
                        else
                        {
                            ShowErrorMessage("Account has been locked due to multiple failed login attempts.");
                        }

                        LogFailedLoginAttempt(email, "Invalid password");
                        return;
                    }

                    // Check if email is verified (optional - can be enforced based on requirements)
                    if (!user.IsEmailVerified)
                    {
                        ShowWarningMessage("Your email address is not verified. Some features may be limited.");
                    }

                    // Successful login - reset failed attempts
                    UpdateUserLogin(user.UserId, DateTime.Now, 0, null);

                    // Set user in session and create authentication
                    SetCurrentUser(user);
                    SignInUser(user, chkRememberMe.Checked);

                    // Log successful login
                    LogUserActivity($"User logged in successfully", user.UserId);

                    // Redirect to appropriate page
                    RedirectAfterLogin(user);
            }
            catch (Exception ex)
            {
                LogError(ex, "Error during login process");
                ShowErrorMessage("An error occurred during login. Please try again.");
            }
        }

        private void RedirectAfterLogin(User user)
        {
            // Check for return URL first
            var returnUrl = ViewState["ReturnUrl"] as string;
            if (!string.IsNullOrEmpty(returnUrl) && IsValidReturnUrl(returnUrl))
            {
                Response.Redirect(returnUrl);
                return;
            }

            // Redirect based on user role
            switch (user.Role.ToLower())
            {
                case "admin":
                    Response.Redirect("~/Admin/Dashboard.aspx");
                    break;
                case "pharmacist":
                    Response.Redirect("~/Pharmacist/Dashboard.aspx");
                    break;
                case "customer":
                    Response.Redirect("~/Customer/Dashboard.aspx");
                    break;
                default:
                    Response.Redirect("~/Default.aspx");
                    break;
            }
        }

        private void RedirectToUserDashboard()
        {
            var currentUser = GetCurrentUser();
            if (currentUser != null)
            {
                switch (currentUser.Role.ToLower())
                {
                    case "admin":
                        Response.Redirect("~/Admin/Dashboard.aspx");
                        break;
                    case "pharmacist":
                        Response.Redirect("~/Pharmacist/Dashboard.aspx");
                        break;
                    case "customer":
                        Response.Redirect("~/Customer/Dashboard.aspx");
                        break;
                    default:
                        Response.Redirect("~/Default.aspx");
                        break;
                }
            }
        }

        private bool IsValidReturnUrl(string returnUrl)
        {
            // Ensure return URL is relative and safe
            if (string.IsNullOrEmpty(returnUrl))
                return false;

            // Must start with / or ~/
            if (!returnUrl.StartsWith("/") && !returnUrl.StartsWith("~/"))
                return false;

            // Must not contain dangerous patterns
            var dangerousPatterns = new[] { "..", "javascript:", "data:", "vbscript:" };
            var lowerUrl = returnUrl.ToLower();
            
            return !dangerousPatterns.Any(pattern => lowerUrl.Contains(pattern));
        }

        private void LogFailedLoginAttempt(string email, string reason)
        {
            try
            {
                LogInfo($"Failed login attempt for email: {email}. Reason: {reason}", "Security");
            }
            catch
            {
                // Silent fail for logging
            }
        }

        // Self-contained logging methods
        private void LogError(Exception ex, string message)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {message} - {ex.Message}\r\n{ex.StackTrace}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void LogInfo(string message, string category)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO [{category}]: {message}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"info_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void LogUserActivity(string activity, int userId)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] USER ACTIVITY [UserId: {userId}]: {activity}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"activity_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            litErrorMessage.Text = SanitizeInput(message);
        }

        private void ShowSuccessMessage(string message)
        {
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            litSuccessMessage.Text = SanitizeInput(message);
        }

        private void ShowWarningMessage(string message)
        {
            // For warnings, we'll use the success panel with different styling
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            pnlSuccess.CssClass = "alert alert-warning";
            litSuccessMessage.Text = SanitizeInput(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Clear password field for security
            txtPassword.Attributes.Add("value", "");
        }
    }
}
