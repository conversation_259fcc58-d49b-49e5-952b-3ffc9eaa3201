using System;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class Login : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (SecurityHelper.GetCurrentUser() != null)
                {
                    RedirectToUserDashboard();
                    return;
                }

                // Check for return URL
                var returnUrl = Request.QueryString["ReturnUrl"];
                if (!string.IsNullOrEmpty(returnUrl))
                {
                    ViewState["ReturnUrl"] = returnUrl;
                }

                // Check for success message (e.g., from registration)
                var successMessage = Request.QueryString["message"];
                if (!string.IsNullOrEmpty(successMessage))
                {
                    ShowSuccessMessage(successMessage);
                }

                SetPageMetadata();
            }
        }

        private void SetPageMetadata()
        {
            Page.Title = "Login - MediEase Pharmacy";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Login to your MediEase account to access pharmacy services, order medicines, and manage your health records.");
                master.AddMetaKeywords("login, pharmacy account, MediEase login, pharmacy services");
            }
        }

        protected void btnLogin_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                var email = txtEmail.Text.Trim().ToLower();
                var password = txtPassword.Text;

                // Validate input
                if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password))
                {
                    ShowErrorMessage("Please enter both email and password.");
                    return;
                }

                // Get user using direct database access
                var user = MediEase.DAL.DirectDatabaseAccess.GetUserByEmail(email);

                if (user == null)
                {
                    ShowErrorMessage("Invalid email or password.");
                    LogFailedLoginAttempt(email, "User not found");
                    return;
                }

                    // Check if account is locked
                    if (SecurityHelper.IsAccountLocked(user))
                    {
                        var lockoutTime = user.AccountLockedUntil?.ToString("yyyy-MM-dd HH:mm:ss");
                        ShowErrorMessage($"Account is locked due to multiple failed login attempts. Please try again after {lockoutTime}.");
                        return;
                    }

                    // Check if account is active
                    if (!user.IsActive)
                    {
                        ShowErrorMessage("Your account has been deactivated. Please contact support.");
                        LogFailedLoginAttempt(email, "Account deactivated");
                        return;
                    }

                    // Verify password
                    if (!SecurityHelper.VerifyPassword(password, user.PasswordHash))
                    {
                        user.FailedLoginAttempts++;

                        // Lock account after 5 failed attempts
                        if (user.FailedLoginAttempts >= 5)
                        {
                            user.AccountLockedUntil = DateTime.Now.AddMinutes(30);
                        }

                        MediEase.DAL.DirectDatabaseAccess.UpdateUserLogin(user.UserId, null, user.FailedLoginAttempts, user.AccountLockedUntil);

                        var remainingAttempts = 5 - user.FailedLoginAttempts;
                        if (remainingAttempts > 0)
                        {
                            ShowErrorMessage($"Invalid email or password. {remainingAttempts} attempts remaining before account lockout.");
                        }
                        else
                        {
                            ShowErrorMessage("Account has been locked due to multiple failed login attempts.");
                        }

                        LogFailedLoginAttempt(email, "Invalid password");
                        return;
                    }

                    // Check if email is verified (optional - can be enforced based on requirements)
                    if (!user.IsEmailVerified)
                    {
                        ShowWarningMessage("Your email address is not verified. Some features may be limited.");
                    }

                    // Successful login - reset failed attempts
                    MediEase.DAL.DirectDatabaseAccess.UpdateUserLogin(user.UserId, DateTime.Now, 0, null);

                    // Set user in session and create authentication
                    SecurityHelper.SetCurrentUser(user);
                    SecurityHelper.SignInUser(user, chkRememberMe.Checked);

                    // Log successful login
                    ErrorLogger.LogUserActivity($"User logged in successfully", user.UserId);

                    // Redirect to appropriate page
                    RedirectAfterLogin(user);
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error during login process");
                ShowErrorMessage("An error occurred during login. Please try again.");
            }
        }

        private void RedirectAfterLogin(User user)
        {
            // Check for return URL first
            var returnUrl = ViewState["ReturnUrl"] as string;
            if (!string.IsNullOrEmpty(returnUrl) && IsValidReturnUrl(returnUrl))
            {
                Response.Redirect(returnUrl);
                return;
            }

            // Redirect based on user role
            switch (user.Role.ToLower())
            {
                case "admin":
                    Response.Redirect("~/Admin/Dashboard.aspx");
                    break;
                case "pharmacist":
                    Response.Redirect("~/Pharmacist/Dashboard.aspx");
                    break;
                case "customer":
                    Response.Redirect("~/Customer/Dashboard.aspx");
                    break;
                default:
                    Response.Redirect("~/Default.aspx");
                    break;
            }
        }

        private void RedirectToUserDashboard()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser != null)
            {
                switch (currentUser.Role.ToLower())
                {
                    case "admin":
                        Response.Redirect("~/Admin/Dashboard.aspx");
                        break;
                    case "pharmacist":
                        Response.Redirect("~/Pharmacist/Dashboard.aspx");
                        break;
                    case "customer":
                        Response.Redirect("~/Customer/Dashboard.aspx");
                        break;
                    default:
                        Response.Redirect("~/Default.aspx");
                        break;
                }
            }
        }

        private bool IsValidReturnUrl(string returnUrl)
        {
            // Ensure return URL is relative and safe
            if (string.IsNullOrEmpty(returnUrl))
                return false;

            // Must start with / or ~/
            if (!returnUrl.StartsWith("/") && !returnUrl.StartsWith("~/"))
                return false;

            // Must not contain dangerous patterns
            var dangerousPatterns = new[] { "..", "javascript:", "data:", "vbscript:" };
            var lowerUrl = returnUrl.ToLower();
            
            return !dangerousPatterns.Any(pattern => lowerUrl.Contains(pattern));
        }

        private void LogFailedLoginAttempt(string email, string reason)
        {
            try
            {
                ErrorLogger.LogInfo($"Failed login attempt for email: {email}. Reason: {reason}", "Security");
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            litErrorMessage.Text = SecurityHelper.SanitizeInput(message);
        }

        private void ShowSuccessMessage(string message)
        {
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            litSuccessMessage.Text = SecurityHelper.SanitizeInput(message);
        }

        private void ShowWarningMessage(string message)
        {
            // For warnings, we'll use the success panel with different styling
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            pnlSuccess.CssClass = "alert alert-warning";
            litSuccessMessage.Text = SecurityHelper.SanitizeInput(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Clear password field for security
            txtPassword.Attributes.Add("value", "");
        }
    }
}
