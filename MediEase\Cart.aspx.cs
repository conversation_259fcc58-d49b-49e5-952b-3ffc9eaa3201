using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class Cart : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadCartItems();
                CheckGuestCheckoutVisibility();
            }
        }

        private void LoadCartItems()
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null)
                {
                    // Handle guest cart (session-based)
                    LoadGuestCartItems();
                    return;
                }

                using (var db = new MediEaseContext())
                {
                    var cartItems = db.CartItems
                        .Where(c => c.UserId == currentUser.UserId)
                        .OrderBy(c => c.CreatedDate)
                        .ToList();

                    if (cartItems.Any())
                    {
                        pnlEmptyCart.Visible = false;
                        pnlCartItems.Visible = true;
                        
                        rptCartItems.DataSource = cartItems;
                        rptCartItems.DataBind();
                        
                        CalculateOrderSummary(cartItems);
                    }
                    else
                    {
                        ShowEmptyCart();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading cart items");
                ShowErrorMessage("Error loading cart items. Please try again.");
            }
        }

        private void LoadGuestCartItems()
        {
            // For guest users, cart items are stored in session
            var guestCart = Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
            
            if (guestCart != null && guestCart.Any())
            {
                pnlEmptyCart.Visible = false;
                pnlCartItems.Visible = true;
                
                // Load medicine details for guest cart items
                using (var db = new MediEaseContext())
                {
                    foreach (var item in guestCart)
                    {
                        item.Medicine = db.Medicines.Find(item.MedicineId);
                    }
                }
                
                rptCartItems.DataSource = guestCart;
                rptCartItems.DataBind();
                
                CalculateOrderSummary(guestCart);
            }
            else
            {
                ShowEmptyCart();
            }
        }

        private void ShowEmptyCart()
        {
            pnlEmptyCart.Visible = true;
            pnlCartItems.Visible = false;
            
            lblSubtotal.Text = "0.00";
            lblDiscount.Text = "0.00";
            lblTax.Text = "0.00";
            lblShipping.Text = "0.00";
            lblTotal.Text = "0.00";
        }

        private void CalculateOrderSummary(System.Collections.Generic.IEnumerable<CartItem> cartItems)
        {
            decimal subtotal = cartItems.Sum(c => c.TotalPrice);
            decimal discount = cartItems.Sum(c => c.Medicine.DiscountAmount * c.Quantity);
            decimal tax = subtotal * 0.08m; // 8% tax
            decimal shipping = subtotal > 50 ? 0 : 5.99m; // Free shipping over $50
            
            // Apply coupon discount if any
            var couponDiscount = GetCouponDiscount(subtotal);
            discount += couponDiscount;
            
            decimal total = subtotal - discount + tax + shipping;
            
            lblSubtotal.Text = subtotal.ToString("N2");
            lblDiscount.Text = discount.ToString("N2");
            lblTax.Text = tax.ToString("N2");
            lblShipping.Text = shipping.ToString("N2");
            lblTotal.Text = total.ToString("N2");
        }

        private decimal GetCouponDiscount(decimal subtotal)
        {
            var couponCode = txtCouponCode.Text.Trim();
            if (string.IsNullOrEmpty(couponCode))
                return 0;

            try
            {
                using (var db = new MediEaseContext())
                {
                    var discount = db.Discounts
                        .FirstOrDefault(d => d.CouponCode == couponCode && 
                                           d.IsActive && 
                                           d.StartDate <= DateTime.Now && 
                                           d.EndDate >= DateTime.Now &&
                                           subtotal >= d.MinimumOrderAmount);

                    if (discount != null)
                    {
                        if (discount.Type == "Percentage")
                        {
                            var discountAmount = subtotal * (discount.Value / 100);
                            return discount.MaximumDiscountAmount > 0 ? 
                                Math.Min(discountAmount, discount.MaximumDiscountAmount) : discountAmount;
                        }
                        else if (discount.Type == "FixedAmount")
                        {
                            return discount.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error calculating coupon discount");
            }

            return 0;
        }

        protected void rptCartItems_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "UpdateQuantity")
                {
                    var args = e.CommandArgument.ToString().Split(',');
                    var cartItemId = Convert.ToInt32(args[0]);
                    var change = Convert.ToInt32(args[1]);
                    
                    UpdateCartItemQuantity(cartItemId, change);
                }
                else if (e.CommandName == "RemoveItem")
                {
                    var cartItemId = Convert.ToInt32(e.CommandArgument);
                    RemoveCartItem(cartItemId);
                }
                
                LoadCartItems();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error handling cart item command");
                ShowErrorMessage("Error updating cart. Please try again.");
            }
        }

        private void UpdateCartItemQuantity(int cartItemId, int change)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            
            if (currentUser == null)
            {
                // Handle guest cart
                var guestCart = Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
                if (guestCart != null)
                {
                    var item = guestCart.FirstOrDefault(c => c.CartItemId == cartItemId);
                    if (item != null)
                    {
                        item.Quantity = Math.Max(1, item.Quantity + change);
                        item.TotalPrice = item.UnitPrice * item.Quantity;
                    }
                }
                return;
            }

            using (var db = new MediEaseContext())
            {
                var cartItem = db.CartItems.Find(cartItemId);
                if (cartItem != null && cartItem.UserId == currentUser.UserId)
                {
                    cartItem.Quantity = Math.Max(1, cartItem.Quantity + change);
                    cartItem.TotalPrice = cartItem.UnitPrice * cartItem.Quantity;
                    cartItem.ModifiedDate = DateTime.Now;
                    
                    db.SaveChanges();
                }
            }
        }

        private void RemoveCartItem(int cartItemId)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            
            if (currentUser == null)
            {
                // Handle guest cart
                var guestCart = Session["GuestCart"] as System.Collections.Generic.List<CartItem>;
                if (guestCart != null)
                {
                    var item = guestCart.FirstOrDefault(c => c.CartItemId == cartItemId);
                    if (item != null)
                    {
                        guestCart.Remove(item);
                    }
                }
                return;
            }

            using (var db = new MediEaseContext())
            {
                var cartItem = db.CartItems.Find(cartItemId);
                if (cartItem != null && cartItem.UserId == currentUser.UserId)
                {
                    db.CartItems.Remove(cartItem);
                    db.SaveChanges();
                }
            }
        }

        protected void btnApplyCoupon_Click(object sender, EventArgs e)
        {
            LoadCartItems(); // This will recalculate with the coupon
            ShowSuccessMessage("Coupon applied successfully!");
        }

        protected void btnCheckout_Click(object sender, EventArgs e)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode("~/Checkout.aspx"));
                return;
            }
            
            Response.Redirect("~/Checkout.aspx");
        }

        protected void lnkGuestCheckout_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Checkout.aspx?guest=true");
        }

        protected void lnkContinueShopping_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Medicines.aspx");
        }

        private void CheckGuestCheckoutVisibility()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            pnlGuestCheckout.Visible = (currentUser == null);
        }

        protected string GetMedicineImage(object imagePath)
        {
            var path = imagePath?.ToString();
            return string.IsNullOrEmpty(path) ? "~/Images/medicine-placeholder.jpg" : path;
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }
    }
}
