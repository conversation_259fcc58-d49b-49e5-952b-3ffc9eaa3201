using System;
using System.Web.UI;
using MediEase.Utilities;

namespace MediEase
{
    public partial class About : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SetupUserInterface();
                SetPageMetadata();
            }
        }

        private void SetupUserInterface()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            
            if (currentUser != null)
            {
                // User is logged in
                phGuestCTA.Visible = false;
                phUserCTA.Visible = true;
                
                // Log user activity
                ErrorLogger.LogUserActivity("Visited about page", currentUser.UserId);
            }
            else
            {
                // Guest user
                phGuestCTA.Visible = true;
                phUserCTA.Visible = false;
            }
        }

        private void SetPageMetadata()
        {
            Page.Title = "About Us - MediEase Pharmacy";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Learn about MediEase, the smart pharmacy management system with AI-powered features. Discover our mission, vision, and commitment to revolutionizing healthcare.");
                master.AddMetaKeywords("about MediEase, pharmacy management, AI healthcare, smart pharmacy, mission, vision, technology");
            }
        }
    }
}
