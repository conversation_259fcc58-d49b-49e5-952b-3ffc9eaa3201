using System;
using System.Text;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class TestRegistration : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            
        }

        protected void btnTestDB_Click(object sender, EventArgs e)
        {
            var results = new StringBuilder();
            results.Append("<div class='alert alert-info'><h6>Database Test Results</h6></div>");

            try
            {
                // Test connection
                if (DirectDatabaseAccess.TestConnection())
                {
                    results.Append("<p class='text-success'>✓ Database connection successful</p>");
                    
                    // Get user count
                    var userCount = DirectDatabaseAccess.GetUserCount();
                    results.Append($"<p>Current users in database: {userCount}</p>");
                }
                else
                {
                    results.Append("<p class='text-danger'>✗ Database connection failed</p>");
                }
            }
            catch (Exception ex)
            {
                results.Append($"<p class='text-danger'>✗ Error: {ex.Message}</p>");
            }

            litResults.Text = results.ToString();
        }

        protected void btnTestRegister_Click(object sender, EventArgs e)
        {
            var results = new StringBuilder();
            results.Append("<div class='alert alert-info'><h6>Registration Test Results</h6></div>");

            try
            {
                var email = txtTestEmail.Text.Trim();
                var password = txtTestPassword.Text;

                // Check if user already exists
                if (DirectDatabaseAccess.EmailExists(email))
                {
                    results.Append($"<p class='text-warning'>⚠ User with email {email} already exists</p>");
                }
                else
                {
                    // Create test user
                    var testUser = new User
                    {
                        FirstName = "Test",
                        LastName = "User",
                        Email = email,
                        PasswordHash = SecurityHelper.HashPassword(password),
                        PhoneNumber = "1234567890",
                        Address = "123 Test St",
                        City = "Test City",
                        State = "TS",
                        PostalCode = "12345",
                        Country = "USA",
                        Role = "Customer",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        LoyaltyPoints = 50,
                        IsEmailVerified = false,
                        IsPhoneVerified = false
                    };

                    // Insert user
                    var newUserId = DirectDatabaseAccess.InsertUser(testUser);
                    results.Append($"<p class='text-success'>✓ User created successfully with ID: {newUserId}</p>");
                    results.Append($"<p>Email: {email}</p>");
                    results.Append($"<p>Password: {password}</p>");
                }

                // Update user count
                var userCount = DirectDatabaseAccess.GetUserCount();
                results.Append($"<p>Total users in database: {userCount}</p>");
            }
            catch (Exception ex)
            {
                results.Append($"<p class='text-danger'>✗ Registration failed: {ex.Message}</p>");
                if (ex.InnerException != null)
                {
                    results.Append($"<p class='text-danger'>Inner Exception: {ex.InnerException.Message}</p>");
                }
            }

            litResults.Text = results.ToString();
        }

        protected void btnTestLogin_Click(object sender, EventArgs e)
        {
            var results = new StringBuilder();
            results.Append("<div class='alert alert-info'><h6>Login Test Results</h6></div>");

            try
            {
                var email = txtTestEmail.Text.Trim();
                var password = txtTestPassword.Text;

                // Get user
                var user = DirectDatabaseAccess.GetUserByEmail(email);
                
                if (user == null)
                {
                    results.Append($"<p class='text-danger'>✗ User not found with email: {email}</p>");
                }
                else
                {
                    results.Append($"<p class='text-success'>✓ User found: {user.FirstName} {user.LastName}</p>");
                    results.Append($"<p>User ID: {user.UserId}</p>");
                    results.Append($"<p>Role: {user.Role}</p>");
                    results.Append($"<p>Active: {user.IsActive}</p>");
                    results.Append($"<p>Loyalty Points: {user.LoyaltyPoints}</p>");
                    
                    // Test password verification
                    if (SecurityHelper.VerifyPassword(password, user.PasswordHash))
                    {
                        results.Append("<p class='text-success'>✓ Password verification successful</p>");
                        
                        // Update login time
                        DirectDatabaseAccess.UpdateUserLogin(user.UserId, DateTime.Now, 0, null);
                        results.Append("<p class='text-success'>✓ Login time updated</p>");
                    }
                    else
                    {
                        results.Append("<p class='text-danger'>✗ Password verification failed</p>");
                    }
                }
            }
            catch (Exception ex)
            {
                results.Append($"<p class='text-danger'>✗ Login test failed: {ex.Message}</p>");
                if (ex.InnerException != null)
                {
                    results.Append($"<p class='text-danger'>Inner Exception: {ex.InnerException.Message}</p>");
                }
            }

            litResults.Text = results.ToString();
        }
    }
}
