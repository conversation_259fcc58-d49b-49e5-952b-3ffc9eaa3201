//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase
{
    public partial class ResetPassword
    {
        /// <summary>
        /// pnlSuccess control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlSuccess;

        /// <summary>
        /// pnlError control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlError;

        /// <summary>
        /// lblError control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblError;

        /// <summary>
        /// pnlResetForm control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlResetForm;

        /// <summary>
        /// txtNewPassword control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtNewPassword;

        /// <summary>
        /// rfvNewPassword control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvNewPassword;

        /// <summary>
        /// revNewPassword control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RegularExpressionValidator revNewPassword;

        /// <summary>
        /// txtConfirmPassword control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtConfirmPassword;

        /// <summary>
        /// rfvConfirmPassword control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvConfirmPassword;

        /// <summary>
        /// cvPasswords control.
        /// </summary>
        protected global::System.Web.UI.WebControls.CompareValidator cvPasswords;

        /// <summary>
        /// btnUpdatePassword control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnUpdatePassword;

        /// <summary>
        /// pnlSuccessActions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlSuccessActions;
    }
}
