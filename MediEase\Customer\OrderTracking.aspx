<%@ Page Title="Order Tracking" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="OrderTracking.aspx.cs" Inherits="MediEase.Customer.OrderTracking" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-truck me-2"></i>Order Tracking</h2>
                <p class="text-muted">Track your orders and view delivery status in real-time</p>
            </div>
        </div>

        <!-- Quick Track Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-search me-2"></i>Quick Track</h5>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Order Number</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtOrderNumber" runat="server" CssClass="form-control" 
                                        placeholder="Enter your order number..."></asp:TextBox>
                                    <asp:Button ID="btnTrackOrder" runat="server" CssClass="btn btn-primary" 
                                        Text="Track Order" OnClick="btnTrackOrder_Click" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Filter by Status</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Orders</asp:ListItem>
                                    <asp:ListItem Value="Pending">Pending</asp:ListItem>
                                    <asp:ListItem Value="Processing">Processing</asp:ListItem>
                                    <asp:ListItem Value="Shipped">Shipped</asp:ListItem>
                                    <asp:ListItem Value="OutForDelivery">Out for Delivery</asp:ListItem>
                                    <asp:ListItem Value="Delivered">Delivered</asp:ListItem>
                                    <asp:ListItem Value="Cancelled">Cancelled</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Details Panel -->
        <asp:Panel ID="pnlOrderDetails" runat="server" Visible="false">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm border-primary">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Order #<asp:Label ID="lblOrderNumber" runat="server"></asp:Label></h5>
                                <span class="badge bg-light text-dark fs-6">
                                    <asp:Label ID="lblOrderStatus" runat="server"></asp:Label>
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Order Information</h6>
                                    <p class="mb-1"><strong>Order Date:</strong> <asp:Label ID="lblOrderDate" runat="server"></asp:Label></p>
                                    <p class="mb-1"><strong>Total Amount:</strong> $<asp:Label ID="lblTotalAmount" runat="server"></asp:Label></p>
                                    <p class="mb-1"><strong>Payment Method:</strong> <asp:Label ID="lblPaymentMethod" runat="server"></asp:Label></p>
                                    <p class="mb-1"><strong>Expected Delivery:</strong> <asp:Label ID="lblExpectedDelivery" runat="server"></asp:Label></p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Delivery Address</h6>
                                    <address>
                                        <asp:Label ID="lblDeliveryAddress" runat="server"></asp:Label>
                                    </address>
                                    <p class="mb-1"><strong>Contact:</strong> <asp:Label ID="lblContactNumber" runat="server"></asp:Label></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tracking Timeline -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-route me-2"></i>Tracking Timeline</h5>
                        </div>
                        <div class="card-body">
                            <div class="tracking-timeline">
                                <asp:Repeater ID="rptTrackingTimeline" runat="server">
                                    <ItemTemplate>
                                        <div class="timeline-item <%# GetTimelineItemClass(Eval("Status").ToString(), Eval("IsCompleted")) %>">
                                            <div class="timeline-marker">
                                                <i class="<%# GetStatusIcon(Eval("Status").ToString()) %>"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title"><%# Eval("Title") %></h6>
                                                <p class="timeline-description"><%# Eval("Description") %></p>
                                                <small class="timeline-time">
                                                    <%# Convert.ToBoolean(Eval("IsCompleted")) ? 
                                                        Convert.ToDateTime(Eval("CompletedDate")).ToString("MMM dd, yyyy hh:mm tt") : 
                                                        "Pending" %>
                                                </small>
                                            </div>
                                        </div>
                                    </ItemTemplate>
                                </asp:Repeater>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-pills me-2"></i>Order Items</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <asp:GridView ID="gvOrderItems" runat="server" CssClass="table table-hover" 
                                    AutoGenerateColumns="false" EmptyDataText="No items found.">
                                    <Columns>
                                        <asp:TemplateField HeaderText="Medicine">
                                            <ItemTemplate>
                                                <div class="d-flex align-items-center">
                                                    <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' alt='<%# Eval("MedicineName") %>' 
                                                         class="me-3 rounded" style="width: 50px; height: 50px; object-fit: cover;" />
                                                    <div>
                                                        <h6 class="mb-1"><%# Eval("MedicineName") %></h6>
                                                        <small class="text-muted"><%# Eval("GenericName") %></small>
                                                    </div>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Quantity">
                                            <ItemTemplate>
                                                <span class="fw-bold"><%# Eval("Quantity") %></span>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Unit Price">
                                            <ItemTemplate>
                                                $<%# String.Format("{0:F2}", Eval("UnitPrice")) %>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Total">
                                            <ItemTemplate>
                                                <span class="fw-bold">$<%# String.Format("{0:F2}", Eval("TotalPrice")) %></span>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Status">
                                            <ItemTemplate>
                                                <span class="badge bg-<%# GetItemStatusColor(Eval("Status").ToString()) %>">
                                                    <%# Eval("Status") %>
                                                </span>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <!-- All Orders List -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Your Orders</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="toggleView('recent')">Recent</button>
                            <button type="button" class="btn btn-outline-primary" onclick="toggleView('all')">All Orders</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <asp:GridView ID="gvOrders" runat="server" CssClass="table table-hover" 
                                AutoGenerateColumns="false" EmptyDataText="No orders found." OnRowCommand="gvOrders_RowCommand">
                                <Columns>
                                    <asp:TemplateField HeaderText="Order #">
                                        <ItemTemplate>
                                            <div>
                                                <span class="fw-bold"><%# Eval("OrderNumber") %></span><br>
                                                <small class="text-muted"><%# Convert.ToDateTime(Eval("OrderDate")).ToString("MMM dd, yyyy") %></small>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Items">
                                        <ItemTemplate>
                                            <span class="fw-bold"><%# Eval("ItemCount") %> items</span><br>
                                            <small class="text-muted"><%# Eval("ItemSummary") %></small>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Total">
                                        <ItemTemplate>
                                            <span class="fw-bold fs-6">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Status">
                                        <ItemTemplate>
                                            <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %> fs-6">
                                                <%# Eval("Status") %>
                                            </span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Delivery">
                                        <ItemTemplate>
                                            <%# GetDeliveryInfo(Eval("Status").ToString(), Eval("ExpectedDelivery"), Eval("DeliveredDate")) %>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Actions">
                                        <ItemTemplate>
                                            <div class="btn-group-vertical btn-group-sm">
                                                <asp:LinkButton ID="btnTrackThis" runat="server" CssClass="btn btn-outline-primary btn-sm" 
                                                    CommandName="TrackOrder" CommandArgument='<%# Eval("OrderId") %>'>
                                                    <i class="fas fa-search me-1"></i>Track
                                                </asp:LinkButton>
                                                <%# CanCancelOrder(Eval("Status").ToString()) ? 
                                                    "<button class=\"btn btn-outline-danger btn-sm mt-1 cancel-order\" data-order-id=\"" + Eval("OrderId") + "\">" +
                                                    "<i class=\"fas fa-times me-1\"></i>Cancel</button>" : "" %>
                                                <%# CanReorder(Eval("Status").ToString()) ? 
                                                    "<button class=\"btn btn-outline-success btn-sm mt-1 reorder\" data-order-id=\"" + Eval("OrderId") + "\">" +
                                                    "<i class=\"fas fa-redo me-1\"></i>Reorder</button>" : "" %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Orders Message -->
        <asp:Panel ID="pnlNoOrders" runat="server" Visible="false">
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h4>No orders found</h4>
                <p class="text-muted">You haven't placed any orders yet.</p>
                <a href="/Medicines.aspx" class="btn btn-primary">
                    <i class="fas fa-pills me-2"></i>Start Shopping
                </a>
            </div>
        </asp:Panel>
    </div>

    <style>
        .tracking-timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .tracking-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        
        .timeline-item.completed::before {
            background: #28a745;
        }
        
        .timeline-item.active::before {
            background: #007bff;
        }
        
        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #fff;
            box-shadow: 0 0 0 3px #e9ecef;
        }
        
        .timeline-item.completed .timeline-marker {
            background: #28a745;
            box-shadow: 0 0 0 3px #28a745;
        }
        
        .timeline-item.active .timeline-marker {
            background: #007bff;
            box-shadow: 0 0 0 3px #007bff;
            animation: pulse 2s infinite;
        }
        
        .timeline-marker i {
            color: white;
            font-size: 12px;
        }
        
        .timeline-content {
            padding-left: 20px;
        }
        
        .timeline-title {
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .timeline-description {
            margin-bottom: 5px;
            color: #6c757d;
        }
        
        .timeline-time {
            color: #6c757d;
            font-size: 0.875rem;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 3px #007bff; }
            50% { box-shadow: 0 0 0 6px rgba(0, 123, 255, 0.5); }
            100% { box-shadow: 0 0 0 3px #007bff; }
        }
    </style>

    <script>
        function toggleView(view) {
            // Toggle button states
            $('.btn-group button').removeClass('active');
            $(`button[onclick="toggleView('${view}')"]`).addClass('active');
            
            // Filter orders based on view
            if (view === 'recent') {
                // Show only recent orders (last 30 days)
                filterOrdersByDate(30);
            } else {
                // Show all orders
                showAllOrders();
            }
        }

        function filterOrdersByDate(days) {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            
            $('#<%= gvOrders.ClientID %> tbody tr').each(function() {
                const dateText = $(this).find('td:first small').text();
                const orderDate = new Date(dateText);
                
                if (orderDate >= cutoffDate) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }

        function showAllOrders() {
            $('#<%= gvOrders.ClientID %> tbody tr').show();
        }

        // Cancel order functionality
        $(document).on('click', '.cancel-order', function() {
            const orderId = $(this).data('order-id');
            if (confirm('Are you sure you want to cancel this order?')) {
                cancelOrder(orderId);
            }
        });

        // Reorder functionality
        $(document).on('click', '.reorder', function() {
            const orderId = $(this).data('order-id');
            if (confirm('Add all items from this order to your cart?')) {
                reorderItems(orderId);
            }
        });

        function cancelOrder(orderId) {
            // AJAX call to cancel order
            $.ajax({
                type: 'POST',
                url: 'OrderTracking.aspx/CancelOrder',
                data: JSON.stringify({ orderId: orderId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Order cancelled successfully!');
                        location.reload();
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('An error occurred while cancelling the order.');
                }
            });
        }

        function reorderItems(orderId) {
            // AJAX call to add order items to cart
            $.ajax({
                type: 'POST',
                url: 'OrderTracking.aspx/ReorderItems',
                data: JSON.stringify({ orderId: orderId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Items added to cart successfully!');
                        updateCartCount();
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('An error occurred while adding items to cart.');
                }
            });
        }
    </script>
</asp:Content>
