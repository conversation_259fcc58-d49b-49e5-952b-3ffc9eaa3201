//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Pharmacist
{
    public partial class InventoryManagement
    {
        /// <summary>
        /// lblTotalMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotalMedicines;

        /// <summary>
        /// lblInStock control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblInStock;

        /// <summary>
        /// lblLowStock control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblLowStock;

        /// <summary>
        /// lblOutOfStock control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblOutOfStock;

        /// <summary>
        /// lblExpiredMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblExpiredMedicines;

        /// <summary>
        /// lblExpiringThisMonth control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblExpiringThisMonth;

        /// <summary>
        /// lblExpiringNext3Months control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblExpiringNext3Months;

        /// <summary>
        /// ddlCategoryFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlCategoryFilter;

        /// <summary>
        /// ddlStockFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlStockFilter;

        /// <summary>
        /// ddlExpiryFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlExpiryFilter;

        /// <summary>
        /// txtSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtSearch;

        /// <summary>
        /// btnSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnSearch;

        /// <summary>
        /// btnRefresh control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnRefresh;

        /// <summary>
        /// gvInventory control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvInventory;

        /// <summary>
        /// rptInventoryGrid control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptInventoryGrid;

        /// <summary>
        /// pnlNoMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlNoMedicines;
    }
}
