using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MediEase.Models
{
    [Table("Prescriptions")]
    public class Prescription
    {
        [Key]
        public int PrescriptionId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Prescription Number")]
        public string PrescriptionNumber { get; set; }

        [Required]
        [Display(Name = "Patient")]
        public int PatientId { get; set; }

        [ForeignKey("PatientId")]
        public virtual User Patient { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Doctor Name")]
        public string DoctorName { get; set; }

        [StringLength(100)]
        [Display(Name = "Doctor License")]
        public string DoctorLicense { get; set; }

        [StringLength(200)]
        [Display(Name = "Hospital/Clinic")]
        public string HospitalClinic { get; set; }

        [StringLength(15)]
        [Display(Name = "Doctor Phone")]
        public string DoctorPhone { get; set; }

        [StringLength(100)]
        [Display(Name = "Doctor Email")]
        public string DoctorEmail { get; set; }

        [Required]
        [Display(Name = "Prescription Date")]
        [DataType(DataType.Date)]
        public DateTime PrescriptionDate { get; set; }

        [Display(Name = "Valid Until")]
        [DataType(DataType.Date)]
        public DateTime? ValidUntil { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, Verified, Dispensed, Expired, Rejected

        [StringLength(1000)]
        [Display(Name = "Patient Diagnosis")]
        public string PatientDiagnosis { get; set; }

        [StringLength(1000)]
        [Display(Name = "Patient Symptoms")]
        public string PatientSymptoms { get; set; }

        [StringLength(500)]
        [Display(Name = "Patient Allergies")]
        public string PatientAllergies { get; set; }

        [StringLength(500)]
        [Display(Name = "Current Medications")]
        public string CurrentMedications { get; set; }

        [StringLength(1000)]
        [Display(Name = "Special Instructions")]
        public string SpecialInstructions { get; set; }

        [StringLength(255)]
        [Display(Name = "Prescription Image")]
        public string PrescriptionImage { get; set; }

        [StringLength(255)]
        [Display(Name = "Additional Document")]
        public string AdditionalDocument { get; set; }

        [Display(Name = "Is Emergency")]
        public bool IsEmergency { get; set; } = false;

        [Display(Name = "Requires Consultation")]
        public bool RequiresConsultation { get; set; } = false;

        [Display(Name = "Is Repeat Prescription")]
        public bool IsRepeatPrescription { get; set; } = false;

        [Display(Name = "Original Prescription")]
        public int? OriginalPrescriptionId { get; set; }

        [ForeignKey("OriginalPrescriptionId")]
        public virtual Prescription OriginalPrescription { get; set; }

        [Display(Name = "Repeat Count")]
        public int RepeatCount { get; set; } = 0;

        [Display(Name = "Max Repeats")]
        public int MaxRepeats { get; set; } = 0;

        [Display(Name = "Verified By")]
        public int? VerifiedBy { get; set; }

        [ForeignKey("VerifiedBy")]
        public virtual User VerifiedByUser { get; set; }

        [Display(Name = "Verification Date")]
        public DateTime? VerificationDate { get; set; }

        [StringLength(1000)]
        [Display(Name = "Verification Notes")]
        public string VerificationNotes { get; set; }

        [Display(Name = "Dispensed By")]
        public int? DispensedBy { get; set; }

        [ForeignKey("DispensedBy")]
        public virtual User DispensedByUser { get; set; }

        [Display(Name = "Dispensed Date")]
        public DateTime? DispensedDate { get; set; }

        [StringLength(1000)]
        [Display(Name = "Dispensing Notes")]
        public string DispensingNotes { get; set; }

        [StringLength(1000)]
        [Display(Name = "Rejection Reason")]
        public string RejectionReason { get; set; }

        [Display(Name = "Total Amount")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [Display(Name = "Insurance Coverage")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal InsuranceCoverage { get; set; } = 0;

        [Display(Name = "Patient Copay")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal PatientCopay { get; set; } = 0;

        [StringLength(100)]
        [Display(Name = "Insurance Provider")]
        public string InsuranceProvider { get; set; }

        [StringLength(50)]
        [Display(Name = "Insurance Policy Number")]
        public string InsurancePolicyNumber { get; set; }

        [StringLength(1000)]
        public string Notes { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "Created By")]
        public int? CreatedBy { get; set; }

        [Display(Name = "Modified By")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<PrescriptionItem> PrescriptionItems { get; set; } = new List<PrescriptionItem>();
        public virtual ICollection<Order> Orders { get; set; } = new List<Order>();

        // Computed Properties
        [NotMapped]
        [Display(Name = "Is Valid")]
        public bool IsValid => ValidUntil.HasValue ? ValidUntil.Value >= DateTime.Today : true;

        [NotMapped]
        [Display(Name = "Is Expired")]
        public bool IsExpired => ValidUntil.HasValue && ValidUntil.Value < DateTime.Today;

        [NotMapped]
        [Display(Name = "Days Until Expiry")]
        public int? DaysUntilExpiry
        {
            get
            {
                if (!ValidUntil.HasValue) return null;
                return (int)(ValidUntil.Value - DateTime.Today).TotalDays;
            }
        }

        [NotMapped]
        [Display(Name = "Can Repeat")]
        public bool CanRepeat => IsRepeatPrescription && RepeatCount < MaxRepeats;

        [NotMapped]
        [Display(Name = "Is Verified")]
        public bool IsVerified => Status == "Verified" || Status == "Dispensed";

        [NotMapped]
        [Display(Name = "Is Dispensed")]
        public bool IsDispensed => Status == "Dispensed";
    }

    [Table("PrescriptionItems")]
    public class PrescriptionItem
    {
        [Key]
        public int PrescriptionItemId { get; set; }

        [Required]
        [Display(Name = "Prescription")]
        public int PrescriptionId { get; set; }

        [ForeignKey("PrescriptionId")]
        public virtual Prescription Prescription { get; set; }

        [Display(Name = "Medicine")]
        public int? MedicineId { get; set; }

        [ForeignKey("MedicineId")]
        public virtual Medicine Medicine { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Medicine Name")]
        public string MedicineName { get; set; }

        [StringLength(200)]
        [Display(Name = "Generic Name")]
        public string GenericName { get; set; }

        [StringLength(50)]
        [Display(Name = "Strength")]
        public string Strength { get; set; }

        [StringLength(50)]
        [Display(Name = "Dosage Form")]
        public string DosageForm { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
        public int Quantity { get; set; }

        [StringLength(500)]
        [Display(Name = "Dosage Instructions")]
        public string DosageInstructions { get; set; }

        [StringLength(100)]
        [Display(Name = "Frequency")]
        public string Frequency { get; set; } // Once daily, Twice daily, etc.

        [StringLength(100)]
        [Display(Name = "Duration")]
        public string Duration { get; set; } // 7 days, 2 weeks, etc.

        [StringLength(100)]
        [Display(Name = "Route")]
        public string Route { get; set; } // Oral, Topical, Injection, etc.

        [StringLength(500)]
        [Display(Name = "Special Instructions")]
        public string SpecialInstructions { get; set; }

        [Display(Name = "Is Substitutable")]
        public bool IsSubstitutable { get; set; } = true;

        [Display(Name = "Is Dispensed")]
        public bool IsDispensed { get; set; } = false;

        [Display(Name = "Dispensed Quantity")]
        public int DispensedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Unit Price")]
        public decimal UnitPrice { get; set; } = 0;

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Total Price")]
        public decimal TotalPrice { get; set; } = 0;

        [StringLength(1000)]
        public string Notes { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Computed Properties
        [NotMapped]
        [Display(Name = "Remaining Quantity")]
        public int RemainingQuantity => Quantity - DispensedQuantity;

        [NotMapped]
        [Display(Name = "Is Fully Dispensed")]
        public bool IsFullyDispensed => DispensedQuantity >= Quantity;

        [NotMapped]
        [Display(Name = "Is Partially Dispensed")]
        public bool IsPartiallyDispensed => DispensedQuantity > 0 && DispensedQuantity < Quantity;
    }

    public enum PrescriptionStatus
    {
        Pending,
        UnderReview,
        Verified,
        PartiallyDispensed,
        Dispensed,
        Expired,
        Rejected,
        Cancelled
    }

    public enum DosageFrequency
    {
        OnceDaily,
        TwiceDaily,
        ThreeTimesDaily,
        FourTimesDaily,
        EveryFourHours,
        EverySixHours,
        EveryEightHours,
        EveryTwelveHours,
        AsNeeded,
        BeforeMeals,
        AfterMeals,
        AtBedtime,
        Weekly,
        Monthly
    }

    public enum MedicationRoute
    {
        Oral,
        Topical,
        Injection,
        Intravenous,
        Intramuscular,
        Subcutaneous,
        Inhalation,
        Nasal,
        Rectal,
        Vaginal,
        Ophthalmic,
        Otic
    }
}
