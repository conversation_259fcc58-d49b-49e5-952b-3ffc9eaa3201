<%@ Page Title="AI Medicine Recommendations" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AIRecommendations.aspx.cs" Inherits="MediEase.Customer.AIRecommendations" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-brain me-2"></i>AI Medicine Recommendations</h2>
                <p class="text-muted">Get personalized medicine recommendations based on your symptoms using advanced AI</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Disclaimer:</strong> These recommendations are for informational purposes only. Always consult with a healthcare professional before taking any medication.
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Symptom Input Form -->
            <div class="col-lg-6">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-stethoscope me-2"></i>Describe Your Symptoms</h5>
                    </div>
                    <div class="card-body">
                        <!-- Primary Symptoms -->
                        <div class="mb-3">
                            <label class="form-label">Primary Symptoms *</label>
                            <asp:TextBox ID="txtSymptoms" runat="server" CssClass="form-control" TextMode="MultiLine" 
                                Rows="4" placeholder="Describe your symptoms in detail (e.g., headache, fever, cough, etc.)" required></asp:TextBox>
                            <div class="form-text">Be as specific as possible for better recommendations</div>
                        </div>

                        <!-- Symptom Severity -->
                        <div class="mb-3">
                            <label class="form-label">Symptom Severity</label>
                            <asp:DropDownList ID="ddlSeverity" runat="server" CssClass="form-select">
                                <asp:ListItem Value="Mild">Mild - Manageable discomfort</asp:ListItem>
                                <asp:ListItem Value="Moderate" Selected="True">Moderate - Noticeable impact on daily activities</asp:ListItem>
                                <asp:ListItem Value="Severe">Severe - Significant impact on daily life</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <!-- Duration -->
                        <div class="mb-3">
                            <label class="form-label">How long have you had these symptoms?</label>
                            <asp:DropDownList ID="ddlDuration" runat="server" CssClass="form-select">
                                <asp:ListItem Value="Less than 1 day">Less than 1 day</asp:ListItem>
                                <asp:ListItem Value="1-3 days" Selected="True">1-3 days</asp:ListItem>
                                <asp:ListItem Value="4-7 days">4-7 days</asp:ListItem>
                                <asp:ListItem Value="1-2 weeks">1-2 weeks</asp:ListItem>
                                <asp:ListItem Value="More than 2 weeks">More than 2 weeks</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <!-- Age Group -->
                        <div class="mb-3">
                            <label class="form-label">Age Group</label>
                            <asp:DropDownList ID="ddlAgeGroup" runat="server" CssClass="form-select">
                                <asp:ListItem Value="Child (2-12 years)">Child (2-12 years)</asp:ListItem>
                                <asp:ListItem Value="Teenager (13-17 years)">Teenager (13-17 years)</asp:ListItem>
                                <asp:ListItem Value="Adult (18-64 years)" Selected="True">Adult (18-64 years)</asp:ListItem>
                                <asp:ListItem Value="Senior (65+ years)">Senior (65+ years)</asp:ListItem>
                            </asp:DropDownList>
                        </div>

                        <!-- Known Allergies -->
                        <div class="mb-3">
                            <label class="form-label">Known Allergies</label>
                            <asp:TextBox ID="txtAllergies" runat="server" CssClass="form-control" 
                                placeholder="List any known drug allergies (e.g., Penicillin, Aspirin)"></asp:TextBox>
                        </div>

                        <!-- Current Medications -->
                        <div class="mb-3">
                            <label class="form-label">Current Medications</label>
                            <asp:TextBox ID="txtCurrentMedications" runat="server" CssClass="form-control" 
                                placeholder="List any medications you're currently taking"></asp:TextBox>
                        </div>

                        <!-- Medical Conditions -->
                        <div class="mb-3">
                            <label class="form-label">Existing Medical Conditions</label>
                            <asp:TextBox ID="txtMedicalConditions" runat="server" CssClass="form-control" 
                                placeholder="List any chronic conditions (e.g., diabetes, hypertension)"></asp:TextBox>
                        </div>

                        <!-- Get Recommendations Button -->
                        <div class="d-grid">
                            <asp:Button ID="btnGetRecommendations" runat="server" CssClass="btn btn-primary btn-lg" 
                                Text="Get AI Recommendations" OnClick="btnGetRecommendations_Click" />
                        </div>

                        <!-- Loading Indicator -->
                        <div id="loadingIndicator" class="text-center mt-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">AI is analyzing your symptoms...</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Symptom Categories -->
                <div class="card shadow-sm mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tags me-2"></i>Quick Symptom Categories</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6 col-md-4">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="addSymptom('Headache')">
                                    <i class="fas fa-head-side-cough me-1"></i>Headache
                                </button>
                            </div>
                            <div class="col-6 col-md-4">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="addSymptom('Fever')">
                                    <i class="fas fa-thermometer-half me-1"></i>Fever
                                </button>
                            </div>
                            <div class="col-6 col-md-4">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="addSymptom('Cough')">
                                    <i class="fas fa-lungs me-1"></i>Cough
                                </button>
                            </div>
                            <div class="col-6 col-md-4">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="addSymptom('Stomach pain')">
                                    <i class="fas fa-stomach me-1"></i>Stomach Pain
                                </button>
                            </div>
                            <div class="col-6 col-md-4">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="addSymptom('Sore throat')">
                                    <i class="fas fa-head-side-virus me-1"></i>Sore Throat
                                </button>
                            </div>
                            <div class="col-6 col-md-4">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="addSymptom('Muscle pain')">
                                    <i class="fas fa-dumbbell me-1"></i>Muscle Pain
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations Display -->
            <div class="col-lg-6">
                <asp:Panel ID="pnlRecommendations" runat="server" Visible="false">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-pills me-2"></i>AI Recommendations</h5>
                        </div>
                        <div class="card-body">
                            <asp:Literal ID="litRecommendations" runat="server"></asp:Literal>
                        </div>
                    </div>
                </asp:Panel>

                <!-- Available Medicines -->
                <asp:Panel ID="pnlAvailableMedicines" runat="server" Visible="false">
                    <div class="card shadow-sm mt-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Available Medicines</h6>
                        </div>
                        <div class="card-body">
                            <asp:Repeater ID="rptRecommendedMedicines" runat="server">
                                <ItemTemplate>
                                    <div class="medicine-item border rounded p-3 mb-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <h6 class="mb-1"><%# Eval("Name") %></h6>
                                                <p class="text-muted small mb-1"><%# Eval("GenericName") %></p>
                                                <p class="small mb-2"><%# Eval("Description") %></p>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2"><%# Eval("Category") %></span>
                                                    <span class="text-success fw-bold">$<%# String.Format("{0:F2}", Eval("FinalPrice")) %></span>
                                                </div>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <%# Convert.ToInt32(Eval("StockQuantity")) > 0 ?
                                                    "<button class=\"btn btn-primary btn-sm add-to-cart\" data-medicine-id=\"" + Eval("MedicineId") + "\">" +
                                                    "<i class=\"fas fa-cart-plus me-1\"></i>Add to Cart</button>" :
                                                    "<button class=\"btn btn-secondary btn-sm\" disabled>Out of Stock</button>" %>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </asp:Panel>

                <!-- Emergency Warning -->
                <div class="card shadow-sm border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>When to Seek Immediate Medical Attention</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-circle text-danger me-2" style="font-size: 8px;"></i>Severe chest pain or difficulty breathing</li>
                            <li class="mb-2"><i class="fas fa-circle text-danger me-2" style="font-size: 8px;"></i>High fever (over 103°F/39.4°C)</li>
                            <li class="mb-2"><i class="fas fa-circle text-danger me-2" style="font-size: 8px;"></i>Severe allergic reactions</li>
                            <li class="mb-2"><i class="fas fa-circle text-danger me-2" style="font-size: 8px;"></i>Persistent vomiting or severe dehydration</li>
                            <li><i class="fas fa-circle text-danger me-2" style="font-size: 8px;"></i>Any symptoms that worsen rapidly</li>
                        </ul>
                        <div class="mt-3">
                            <button type="button" class="btn btn-danger btn-sm">
                                <i class="fas fa-phone me-1"></i>Emergency: 911
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Consultation Recommendation -->
                <div class="card shadow-sm mt-3">
                    <div class="card-body text-center">
                        <h6><i class="fas fa-user-md me-2"></i>Need Professional Consultation?</h6>
                        <p class="text-muted small">For personalized medical advice and proper diagnosis</p>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleChatbot()">
                            <i class="fas fa-comments me-1"></i>Chat with AI Assistant
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addSymptom(symptom) {
            const symptomsTextarea = document.getElementById('<%= txtSymptoms.ClientID %>');
            const currentText = symptomsTextarea.value.trim();
            
            if (currentText === '') {
                symptomsTextarea.value = symptom;
            } else if (!currentText.toLowerCase().includes(symptom.toLowerCase())) {
                symptomsTextarea.value = currentText + ', ' + symptom;
            }
            
            symptomsTextarea.focus();
        }

        // Show loading indicator when getting recommendations
        function showLoading() {
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('<%= btnGetRecommendations.ClientID %>').disabled = true;
        }

        // Add to cart functionality
        $(document).on('click', '.add-to-cart', function() {
            const medicineId = $(this).data('medicine-id');
            addToCart(medicineId, 1);
        });

        function addToCart(medicineId, quantity) {
            $.ajax({
                type: 'POST',
                url: '/api/CartHandler.ashx',
                data: JSON.stringify({ 
                    action: 'add',
                    medicineId: medicineId,
                    quantity: quantity 
                }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        showAlert('Medicine added to cart successfully!', 'success');
                        updateCartCount();
                    } else {
                        showAlert(response.message || 'Error adding to cart', 'danger');
                    }
                },
                error: function() {
                    showAlert('Error adding to cart. Please try again.', 'danger');
                }
            });
        }

        // Form submission with loading
        $(document).ready(function() {
            $('form').on('submit', function() {
                if (document.getElementById('<%= btnGetRecommendations.ClientID %>').clicked) {
                    showLoading();
                }
            });
        });
    </script>
</asp:Content>
