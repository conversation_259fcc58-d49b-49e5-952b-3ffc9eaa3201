using System;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Utilities;
using System.Linq;

namespace MediEase
{
    public partial class TestDatabase : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            
        }

        protected void btnTestConnection_Click(object sender, EventArgs e)
        {
            var results = new StringBuilder();
            results.Append("<div class='alert alert-info'><h4>Database Connection Test Results</h4></div>");

            try
            {
                // Test 1: Basic connection string test
                results.Append("<h5>1. Testing Connection String</h5>");
                var connectionString = System.Configuration.ConfigurationManager.ConnectionStrings["MediEaseConnection"].ConnectionString;
                results.Append($"<p><strong>Connection String:</strong> {connectionString}</p>");

                // Test 2: Raw SQL connection test
                results.Append("<h5>2. Testing Raw SQL Connection</h5>");
                try
                {
                    using (var connection = new SqlConnection(connectionString))
                    {
                        connection.Open();
                        results.Append("<p class='text-success'>✓ Raw SQL connection successful</p>");
                        
                        // Test if tables exist
                        var command = new SqlCommand("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'", connection);
                        var reader = command.ExecuteReader();
                        
                        results.Append("<p><strong>Tables found:</strong></p><ul>");
                        while (reader.Read())
                        {
                            results.Append($"<li>{reader["TABLE_NAME"]}</li>");
                        }
                        results.Append("</ul>");
                        reader.Close();
                    }
                }
                catch (Exception ex)
                {
                    results.Append($"<p class='text-danger'>✗ Raw SQL connection failed: {ex.Message}</p>");
                }

                // Test 3: Entity Framework connection test
                results.Append("<h5>3. Testing Entity Framework Connection</h5>");
                try
                {
                    using (var context = new MediEaseContext())
                    {
                        // Test if we can connect to the database
                        var canConnect = context.Database.Exists();
                        results.Append($"<p class='text-success'>✓ Entity Framework database exists: {canConnect}</p>");

                        if (canConnect)
                        {
                            try
                            {
                                // Test if we can query Users table
                                var userCount = context.Users.Count();
                                results.Append($"<p class='text-success'>✓ Users table accessible. Count: {userCount}</p>");
                            }
                            catch (Exception ex)
                            {
                                results.Append($"<p class='text-warning'>⚠ Users table query failed: {ex.Message}</p>");
                            }

                            try
                            {
                                // Test if we can query Medicines table
                                var medicineCount = context.Medicines.Count();
                                results.Append($"<p class='text-success'>✓ Medicines table accessible. Count: {medicineCount}</p>");
                            }
                            catch (Exception ex)
                            {
                                results.Append($"<p class='text-warning'>⚠ Medicines table query failed: {ex.Message}</p>");
                            }

                            try
                            {
                                // Test if we can query Categories table
                                var categoryCount = context.Categories.Count();
                                results.Append($"<p class='text-success'>✓ Categories table accessible. Count: {categoryCount}</p>");
                            }
                            catch (Exception ex)
                            {
                                results.Append($"<p class='text-warning'>⚠ Categories table query failed: {ex.Message}</p>");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.Append($"<p class='text-danger'>✗ Entity Framework connection failed: {ex.Message}</p>");
                    results.Append($"<p><strong>Stack Trace:</strong><br/><pre>{ex.StackTrace}</pre></p>");
                }

                // Test 4: Test creating a simple user
                results.Append("<h5>4. Testing User Creation</h5>");
                try
                {
                    using (var context = new MediEaseContext())
                    {
                        var testUser = new Models.User
                        {
                            FirstName = "Test",
                            LastName = "User",
                            Email = "<EMAIL>",
                            PasswordHash = "testhash",
                            Role = "Customer",
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            LoyaltyPoints = 0
                        };

                        // Don't actually save, just test the creation
                        context.Users.Add(testUser);
                        results.Append("<p class='text-success'>✓ Test user object created successfully</p>");
                        
                        // Try to save
                        context.SaveChanges();
                        results.Append("<p class='text-success'>✓ Test user saved to database successfully</p>");
                        
                        // Remove the test user
                        context.Users.Remove(testUser);
                        context.SaveChanges();
                        results.Append("<p class='text-success'>✓ Test user removed successfully</p>");
                    }
                }
                catch (Exception ex)
                {
                    results.Append($"<p class='text-danger'>✗ User creation test failed: {ex.Message}</p>");
                    results.Append($"<p><strong>Inner Exception:</strong> {ex.InnerException?.Message}</p>");
                }

                results.Append("<div class='alert alert-success mt-3'>Database test completed!</div>");
            }
            catch (Exception ex)
            {
                results.Append($"<div class='alert alert-danger'>Overall test failed: {ex.Message}</div>");
                ErrorLogger.LogError(ex, "Database test failed");
            }

            litResults.Text = results.ToString();
        }
    }
}
