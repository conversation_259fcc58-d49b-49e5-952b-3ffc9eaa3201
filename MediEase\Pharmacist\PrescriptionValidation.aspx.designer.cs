//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Pharmacist
{
    public partial class PrescriptionValidation
    {
        /// <summary>
        /// lblPendingCount control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPendingCount;

        /// <summary>
        /// ddlStatusFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlStatusFilter;

        /// <summary>
        /// ddlPriorityFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlPriorityFilter;

        /// <summary>
        /// txtSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtSearch;

        /// <summary>
        /// btnSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnSearch;

        /// <summary>
        /// btnRefresh control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnRefresh;

        /// <summary>
        /// gvPrescriptions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvPrescriptions;

        /// <summary>
        /// rptPrescriptionsCard control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptPrescriptionsCard;

        /// <summary>
        /// pnlNoPrescriptions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlNoPrescriptions;
    }
}
