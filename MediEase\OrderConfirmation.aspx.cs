using System;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase
{
    public partial class OrderConfirmation : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                var orderIdParam = Request.QueryString["orderId"];
                if (int.TryParse(orderIdParam, out int orderId))
                {
                    LoadOrderDetails(orderId);
                }
                else
                {
                    Response.Redirect("~/Default.aspx");
                }
            }
        }

        private void LoadOrderDetails(int orderId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var order = db.Orders
                        .Include("Customer")
                        .Include("OrderItems.Medicine")
                        .FirstOrDefault(o => o.OrderId == orderId);

                    if (order == null)
                    {
                        Response.Redirect("~/Default.aspx");
                        return;
                    }

                    // Verify user has access to this order
                    var currentUser = SecurityHelper.GetCurrentUser();
                    if (currentUser != null && order.CustomerId != currentUser.UserId)
                    {
                        Response.Redirect("~/Default.aspx");
                        return;
                    }

                    // Populate order details
                    PopulateOrderDetails(order);
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading order confirmation");
                Response.Redirect("~/Default.aspx");
            }
        }

        private void PopulateOrderDetails(Models.Order order)
        {
            // Basic order information
            lblOrderNumber.Text = order.OrderNumber;
            lblOrderDate.Text = order.OrderDate.ToString("MMMM dd, yyyy 'at' hh:mm tt");

            // Customer information
            lblCustomerName.Text = $"{order.Customer.FirstName} {order.Customer.LastName}";
            lblCustomerEmail.Text = order.Customer.Email;
            lblShippingAddress.Text = order.ShippingAddress;

            // Order items
            gvOrderItems.DataSource = order.OrderItems.ToList();
            gvOrderItems.DataBind();

            // Order totals
            lblSubtotal.Text = order.Subtotal.ToString("N2");
            lblDiscount.Text = order.DiscountAmount.ToString("N2");
            lblTax.Text = order.TaxAmount.ToString("N2");
            lblShipping.Text = order.ShippingCost.ToString("N2");
            lblTotal.Text = order.TotalAmount.ToString("N2");

            // Payment information
            lblPaymentMethod.Text = GetPaymentMethodDisplay(order.PaymentMethod);
            lblPaymentStatus.Text = order.PaymentStatus;
            
            // Set payment status badge color
            switch (order.PaymentStatus?.ToLower())
            {
                case "paid":
                    lblPaymentStatus.CssClass = "badge bg-success";
                    break;
                case "pending":
                    lblPaymentStatus.CssClass = "badge bg-warning";
                    break;
                case "failed":
                    lblPaymentStatus.CssClass = "badge bg-danger";
                    break;
                default:
                    lblPaymentStatus.CssClass = "badge bg-secondary";
                    break;
            }

            // Check if prescription is required
            bool requiresPrescription = order.OrderItems.Any(oi => oi.Medicine.PrescriptionRequired);
            pnlPrescriptionNotice.Visible = requiresPrescription;

            // Expected delivery
            if (order.ExpectedDeliveryDate.HasValue)
            {
                lblExpectedDelivery.Text = order.ExpectedDeliveryDate.Value.ToString("MMMM dd, yyyy");
            }
            else
            {
                // Calculate expected delivery (3-5 business days from now)
                var expectedDate = DateTime.Now.AddDays(5);
                lblExpectedDelivery.Text = expectedDate.ToString("MMMM dd, yyyy");
            }
        }

        private string GetPaymentMethodDisplay(string paymentMethod)
        {
            switch (paymentMethod?.ToLower())
            {
                case "creditcard":
                    return "Credit Card";
                case "paypal":
                    return "PayPal";
                case "cashondelivery":
                    return "Cash on Delivery";
                default:
                    return paymentMethod ?? "Unknown";
            }
        }

        protected void lnkContinueShopping_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Medicines.aspx");
        }

        protected void lnkViewOrders_Click(object sender, EventArgs e)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser != null)
            {
                Response.Redirect("~/Customer/Orders.aspx");
            }
            else
            {
                Response.Redirect("~/Login.aspx");
            }
        }
    }
}
