<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
    
    <customErrors defaultRedirect="~/Error.aspx" mode="RemoteOnly" xdt:Transform="Replace" />
  </system.web>
  
  <connectionStrings>
    <add name="MediEaseConnection" 
         connectionString="[Production Connection String]" 
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
  </connectionStrings>
  
  <appSettings>
    <add key="OpenRouterApiKey" value="[Production API Key]" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
  </appSettings>
</configuration>
