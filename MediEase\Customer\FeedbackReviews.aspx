<%@ Page Title="Feedback & Reviews" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="FeedbackReviews.aspx.cs" Inherits="MediEase.Customer.FeedbackReviews" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-star me-2 text-warning"></i>Feedback & Reviews</h2>
                        <p class="text-muted">Share your experience and help others make informed decisions</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#submitFeedbackModal">
                            <i class="fas fa-comment me-2"></i>Submit Feedback
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100 border-primary">
                    <div class="card-body">
                        <i class="fas fa-pills fa-3x text-primary mb-3"></i>
                        <h6>Review Medicine</h6>
                        <p class="small text-muted">Rate medicines you've purchased</p>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#reviewMedicineModal">
                            <i class="fas fa-star me-1"></i>Review
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100 border-success">
                    <div class="card-body">
                        <i class="fas fa-shipping-fast fa-3x text-success mb-3"></i>
                        <h6>Rate Service</h6>
                        <p class="small text-muted">Share your delivery experience</p>
                        <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#rateServiceModal">
                            <i class="fas fa-thumbs-up me-1"></i>Rate
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100 border-warning">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h6>Report Issue</h6>
                        <p class="small text-muted">Report problems or concerns</p>
                        <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#reportIssueModal">
                            <i class="fas fa-flag me-1"></i>Report
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100 border-info">
                    <div class="card-body">
                        <i class="fas fa-lightbulb fa-3x text-info mb-3"></i>
                        <h6>Suggest Feature</h6>
                        <p class="small text-muted">Help us improve our platform</p>
                        <button class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#suggestFeatureModal">
                            <i class="fas fa-plus me-1"></i>Suggest
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Your Reviews -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>Your Reviews</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <asp:Button ID="btnFilterMedicines" runat="server" CssClass="btn btn-outline-primary active" Text="Medicines" OnClick="btnFilterMedicines_Click" />
                            <asp:Button ID="btnFilterService" runat="server" CssClass="btn btn-outline-success" Text="Service" OnClick="btnFilterService_Click" />
                            <asp:Button ID="btnFilterAll" runat="server" CssClass="btn btn-outline-secondary" Text="All" OnClick="btnFilterAll_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptUserReviews" runat="server" OnItemCommand="rptUserReviews_ItemCommand">
                            <ItemTemplate>
                                <div class="review-item border rounded p-3 mb-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="rating me-3">
                                                    <%# GenerateStarRating(Convert.ToInt32(Eval("Rating"))) %>
                                                </div>
                                                <span class="badge bg-<%# GetReviewTypeColor(Eval("ReviewType").ToString()) %>">
                                                    <%# Eval("ReviewType") %>
                                                </span>
                                                <small class="text-muted ms-3">
                                                    <%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd, yyyy") %>
                                                </small>
                                            </div>
                                            <h6 class="text-primary"><%# Eval("Title") %></h6>
                                            <p class="text-muted mb-2"><%# Eval("Comment") %></p>
                                            <%# !string.IsNullOrEmpty(Eval("MedicineName")?.ToString()) ? 
                                                "<small class='text-muted'><strong>Medicine:</strong> " + Eval("MedicineName") + "</small>" : "" %>
                                        </div>
                                        <div class="review-actions">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item edit-review" href="#" data-review-id='<%# Eval("ReviewId") %>'>
                                                        <i class="fas fa-edit me-2"></i>Edit Review</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><asp:LinkButton runat="server" CssClass="dropdown-item text-danger" 
                                                        CommandName="Delete" CommandArgument='<%# Eval("ReviewId") %>'
                                                        OnClientClick="return confirm('Are you sure you want to delete this review?');">
                                                        <i class="fas fa-trash me-2"></i>Delete Review
                                                    </asp:LinkButton></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <%# Convert.ToBoolean(Eval("IsHelpful")) ? 
                                        "<div class='mt-2'><span class='badge bg-success'><i class='fas fa-thumbs-up me-1'></i>Marked as Helpful</span></div>" : "" %>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>

                        <asp:Panel ID="pnlNoReviews" runat="server" Visible="false">
                            <div class="text-center py-5">
                                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                <h4>No reviews yet</h4>
                                <p class="text-muted">Share your experience by writing your first review!</p>
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#reviewMedicineModal">
                                    <i class="fas fa-star me-2"></i>Write Your First Review
                                </button>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>

        <!-- Your Feedback -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-comments me-2"></i>Your Feedback</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <asp:Button ID="btnFilterPending" runat="server" CssClass="btn btn-outline-warning active" Text="Pending" OnClick="btnFilterPending_Click" />
                            <asp:Button ID="btnFilterResolved" runat="server" CssClass="btn btn-outline-success" Text="Resolved" OnClick="btnFilterResolved_Click" />
                            <asp:Button ID="btnFilterAllFeedback" runat="server" CssClass="btn btn-outline-secondary" Text="All" OnClick="btnFilterAllFeedback_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptUserFeedback" runat="server">
                            <ItemTemplate>
                                <div class="feedback-item border rounded p-3 mb-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="badge bg-<%# GetFeedbackTypeColor(Eval("FeedbackType").ToString()) %> me-2">
                                                    <%# Eval("FeedbackType") %>
                                                </span>
                                                <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %> me-2">
                                                    <%# Eval("Status") %>
                                                </span>
                                                <small class="text-muted">
                                                    <%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd, yyyy") %>
                                                </small>
                                            </div>
                                            <h6 class="text-primary"><%# Eval("Subject") %></h6>
                                            <p class="text-muted mb-2"><%# Eval("Message") %></p>
                                            <%# !string.IsNullOrEmpty(Eval("Response")?.ToString()) ? 
                                                "<div class='response-box p-2 bg-light rounded mt-2'>" +
                                                "<small class='text-muted'><strong>Response:</strong></small><br>" +
                                                "<small>" + Eval("Response") + "</small>" +
                                                "</div>" : "" %>
                                        </div>
                                        <div class="feedback-actions">
                                            <%# Eval("Status").ToString() == "Pending" ? 
                                                "<button class='btn btn-sm btn-outline-info follow-up' data-feedback-id='" + Eval("FeedbackId") + "'>" +
                                                "<i class='fas fa-reply me-1'></i>Follow Up</button>" : "" %>
                                        </div>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>

                        <asp:Panel ID="pnlNoFeedback" runat="server" Visible="false">
                            <div class="text-center py-5">
                                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                <h4>No feedback submitted yet</h4>
                                <p class="text-muted">Have a question or suggestion? We'd love to hear from you!</p>
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#submitFeedbackModal">
                                    <i class="fas fa-comment me-2"></i>Submit Your First Feedback
                                </button>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submit Feedback Modal -->
    <div class="modal fade" id="submitFeedbackModal" tabindex="-1" aria-labelledby="submitFeedbackModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="submitFeedbackModalLabel">Submit Feedback</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="feedbackForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Feedback Type *</label>
                                    <asp:DropDownList ID="ddlFeedbackType" runat="server" CssClass="form-select" required>
                                        <asp:ListItem Value="">Select type...</asp:ListItem>
                                        <asp:ListItem Value="General">General Feedback</asp:ListItem>
                                        <asp:ListItem Value="Complaint">Complaint</asp:ListItem>
                                        <asp:ListItem Value="Suggestion">Suggestion</asp:ListItem>
                                        <asp:ListItem Value="Bug">Bug Report</asp:ListItem>
                                        <asp:ListItem Value="Feature">Feature Request</asp:ListItem>
                                        <asp:ListItem Value="Support">Support Request</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <asp:DropDownList ID="ddlPriority" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="Low">Low</asp:ListItem>
                                        <asp:ListItem Value="Medium" Selected="true">Medium</asp:ListItem>
                                        <asp:ListItem Value="High">High</asp:ListItem>
                                        <asp:ListItem Value="Urgent">Urgent</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Subject *</label>
                            <asp:TextBox ID="txtSubject" runat="server" CssClass="form-control" placeholder="Brief description of your feedback" required></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Message *</label>
                            <asp:TextBox ID="txtMessage" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="5" 
                                placeholder="Please provide detailed information about your feedback..." required></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Related Order (Optional)</label>
                            <asp:DropDownList ID="ddlRelatedOrder" runat="server" CssClass="form-select">
                                <asp:ListItem Value="">Select an order if applicable...</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkEmailUpdates" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkEmailUpdates.ClientID %>">
                                    Send me email updates about this feedback
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSubmitFeedback" runat="server" CssClass="btn btn-warning" Text="Submit Feedback" OnClick="btnSubmitFeedback_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Review Medicine Modal -->
    <div class="modal fade" id="reviewMedicineModal" tabindex="-1" aria-labelledby="reviewMedicineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewMedicineModalLabel">Review Medicine</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="reviewForm">
                        <div class="mb-3">
                            <label class="form-label">Select Medicine *</label>
                            <asp:DropDownList ID="ddlReviewMedicine" runat="server" CssClass="form-select" required>
                                <asp:ListItem Value="">Choose a medicine you've purchased...</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Overall Rating *</label>
                            <div class="star-rating" id="starRating">
                                <i class="fas fa-star" data-rating="1"></i>
                                <i class="fas fa-star" data-rating="2"></i>
                                <i class="fas fa-star" data-rating="3"></i>
                                <i class="fas fa-star" data-rating="4"></i>
                                <i class="fas fa-star" data-rating="5"></i>
                            </div>
                            <asp:HiddenField ID="hdnRating" runat="server" />
                            <small class="text-muted">Click on stars to rate</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Review Title *</label>
                            <asp:TextBox ID="txtReviewTitle" runat="server" CssClass="form-control" placeholder="Brief title for your review" required></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Your Review *</label>
                            <asp:TextBox ID="txtReviewComment" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="4" 
                                placeholder="Share your experience with this medicine..." required></asp:TextBox>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Effectiveness</label>
                                    <asp:DropDownList ID="ddlEffectiveness" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="">Not specified</asp:ListItem>
                                        <asp:ListItem Value="Very Effective">Very Effective</asp:ListItem>
                                        <asp:ListItem Value="Effective">Effective</asp:ListItem>
                                        <asp:ListItem Value="Somewhat Effective">Somewhat Effective</asp:ListItem>
                                        <asp:ListItem Value="Not Effective">Not Effective</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Side Effects</label>
                                    <asp:DropDownList ID="ddlSideEffects" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="">Not specified</asp:ListItem>
                                        <asp:ListItem Value="None">No Side Effects</asp:ListItem>
                                        <asp:ListItem Value="Mild">Mild Side Effects</asp:ListItem>
                                        <asp:ListItem Value="Moderate">Moderate Side Effects</asp:ListItem>
                                        <asp:ListItem Value="Severe">Severe Side Effects</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkRecommend" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkRecommend.ClientID %>">
                                    I would recommend this medicine to others
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSubmitReview" runat="server" CssClass="btn btn-primary" Text="Submit Review" OnClick="btnSubmitReview_Click" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .review-item, .feedback-item {
            transition: all 0.3s ease;
        }
        
        .review-item:hover, .feedback-item:hover {
            background-color: #f8f9fa;
        }
        
        .star-rating {
            font-size: 1.5rem;
            color: #ddd;
            cursor: pointer;
        }
        
        .star-rating .fas.active {
            color: #ffc107;
        }
        
        .star-rating .fas:hover,
        .star-rating .fas.hover {
            color: #ffc107;
        }
        
        .response-box {
            border-left: 4px solid #28a745;
        }
        
        .rating .fas {
            color: #ffc107;
        }
    </style>

    <script>
        // Star rating functionality
        $(document).ready(function() {
            $('.star-rating .fas').on('mouseenter', function() {
                const rating = $(this).data('rating');
                highlightStars(rating);
            });
            
            $('.star-rating').on('mouseleave', function() {
                const currentRating = $('#<%= hdnRating.ClientID %>').val();
                highlightStars(currentRating);
            });
            
            $('.star-rating .fas').on('click', function() {
                const rating = $(this).data('rating');
                $('#<%= hdnRating.ClientID %>').val(rating);
                highlightStars(rating);
            });
        });

        function highlightStars(rating) {
            $('.star-rating .fas').each(function(index) {
                if (index < rating) {
                    $(this).addClass('active');
                } else {
                    $(this).removeClass('active');
                }
            });
        }

        // Edit review
        $(document).on('click', '.edit-review', function(e) {
            e.preventDefault();
            const reviewId = $(this).data('review-id');
            loadEditReviewForm(reviewId);
        });

        // Follow up on feedback
        $(document).on('click', '.follow-up', function(e) {
            e.preventDefault();
            const feedbackId = $(this).data('feedback-id');
            showFollowUpForm(feedbackId);
        });

        function loadEditReviewForm(reviewId) {
            $.ajax({
                type: 'POST',
                url: 'FeedbackReviews.aspx/GetReviewForEdit',
                data: JSON.stringify({ reviewId: reviewId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        populateReviewForm(response.d.review);
                        $('#reviewMedicineModalLabel').text('Edit Review');
                        $('#reviewMedicineModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading review data.');
                }
            });
        }

        function populateReviewForm(review) {
            $('#<%= ddlReviewMedicine.ClientID %>').val(review.MedicineId);
            $('#<%= hdnRating.ClientID %>').val(review.Rating);
            highlightStars(review.Rating);
            $('#<%= txtReviewTitle.ClientID %>').val(review.Title);
            $('#<%= txtReviewComment.ClientID %>').val(review.Comment);
            $('#<%= ddlEffectiveness.ClientID %>').val(review.Effectiveness);
            $('#<%= ddlSideEffects.ClientID %>').val(review.SideEffects);
            $('#<%= chkRecommend.ClientID %>').prop('checked', review.WouldRecommend);
        }

        function showFollowUpForm(feedbackId) {
            const followUpMessage = prompt('Enter your follow-up message:');
            if (followUpMessage) {
                $.ajax({
                    type: 'POST',
                    url: 'FeedbackReviews.aspx/SubmitFollowUp',
                    data: JSON.stringify({ feedbackId: feedbackId, message: followUpMessage }),
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    success: function(response) {
                        if (response.d.success) {
                            showSuccessMessage('Follow-up submitted successfully!');
                            location.reload();
                        } else {
                            showErrorMessage(response.d.message);
                        }
                    },
                    error: function() {
                        showErrorMessage('Error submitting follow-up.');
                    }
                });
            }
        }
    </script>
</asp:Content>
