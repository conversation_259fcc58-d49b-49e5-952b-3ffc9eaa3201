<%@ Page Title="Upload Prescription" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="UploadPrescription.aspx.cs" Inherits="MediEase.Customer.UploadPrescription" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-prescription me-2"></i>Upload Prescription</h2>
                <p class="text-muted">Upload your prescription and get medicines delivered to your doorstep</p>
            </div>
        </div>

        <div class="row">
            <!-- Upload Form -->
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Prescription Details</h5>
                    </div>
                    <div class="card-body">
                        <!-- Doctor Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Doctor Name *</label>
                                    <asp:TextBox ID="txtDoctorName" runat="server" CssClass="form-control" placeholder="Dr. John Smith" required></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvDoctorName" runat="server" ControlToValidate="txtDoctorName" 
                                        ErrorMessage="Doctor name is required" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Doctor License Number</label>
                                    <asp:TextBox ID="txtDoctorLicense" runat="server" CssClass="form-control" placeholder="License number"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Hospital/Clinic</label>
                                    <asp:TextBox ID="txtHospitalClinic" runat="server" CssClass="form-control" placeholder="Hospital or clinic name"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Doctor Phone</label>
                                    <asp:TextBox ID="txtDoctorPhone" runat="server" CssClass="form-control" placeholder="(*************"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <!-- Prescription Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prescription Date *</label>
                                    <asp:TextBox ID="txtPrescriptionDate" runat="server" CssClass="form-control" TextMode="Date" required></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvPrescriptionDate" runat="server" ControlToValidate="txtPrescriptionDate" 
                                        ErrorMessage="Prescription date is required" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Valid Until</label>
                                    <asp:TextBox ID="txtValidUntil" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <!-- Patient Information -->
                        <div class="mb-3">
                            <label class="form-label">Patient Diagnosis</label>
                            <asp:TextBox ID="txtPatientDiagnosis" runat="server" CssClass="form-control" TextMode="MultiLine" 
                                Rows="2" placeholder="Brief description of diagnosis"></asp:TextBox>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Symptoms</label>
                            <asp:TextBox ID="txtPatientSymptoms" runat="server" CssClass="form-control" TextMode="MultiLine" 
                                Rows="2" placeholder="Describe symptoms"></asp:TextBox>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Known Allergies</label>
                                    <asp:TextBox ID="txtPatientAllergies" runat="server" CssClass="form-control" 
                                        placeholder="List any allergies"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Current Medications</label>
                                    <asp:TextBox ID="txtCurrentMedications" runat="server" CssClass="form-control" 
                                        placeholder="List current medications"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <!-- Special Instructions -->
                        <div class="mb-3">
                            <label class="form-label">Special Instructions</label>
                            <asp:TextBox ID="txtSpecialInstructions" runat="server" CssClass="form-control" TextMode="MultiLine" 
                                Rows="2" placeholder="Any special instructions from doctor"></asp:TextBox>
                        </div>

                        <!-- Emergency & Repeat Options -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <asp:CheckBox ID="chkIsEmergency" runat="server" CssClass="form-check-input" />
                                    <label class="form-check-label">Emergency Prescription</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <asp:CheckBox ID="chkRequiresConsultation" runat="server" CssClass="form-check-input" />
                                    <label class="form-check-label">Requires Consultation</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <asp:CheckBox ID="chkIsRepeatPrescription" runat="server" CssClass="form-check-input" />
                                    <label class="form-check-label">Repeat Prescription</label>
                                </div>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="mb-3">
                            <label class="form-label">Prescription Image/Document *</label>
                            <div class="upload-area border-2 border-dashed rounded p-4 text-center" id="uploadArea">
                                <asp:FileUpload ID="fuPrescriptionImage" runat="server" CssClass="d-none" 
                                    accept="image/*,.pdf" onchange="handleFileSelect(this)" />
                                <div id="uploadPrompt">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <p class="mb-2">Click to upload or drag and drop</p>
                                    <p class="text-muted small">Supports: JPG, PNG, PDF (Max 5MB)</p>
                                    <button type="button" class="btn btn-outline-primary" onclick="$('#<%= fuPrescriptionImage.ClientID %>').click()">
                                        Choose File
                                    </button>
                                </div>
                                <div id="filePreview" style="display: none;">
                                    <img id="imagePreview" src="" alt="Preview" class="img-fluid mb-2" style="max-height: 200px;" />
                                    <p id="fileName" class="mb-2"></p>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()">
                                        <i class="fas fa-times me-1"></i>Remove
                                    </button>
                                </div>
                            </div>
                            <asp:RequiredFieldValidator ID="rfvPrescriptionImage" runat="server" ControlToValidate="fuPrescriptionImage" 
                                ErrorMessage="Please upload prescription image or document" CssClass="text-danger" Display="Dynamic"></asp:RequiredFieldValidator>
                        </div>

                        <!-- Additional Document -->
                        <div class="mb-3">
                            <label class="form-label">Additional Document (Optional)</label>
                            <asp:FileUpload ID="fuAdditionalDocument" runat="server" CssClass="form-control" accept="image/*,.pdf" />
                            <div class="form-text">Upload any additional documents related to this prescription</div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label class="form-label">Additional Notes</label>
                            <asp:TextBox ID="txtNotes" runat="server" CssClass="form-control" TextMode="MultiLine" 
                                Rows="3" placeholder="Any additional information or requests"></asp:TextBox>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <asp:Button ID="btnSubmitPrescription" runat="server" CssClass="btn btn-primary btn-lg" 
                                Text="Submit Prescription" OnClick="btnSubmitPrescription_Click" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Information Sidebar -->
            <div class="col-lg-4">
                <!-- Process Information -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <div class="step-item mb-3">
                            <div class="d-flex">
                                <div class="step-number bg-primary text-white rounded-circle me-3">1</div>
                                <div>
                                    <h6>Upload Prescription</h6>
                                    <p class="text-muted small">Upload clear image or PDF of your prescription</p>
                                </div>
                            </div>
                        </div>
                        <div class="step-item mb-3">
                            <div class="d-flex">
                                <div class="step-number bg-primary text-white rounded-circle me-3">2</div>
                                <div>
                                    <h6>Pharmacist Review</h6>
                                    <p class="text-muted small">Our licensed pharmacist will verify your prescription</p>
                                </div>
                            </div>
                        </div>
                        <div class="step-item mb-3">
                            <div class="d-flex">
                                <div class="step-number bg-primary text-white rounded-circle me-3">3</div>
                                <div>
                                    <h6>Order Confirmation</h6>
                                    <p class="text-muted small">You'll receive confirmation with pricing and delivery details</p>
                                </div>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="d-flex">
                                <div class="step-number bg-primary text-white rounded-circle me-3">4</div>
                                <div>
                                    <h6>Home Delivery</h6>
                                    <p class="text-muted small">Medicines delivered safely to your doorstep</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Important Notes -->
                <div class="card shadow-sm mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Prescription must be valid and not expired</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Image should be clear and readable</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Doctor's signature must be visible</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>All patient details should match your profile</li>
                            <li><i class="fas fa-check text-success me-2"></i>Processing time: 2-4 hours during business hours</li>
                        </ul>
                    </div>
                </div>

                <!-- Contact Support -->
                <div class="card shadow-sm mt-3">
                    <div class="card-body text-center">
                        <h6><i class="fas fa-headset me-2"></i>Need Help?</h6>
                        <p class="text-muted small">Our support team is here to assist you</p>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleChatbot()">
                            <i class="fas fa-comments me-1"></i>Chat with AI Assistant
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .step-number {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .upload-area {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #007bff !important;
            background-color: #f8f9fa;
        }

        .upload-area.dragover {
            border-color: #007bff !important;
            background-color: #e3f2fd;
        }
    </style>

    <script>
        function handleFileSelect(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                
                // Check file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    clearFile();
                    return;
                }
                
                $('#uploadPrompt').hide();
                $('#filePreview').show();
                $('#fileName').text(`${fileName} (${fileSize} MB)`);
                
                // Show preview for images
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#imagePreview').attr('src', e.target.result).show();
                    };
                    reader.readAsDataURL(file);
                } else {
                    $('#imagePreview').hide();
                }
            }
        }

        function clearFile() {
            $('#<%= fuPrescriptionImage.ClientID %>').val('');
            $('#uploadPrompt').show();
            $('#filePreview').hide();
            $('#imagePreview').attr('src', '');
        }

        // Drag and drop functionality
        $(document).ready(function() {
            const uploadArea = document.getElementById('uploadArea');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const fileInput = document.getElementById('<%= fuPrescriptionImage.ClientID %>');
                    fileInput.files = files;
                    handleFileSelect(fileInput);
                }
            });
            
            // Click to upload
            uploadArea.addEventListener('click', function() {
                $('#<%= fuPrescriptionImage.ClientID %>').click();
            });
        });
    </script>
</asp:Content>
