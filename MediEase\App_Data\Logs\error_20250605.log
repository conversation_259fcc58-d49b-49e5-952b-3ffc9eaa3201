[2025-06-05 18:47:03] ERROR: Error loading featured medicines - Invalid column name 'Unit'.
Invalid column name 'DiscountAmount'.
Invalid column name 'ImagePath'.
Invalid column name 'DiscountAmount'.
Invalid column name 'DiscountAmount'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader()
   at MediEase._Default.LoadFeaturedMedicines() in C:\Users\<USER>\Desktop\Project\MediEase\Default.aspx.cs:line 105
[2025-06-05 18:47:56] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 18:48:57] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 18:58:00] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 18:58:20] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 18:59:51] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 19:00:14] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 19:05:08] ERROR: Error loading featured medicines - Invalid column name 'Unit'.
Invalid column name 'DiscountAmount'.
Invalid column name 'ImagePath'.
Invalid column name 'DiscountAmount'.
Invalid column name 'DiscountAmount'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader()
   at MediEase._Default.LoadFeaturedMedicines() in C:\Users\<USER>\Desktop\Project\MediEase\Default.aspx.cs:line 105
[2025-06-05 19:06:33] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 19:12:08] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 19:12:30] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 19:12:36] ERROR: Error during user registration - Could not load file or assembly 'System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51' or one of its dependencies. The system cannot find the file specified.
   at BCrypt.Net.BCrypt.HashPassword(String inputKey, String salt, Boolean enhancedEntropy, HashType hashType)
   at MediEase.Register.HashPassword(String password) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 61
   at MediEase.Register.btnRegister_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 189
[2025-06-05 19:18:41] ERROR: Error inserting user: <EMAIL> - Invalid column name 'IsPhoneVerified'.
Invalid column name 'TwoFactorEnabled'.
Invalid column name 'NotificationPreferences'.
Invalid column name 'BloodType'.
Invalid column name 'Allergies'.
Invalid column name 'CurrentMedications'.
Invalid column name 'EmailNotifications'.
Invalid column name 'SMSNotifications'.
Invalid column name 'NewsletterSubscription'.
Invalid column name 'AutoRefillReminders'.
Invalid column name 'FailedLoginAttempts'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteScalar()
   at MediEase.Register.InsertUser(User user) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 357
[2025-06-05 19:18:41] ERROR: Registration failed: Invalid column name 'IsPhoneVerified'.
Invalid column name 'TwoFactorEnabled'.
Invalid column name 'NotificationPreferences'.
Invalid column name 'BloodType'.
Invalid column name 'Allergies'.
Invalid column name 'CurrentMedications'.
Invalid column name 'EmailNotifications'.
Invalid column name 'SMSNotifications'.
Invalid column name 'NewsletterSubscription'.
Invalid column name 'AutoRefillReminders'.
Invalid column name 'FailedLoginAttempts'.
[2025-06-05 19:19:31] ERROR: Error inserting user: <EMAIL> - Invalid column name 'IsPhoneVerified'.
Invalid column name 'TwoFactorEnabled'.
Invalid column name 'NotificationPreferences'.
Invalid column name 'BloodType'.
Invalid column name 'Allergies'.
Invalid column name 'CurrentMedications'.
Invalid column name 'EmailNotifications'.
Invalid column name 'SMSNotifications'.
Invalid column name 'NewsletterSubscription'.
Invalid column name 'AutoRefillReminders'.
Invalid column name 'FailedLoginAttempts'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteScalar()
   at MediEase.Register.InsertUser(User user) in C:\Users\<USER>\Desktop\Project\MediEase\Register.aspx.cs:line 357
[2025-06-05 19:19:31] ERROR: Registration failed: Invalid column name 'IsPhoneVerified'.
Invalid column name 'TwoFactorEnabled'.
Invalid column name 'NotificationPreferences'.
Invalid column name 'BloodType'.
Invalid column name 'Allergies'.
Invalid column name 'CurrentMedications'.
Invalid column name 'EmailNotifications'.
Invalid column name 'SMSNotifications'.
Invalid column name 'NewsletterSubscription'.
Invalid column name 'AutoRefillReminders'.
Invalid column name 'FailedLoginAttempts'.
