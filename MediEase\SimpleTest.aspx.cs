using System;
using System.Data.SqlClient;
using System.Web.UI;

namespace MediEase
{
    public partial class SimpleTest : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            
        }

        protected void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                var connectionString = System.Configuration.ConfigurationManager.ConnectionStrings["MediEaseConnection"].ConnectionString;
                lblResult.Text = $"Connection String: {connectionString}<br/><br/>";

                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    lblResult.Text += "✓ Database connection successful!<br/>";
                    
                    // Test if Users table exists and has data
                    var command = new SqlCommand("SELECT COUNT(*) FROM Users", connection);
                    var userCount = (int)command.ExecuteScalar();
                    lblResult.Text += $"✓ Users table found. Count: {userCount}<br/>";
                    
                    // Test if we can insert a test user
                    var insertCommand = new SqlCommand(@"
                        INSERT INTO Users (FirstName, LastName, Email, PasswordHash, Role, IsActive, CreatedDate, LoyaltyPoints)
                        VALUES (@FirstName, @LastName, @Email, @PasswordHash, @Role, @IsActive, @CreatedDate, @LoyaltyPoints);
                        SELECT SCOPE_IDENTITY();", connection);
                    
                    insertCommand.Parameters.AddWithValue("@FirstName", "Test");
                    insertCommand.Parameters.AddWithValue("@LastName", "User");
                    insertCommand.Parameters.AddWithValue("@Email", "test" + DateTime.Now.Ticks + "@test.com");
                    insertCommand.Parameters.AddWithValue("@PasswordHash", "testhash");
                    insertCommand.Parameters.AddWithValue("@Role", "Customer");
                    insertCommand.Parameters.AddWithValue("@IsActive", true);
                    insertCommand.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    insertCommand.Parameters.AddWithValue("@LoyaltyPoints", 50);
                    
                    var newUserId = insertCommand.ExecuteScalar();
                    lblResult.Text += $"✓ Test user created with ID: {newUserId}<br/>";
                    
                    // Clean up - delete the test user
                    var deleteCommand = new SqlCommand("DELETE FROM Users WHERE UserId = @UserId", connection);
                    deleteCommand.Parameters.AddWithValue("@UserId", newUserId);
                    deleteCommand.ExecuteNonQuery();
                    lblResult.Text += "✓ Test user deleted successfully<br/>";
                    
                    lblResult.CssClass = "alert alert-success d-block";
                    lblResult.Text += "<br/><strong>All tests passed! Database is working correctly.</strong>";
                }
            }
            catch (Exception ex)
            {
                lblResult.CssClass = "alert alert-danger d-block";
                lblResult.Text = $"❌ Error: {ex.Message}<br/><br/>";
                
                if (ex.InnerException != null)
                {
                    lblResult.Text += $"Inner Exception: {ex.InnerException.Message}<br/>";
                }
                
                lblResult.Text += $"<br/>Stack Trace:<br/><pre>{ex.StackTrace}</pre>";
            }
        }
    }
}
