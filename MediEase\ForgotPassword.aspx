<%@ Page Title="Forgot Password" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ForgotPassword.aspx.cs" Inherits="MediEase.ForgotPassword" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </h3>
                        <p class="mb-0 mt-2">Enter your email to reset your password</p>
                    </div>
                    <div class="card-body p-4">
                        <!-- Success Panel -->
                        <asp:Panel ID="pnlSuccess" runat="server" CssClass="alert alert-success" Visible="false">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Email Sent!</strong> 
                            <p class="mb-0 mt-2">We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.</p>
                            <p class="mb-0 mt-2"><small>If you don't see the email, please check your spam folder.</small></p>
                        </asp:Panel>

                        <!-- Error Panel -->
                        <asp:Panel ID="pnlError" runat="server" CssClass="alert alert-danger" Visible="false">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <asp:Label ID="lblError" runat="server"></asp:Label>
                        </asp:Panel>

                        <!-- Reset Form -->
                        <asp:Panel ID="pnlResetForm" runat="server">
                            <div class="mb-4">
                                <label for="txtEmail" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" 
                                        TextMode="Email" placeholder="Enter your registered email address" Required="true"></asp:TextBox>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail" 
                                    ErrorMessage="Email address is required" CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail"
                                    ValidationExpression="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                                    ErrorMessage="Please enter a valid email address" CssClass="text-danger small" Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>

                            <div class="d-grid mb-3">
                                <asp:Button ID="btnResetPassword" runat="server" CssClass="btn btn-primary btn-lg" 
                                    Text="Send Reset Link" OnClick="btnResetPassword_Click" />
                            </div>

                            <div class="text-center">
                                <p class="text-muted mb-2">Remember your password?</p>
                                <a href="~/Login.aspx" runat="server" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Login
                                </a>
                            </div>
                        </asp:Panel>

                        <!-- Reset Success Actions -->
                        <asp:Panel ID="pnlSuccessActions" runat="server" Visible="false">
                            <div class="text-center">
                                <div class="mb-3">
                                    <i class="fas fa-envelope-open-text fa-3x text-success"></i>
                                </div>
                                <p class="text-muted mb-3">Check your email for the reset link</p>
                                <div class="d-grid gap-2">
                                    <a href="~/Login.aspx" runat="server" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                                    </a>
                                    <asp:Button ID="btnResendEmail" runat="server" CssClass="btn btn-outline-secondary" 
                                        Text="Resend Email" OnClick="btnResendEmail_Click" />
                                </div>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="card mt-4 border-0">
                    <div class="card-body text-center">
                        <h6 class="text-muted">Need Help?</h6>
                        <p class="small text-muted mb-3">
                            If you're having trouble resetting your password, our support team is here to help.
                        </p>
                        <div class="d-flex justify-content-center gap-2 flex-wrap">
                            <a href="~/Contact.aspx" runat="server" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-envelope me-1"></i>Contact Support
                            </a>
                            <a href="tel:+1234567890" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-phone me-1"></i>Call Us
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
