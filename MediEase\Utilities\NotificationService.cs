using System;
using System.Configuration;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web;
using MediEase.DAL;
using MediEase.Models;

namespace MediEase.Utilities
{
    public static class NotificationService
    {
        private static readonly string SmtpServer = ConfigurationManager.AppSettings["SmtpServer"] ?? "smtp.gmail.com";
        private static readonly string SmtpPort = ConfigurationManager.AppSettings["SmtpPort"] ?? "587";
        private static readonly string SmtpUsername = ConfigurationManager.AppSettings["SmtpUsername"] ?? "";
        private static readonly string SmtpPassword = ConfigurationManager.AppSettings["SmtpPassword"] ?? "";
        private static readonly string FromEmail = ConfigurationManager.AppSettings["FromEmail"] ?? "<EMAIL>";
        private static readonly string FromName = ConfigurationManager.AppSettings["FromName"] ?? "MediEase";
        
        // SMS Configuration
        private static readonly string SmsApiKey = ConfigurationManager.AppSettings["SmsApiKey"] ?? "";
        private static readonly string SmsApiUrl = ConfigurationManager.AppSettings["SmsApiUrl"] ?? "";
        private static readonly string SmsFromNumber = ConfigurationManager.AppSettings["SmsFromNumber"] ?? "";

        #region Email Notifications

        public static async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            try
            {
                using (var client = new SmtpClient(SmtpServer, int.Parse(SmtpPort)))
                {
                    client.EnableSsl = true;
                    client.UseDefaultCredentials = false;
                    client.Credentials = new NetworkCredential(SmtpUsername, SmtpPassword);

                    var message = new MailMessage
                    {
                        From = new MailAddress(FromEmail, FromName),
                        Subject = subject,
                        Body = body,
                        IsBodyHtml = isHtml
                    };

                    message.To.Add(toEmail);

                    await client.SendMailAsync(message);
                    
                    // Log successful email
                    ErrorLogger.LogInfo($"Email sent successfully to {toEmail}", "NotificationService");
                    return true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, $"Failed to send email to {toEmail}");
                return false;
            }
        }

        public static async Task SendWelcomeEmailAsync(User user)
        {
            var subject = "Welcome to MediEase - Your Health Partner";
            var body = GenerateWelcomeEmailTemplate(user);
            await SendEmailAsync(user.Email, subject, body);
            
            // Create notification record
            await CreateNotificationAsync(user.UserId, "Welcome", "Welcome to MediEase! Your account has been created successfully.", "Success");
        }

        public static async Task SendOrderConfirmationEmailAsync(User user, Order order)
        {
            var subject = $"Order Confirmation - {order.OrderNumber}";
            var body = GenerateOrderConfirmationTemplate(user, order);
            await SendEmailAsync(user.Email, subject, body);
            
            await CreateNotificationAsync(user.UserId, "Order Confirmation", 
                $"Your order {order.OrderNumber} has been confirmed and is being processed.", "Info");
        }

        public static async Task SendOrderStatusUpdateEmailAsync(User user, Order order, string oldStatus, string newStatus)
        {
            var subject = $"Order Update - {order.OrderNumber}";
            var body = GenerateOrderStatusUpdateTemplate(user, order, oldStatus, newStatus);
            await SendEmailAsync(user.Email, subject, body);
            
            await CreateNotificationAsync(user.UserId, "Order Status Update", 
                $"Your order {order.OrderNumber} status has been updated to {newStatus}.", "Info");
        }

        public static async Task SendPrescriptionStatusEmailAsync(User user, Prescription prescription)
        {
            var subject = $"Prescription Update - {prescription.PrescriptionNumber}";
            var body = GeneratePrescriptionStatusTemplate(user, prescription);
            await SendEmailAsync(user.Email, subject, body);
            
            await CreateNotificationAsync(user.UserId, "Prescription Update", 
                $"Your prescription {prescription.PrescriptionNumber} has been {prescription.Status.ToLower()}.", "Info");
        }

        public static async Task SendHealthReminderEmailAsync(User user, HealthReminder reminder)
        {
            var subject = $"Health Reminder: {reminder.Title}";
            var body = GenerateHealthReminderTemplate(user, reminder);
            await SendEmailAsync(user.Email, subject, body);
        }

        public static async Task SendAutoRefillNotificationAsync(User user, AutoRefill autoRefill)
        {
            var subject = "Auto-Refill Notification";
            var body = GenerateAutoRefillTemplate(user, autoRefill);
            await SendEmailAsync(user.Email, subject, body);
            
            await CreateNotificationAsync(user.UserId, "Auto-Refill", 
                $"Your auto-refill for {autoRefill.Medicine.Name} will be processed in 3 days.", "Info");
        }

        public static async Task SendLowStockAlertAsync(string pharmacistEmail, Medicine medicine)
        {
            var subject = $"Low Stock Alert - {medicine.Name}";
            var body = GenerateLowStockAlertTemplate(medicine);
            await SendEmailAsync(pharmacistEmail, subject, body);
        }

        #endregion

        #region SMS Notifications

        public static async Task<bool> SendSmsAsync(string phoneNumber, string message)
        {
            try
            {
                if (string.IsNullOrEmpty(SmsApiKey) || string.IsNullOrEmpty(phoneNumber))
                {
                    ErrorLogger.LogInfo("SMS service not configured or phone number missing", "NotificationService");
                    return false;
                }

                // Clean phone number
                phoneNumber = CleanPhoneNumber(phoneNumber);

                using (var client = new WebClient())
                {
                    client.Headers.Add("Authorization", $"Bearer {SmsApiKey}");
                    client.Headers.Add("Content-Type", "application/json");

                    var payload = new
                    {
                        to = phoneNumber,
                        from = SmsFromNumber,
                        body = message
                    };

                    var jsonPayload = Newtonsoft.Json.JsonConvert.SerializeObject(payload);
                    await client.UploadStringTaskAsync(SmsApiUrl, "POST", jsonPayload);

                    ErrorLogger.LogInfo($"SMS sent successfully to {phoneNumber}", "NotificationService");
                    return true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, $"Failed to send SMS to {phoneNumber}");
                return false;
            }
        }

        public static async Task SendOrderStatusSmsAsync(User user, Order order)
        {
            if (!string.IsNullOrEmpty(user.PhoneNumber))
            {
                var message = $"MediEase: Your order {order.OrderNumber} status: {order.Status}. Track at mediease.com/track";
                await SendSmsAsync(user.PhoneNumber, message);
            }
        }

        public static async Task SendHealthReminderSmsAsync(User user, HealthReminder reminder)
        {
            if (!string.IsNullOrEmpty(user.PhoneNumber))
            {
                var message = $"MediEase Reminder: {reminder.Title} - {reminder.Description}";
                await SendSmsAsync(user.PhoneNumber, message);
            }
        }

        public static async Task SendAutoRefillSmsAsync(User user, AutoRefill autoRefill)
        {
            if (!string.IsNullOrEmpty(user.PhoneNumber))
            {
                var message = $"MediEase: Auto-refill for {autoRefill.Medicine.Name} will be processed in 3 days. Reply STOP to cancel.";
                await SendSmsAsync(user.PhoneNumber, message);
            }
        }

        #endregion

        #region Push Notifications

        public static async Task CreateNotificationAsync(int userId, string title, string message, string type = "Info", string actionUrl = null)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var notification = new Notification
                    {
                        UserId = userId,
                        Title = title,
                        Message = message,
                        Type = type,
                        ActionUrl = actionUrl,
                        IsRead = false,
                        CreatedDate = DateTime.Now
                    };

                    db.Notifications.Add(notification);
                    await db.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error creating notification");
            }
        }

        public static async Task MarkNotificationAsReadAsync(int notificationId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var notification = await db.Notifications.FindAsync(notificationId);
                    if (notification != null && !notification.IsRead)
                    {
                        notification.IsRead = true;
                        notification.ReadDate = DateTime.Now;
                        await db.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error marking notification as read");
            }
        }

        #endregion

        #region Email Templates

        private static string GenerateWelcomeEmailTemplate(User user)
        {
            return $@"
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background: #007bff; color: white; padding: 20px; text-align: center; }}
                        .content {{ padding: 20px; background: #f9f9f9; }}
                        .footer {{ padding: 20px; text-align: center; color: #666; }}
                        .btn {{ background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>Welcome to MediEase!</h1>
                        </div>
                        <div class='content'>
                            <h2>Hello {user.FirstName},</h2>
                            <p>Welcome to MediEase - Your trusted health partner! We're excited to have you join our community.</p>
                            <p>With MediEase, you can:</p>
                            <ul>
                                <li>Browse and order medicines online</li>
                                <li>Upload prescriptions for verification</li>
                                <li>Get AI-powered health recommendations</li>
                                <li>Track your orders in real-time</li>
                                <li>Manage your family's health profiles</li>
                                <li>Set up auto-refills for regular medications</li>
                            </ul>
                            <p style='text-align: center; margin: 30px 0;'>
                                <a href='https://mediease.com/dashboard' class='btn'>Get Started</a>
                            </p>
                        </div>
                        <div class='footer'>
                            <p>Thank you for choosing MediEase!</p>
                            <p>If you have any questions, contact <NAME_EMAIL></p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GenerateOrderConfirmationTemplate(User user, Order order)
        {
            return $@"
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background: #28a745; color: white; padding: 20px; text-align: center; }}
                        .content {{ padding: 20px; }}
                        .order-details {{ background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                        .footer {{ padding: 20px; text-align: center; color: #666; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>Order Confirmed!</h1>
                        </div>
                        <div class='content'>
                            <h2>Hello {user.FirstName},</h2>
                            <p>Thank you for your order! We've received your order and it's being processed.</p>
                            <div class='order-details'>
                                <h3>Order Details:</h3>
                                <p><strong>Order Number:</strong> {order.OrderNumber}</p>
                                <p><strong>Order Date:</strong> {order.OrderDate:MMM dd, yyyy}</p>
                                <p><strong>Total Amount:</strong> ${order.TotalAmount:F2}</p>
                                <p><strong>Payment Method:</strong> {order.PaymentMethod}</p>
                                <p><strong>Delivery Address:</strong> {order.ShippingAddress}</p>
                            </div>
                            <p>You can track your order status anytime by visiting your dashboard.</p>
                        </div>
                        <div class='footer'>
                            <p>Thank you for choosing MediEase!</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GenerateOrderStatusUpdateTemplate(User user, Order order, string oldStatus, string newStatus)
        {
            return $@"
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background: #17a2b8; color: white; padding: 20px; text-align: center; }}
                        .content {{ padding: 20px; }}
                        .status-update {{ background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>Order Status Update</h1>
                        </div>
                        <div class='content'>
                            <h2>Hello {user.FirstName},</h2>
                            <p>Your order status has been updated!</p>
                            <div class='status-update'>
                                <h3>Order {order.OrderNumber}</h3>
                                <p><strong>Previous Status:</strong> {oldStatus}</p>
                                <p><strong>Current Status:</strong> {newStatus}</p>
                                <p><strong>Updated:</strong> {DateTime.Now:MMM dd, yyyy hh:mm tt}</p>
                            </div>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GeneratePrescriptionStatusTemplate(User user, Prescription prescription)
        {
            return $@"
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background: #6f42c1; color: white; padding: 20px; text-align: center; }}
                        .content {{ padding: 20px; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>Prescription Update</h1>
                        </div>
                        <div class='content'>
                            <h2>Hello {user.FirstName},</h2>
                            <p>Your prescription has been updated:</p>
                            <p><strong>Prescription Number:</strong> {prescription.PrescriptionNumber}</p>
                            <p><strong>Status:</strong> {prescription.Status}</p>
                            <p><strong>Doctor:</strong> {prescription.DoctorName}</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GenerateHealthReminderTemplate(User user, HealthReminder reminder)
        {
            return $@"
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background: #fd7e14; color: white; padding: 20px; text-align: center; }}
                        .content {{ padding: 20px; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>Health Reminder</h1>
                        </div>
                        <div class='content'>
                            <h2>Hello {user.FirstName},</h2>
                            <p>This is a reminder for your health task:</p>
                            <h3>{reminder.Title}</h3>
                            <p>{reminder.Description}</p>
                            <p><strong>Time:</strong> {reminder.ReminderTime}</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GenerateAutoRefillTemplate(User user, AutoRefill autoRefill)
        {
            return $@"
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background: #20c997; color: white; padding: 20px; text-align: center; }}
                        .content {{ padding: 20px; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>Auto-Refill Notification</h1>
                        </div>
                        <div class='content'>
                            <h2>Hello {user.FirstName},</h2>
                            <p>Your auto-refill is scheduled to be processed:</p>
                            <p><strong>Medicine:</strong> {autoRefill.Medicine.Name}</p>
                            <p><strong>Quantity:</strong> {autoRefill.Quantity}</p>
                            <p><strong>Next Refill Date:</strong> {autoRefill.NextRefillDate:MMM dd, yyyy}</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GenerateLowStockAlertTemplate(Medicine medicine)
        {
            return $@"
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .header {{ background: #dc3545; color: white; padding: 20px; text-align: center; }}
                        .content {{ padding: 20px; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>Low Stock Alert</h1>
                        </div>
                        <div class='content'>
                            <h2>Attention Required!</h2>
                            <p>The following medicine is running low on stock:</p>
                            <p><strong>Medicine:</strong> {medicine.Name}</p>
                            <p><strong>Current Stock:</strong> {medicine.StockQuantity}</p>
                            <p><strong>Minimum Level:</strong> {medicine.MinimumStockLevel}</p>
                            <p>Please reorder immediately to avoid stockouts.</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        #endregion

        #region Helper Methods

        private static string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return phoneNumber;

            // Remove all non-numeric characters
            var cleaned = System.Text.RegularExpressions.Regex.Replace(phoneNumber, @"[^\d]", "");
            
            // Add country code if not present (assuming US +1)
            if (cleaned.Length == 10)
                cleaned = "1" + cleaned;
            
            return "+" + cleaned;
        }

        #endregion
    }
}
