using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Customer
{
    public partial class PriceComparison : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!IsPostBack)
            {
                LoadCategories();
                
                // Check if there's a search query in URL
                var searchQuery = Request.QueryString["search"];
                if (!string.IsNullOrEmpty(searchQuery))
                {
                    txtMedicineSearch.Text = searchQuery;
                    PerformSearch();
                }
            }
        }

        private void LoadCategories()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var categories = db.Medicines
                        .Where(m => !string.IsNullOrEmpty(m.Category))
                        .Select(m => m.Category)
                        .Distinct()
                        .OrderBy(c => c)
                        .ToList();

                    ddlCategory.Items.Clear();
                    ddlCategory.Items.Add(new ListItem("All Categories", ""));
                    
                    foreach (var category in categories)
                    {
                        ddlCategory.Items.Add(new ListItem(category, category));
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading categories for price comparison");
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        protected void ddlCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            PerformSearch();
        }

        protected void ddlSortBy_SelectedIndexChanged(object sender, EventArgs e)
        {
            PerformSearch();
        }

        private void PerformSearch()
        {
            try
            {
                var searchTerm = txtMedicineSearch.Text.Trim();
                var selectedCategory = ddlCategory.SelectedValue;
                var sortBy = ddlSortBy.SelectedValue;

                using (var db = new MediEaseContext())
                {
                    var query = db.Medicines.AsQueryable();

                    // Apply search filter
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(m => 
                            m.Name.Contains(searchTerm) || 
                            m.GenericName.Contains(searchTerm) ||
                            m.Description.Contains(searchTerm) ||
                            m.Manufacturer.Contains(searchTerm));
                    }

                    // Apply category filter
                    if (!string.IsNullOrEmpty(selectedCategory))
                    {
                        query = query.Where(m => m.Category == selectedCategory);
                    }

                    // Apply sorting
                    switch (sortBy)
                    {
                        case "price_asc":
                            query = query.OrderBy(m => m.FinalPrice);
                            break;
                        case "price_desc":
                            query = query.OrderByDescending(m => m.FinalPrice);
                            break;
                        case "savings_desc":
                            query = query.OrderByDescending(m => m.DiscountPercentage);
                            break;
                        case "name_asc":
                            query = query.OrderBy(m => m.Name);
                            break;
                        case "popularity":
                            // Order by stock quantity as a proxy for popularity
                            query = query.OrderByDescending(m => m.StockQuantity);
                            break;
                        default:
                            query = query.OrderBy(m => m.FinalPrice);
                            break;
                    }

                    var results = query.Take(50).ToList(); // Limit to 50 results for performance

                    if (results.Any())
                    {
                        // Bind to both GridView and Repeater
                        gvPriceComparison.DataSource = results;
                        gvPriceComparison.DataBind();
                        
                        rptMedicinesGrid.DataSource = results;
                        rptMedicinesGrid.DataBind();

                        lblResultCount.Text = results.Count.ToString();
                        pnlResults.Visible = true;
                        pnlNoResults.Visible = false;

                        // Log search activity
                        var currentUser = SecurityHelper.GetCurrentUser();
                        if (currentUser != null)
                        {
                            ErrorLogger.LogUserActivity($"Price comparison search: '{searchTerm}' in category '{selectedCategory}'", currentUser.UserId);
                        }
                    }
                    else
                    {
                        pnlResults.Visible = false;
                        pnlNoResults.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error performing price comparison search");
                ShowErrorMessage("An error occurred while searching. Please try again.");
                pnlResults.Visible = false;
                pnlNoResults.Visible = true;
            }
        }

        protected void btnSetAlert_Click(object sender, EventArgs e)
        {
            try
            {
                var medicineName = txtAlertMedicine.Text.Trim();
                var targetPriceText = txtTargetPrice.Text.Trim();

                if (string.IsNullOrEmpty(medicineName) || string.IsNullOrEmpty(targetPriceText))
                {
                    ShowErrorMessage("Please enter both medicine name and target price.");
                    return;
                }

                if (!decimal.TryParse(targetPriceText, out decimal targetPrice) || targetPrice <= 0)
                {
                    ShowErrorMessage("Please enter a valid target price.");
                    return;
                }

                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    // Check if medicine exists
                    var medicine = db.Medicines.FirstOrDefault(m => 
                        m.Name.Contains(medicineName) || m.GenericName.Contains(medicineName));

                    if (medicine == null)
                    {
                        ShowErrorMessage("Medicine not found. Please check the name and try again.");
                        return;
                    }

                    // Create price alert (you would need to create a PriceAlert model/table)
                    // For now, just log the alert request
                    ErrorLogger.LogUserActivity($"Price alert set for '{medicine.Name}' at target price ${targetPrice}", currentUser.UserId);
                    
                    ShowSuccessMessage($"Price alert set for {medicine.Name} at ${targetPrice:F2}. You'll be notified when the price drops!");
                    
                    // Clear form
                    txtAlertMedicine.Text = "";
                    txtTargetPrice.Text = "";
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error setting price alert");
                ShowErrorMessage("An error occurred while setting the price alert. Please try again.");
            }
        }

        // Helper methods for data binding
        protected string GetMedicineImage(object imagePath)
        {
            var path = imagePath?.ToString();
            if (string.IsNullOrEmpty(path))
                return "~/Images/medicine-placeholder.jpg";
            
            return path.StartsWith("~/") ? path : "~/Images/Medicines/" + path;
        }

        protected string GetBestDealBadge(object dataItem)
        {
            if (dataItem is Medicine medicine)
            {
                if (medicine.DiscountPercentage >= 30)
                {
                    return "<div class=\"position-absolute top-0 start-0 bg-success text-white px-2 py-1 m-2 rounded\">BEST DEAL</div>";
                }
                else if (medicine.DiscountPercentage >= 20)
                {
                    return "<div class=\"position-absolute top-0 start-0 bg-warning text-dark px-2 py-1 m-2 rounded\">GREAT DEAL</div>";
                }
            }
            return "";
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Price Comparison Tool - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Compare medicine prices across different brands and manufacturers. Find the best deals and save money on your medications.");
                master.AddMetaKeywords("price comparison, medicine prices, pharmacy deals, medication savings, best prices");
            }
        }
    }
}
