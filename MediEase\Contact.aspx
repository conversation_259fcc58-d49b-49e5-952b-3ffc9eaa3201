<%@ Page Title="Contact Us" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Contact.aspx.cs" Inherits="MediEase.Contact" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="display-4 text-primary">Contact Us</h1>
                <p class="lead text-muted">Get in touch with our team for any questions or support</p>
            </div>
        </div>

        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-envelope me-2"></i>Send us a Message</h4>
                    </div>
                    <div class="card-body p-4">
                        <asp:Panel ID="pnlSuccess" runat="server" CssClass="alert alert-success" Visible="false">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Thank you!</strong> Your message has been sent successfully. We'll get back to you soon.
                        </asp:Panel>

                        <asp:Panel ID="pnlError" runat="server" CssClass="alert alert-danger" Visible="false">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <asp:Label ID="lblError" runat="server"></asp:Label>
                        </asp:Panel>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="txtName" class="form-label">Full Name *</label>
                                <asp:TextBox ID="txtName" runat="server" CssClass="form-control" placeholder="Enter your full name" Required="true"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvName" runat="server" ControlToValidate="txtName" 
                                    ErrorMessage="Name is required" CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="txtEmail" class="form-label">Email Address *</label>
                                <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" TextMode="Email" placeholder="Enter your email" Required="true"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail" 
                                    ErrorMessage="Email is required" CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail"
                                    ValidationExpression="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                                    ErrorMessage="Please enter a valid email address" CssClass="text-danger small" Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="txtPhone" class="form-label">Phone Number</label>
                                <asp:TextBox ID="txtPhone" runat="server" CssClass="form-control" placeholder="Enter your phone number"></asp:TextBox>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="ddlSubject" class="form-label">Subject *</label>
                                <asp:DropDownList ID="ddlSubject" runat="server" CssClass="form-select" Required="true">
                                    <asp:ListItem Value="">Select a subject</asp:ListItem>
                                    <asp:ListItem Value="General Inquiry">General Inquiry</asp:ListItem>
                                    <asp:ListItem Value="Order Support">Order Support</asp:ListItem>
                                    <asp:ListItem Value="Prescription Help">Prescription Help</asp:ListItem>
                                    <asp:ListItem Value="Technical Support">Technical Support</asp:ListItem>
                                    <asp:ListItem Value="Billing Question">Billing Question</asp:ListItem>
                                    <asp:ListItem Value="Complaint">Complaint</asp:ListItem>
                                    <asp:ListItem Value="Feedback">Feedback</asp:ListItem>
                                    <asp:ListItem Value="Partnership">Partnership</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvSubject" runat="server" ControlToValidate="ddlSubject" 
                                    ErrorMessage="Please select a subject" CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="txtMessage" class="form-label">Message *</label>
                            <asp:TextBox ID="txtMessage" runat="server" CssClass="form-control" TextMode="MultiLine" 
                                Rows="5" placeholder="Enter your message here..." Required="true"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvMessage" runat="server" ControlToValidate="txtMessage" 
                                ErrorMessage="Message is required" CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                        </div>

                        <div class="mb-3 form-check">
                            <asp:CheckBox ID="chkNewsletter" runat="server" CssClass="form-check-input" />
                            <label class="form-check-label" for="chkNewsletter">
                                Subscribe to our newsletter for health tips and special offers
                            </label>
                        </div>

                        <div class="d-grid">
                            <asp:Button ID="btnSend" runat="server" CssClass="btn btn-primary btn-lg" 
                                Text="Send Message" OnClick="btnSend_Click" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Our Location</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6><i class="fas fa-building me-2 text-primary"></i>MediEase Pharmacy</h6>
                            <p class="mb-1">123 Healthcare Avenue</p>
                            <p class="mb-1">Medical District</p>
                            <p class="mb-0">City, State 12345</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-phone me-2 text-primary"></i>Phone</h6>
                            <p class="mb-0">
                                <a href="tel:+**********" class="text-decoration-none">+****************</a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-envelope me-2 text-primary"></i>Email</h6>
                            <p class="mb-0">
                                <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-clock me-2 text-primary"></i>Business Hours</h6>
                            <p class="mb-1"><strong>Monday - Friday:</strong> 8:00 AM - 8:00 PM</p>
                            <p class="mb-1"><strong>Saturday:</strong> 9:00 AM - 6:00 PM</p>
                            <p class="mb-0"><strong>Sunday:</strong> 10:00 AM - 4:00 PM</p>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-headset me-2"></i>Emergency Support</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">For urgent medical questions or prescription emergencies:</p>
                        <p class="mb-2">
                            <strong>24/7 Hotline:</strong><br>
                            <a href="tel:+**********" class="text-decoration-none text-danger">
                                <i class="fas fa-phone me-1"></i>+****************
                            </a>
                        </p>
                        <p class="small text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            For life-threatening emergencies, please call 911 or visit your nearest emergency room.
                        </p>
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>Follow Us</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center gap-3">
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-info btn-sm">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-danger btn-sm">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                        <p class="small text-muted mt-2 mb-0">
                            Stay connected for health tips and updates
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h4 class="mb-0"><i class="fas fa-question-circle me-2"></i>Quick Help</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-shipping-fast me-2 text-primary"></i>Delivery Information</h6>
                                <p class="small">Standard delivery: 2-3 business days<br>Express delivery: Same day or next day</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-undo me-2 text-primary"></i>Return Policy</h6>
                                <p class="small">Unopened medications can be returned within 30 days with original receipt</p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-prescription-bottle me-2 text-primary"></i>Prescription Refills</h6>
                                <p class="small">Upload your prescription or call us for easy refills</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-shield-alt me-2 text-primary"></i>Privacy & Security</h6>
                                <p class="small">Your health information is protected with industry-standard encryption</p>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <a href="~/FAQ.aspx" runat="server" class="btn btn-outline-primary">
                                <i class="fas fa-question-circle me-2"></i>View Full FAQ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
