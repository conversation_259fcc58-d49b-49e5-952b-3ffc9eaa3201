using System;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;
using Newtonsoft.Json;

namespace MediEase.Customer
{
    public partial class AccessibilitySettings : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is logged in
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null)
                {
                    Response.Redirect("~/Login.aspx");
                    return;
                }

                LoadUserAccessibilitySettings();
            }
        }

        private void LoadUserAccessibilitySettings()
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser != null)
                {
                    using (var db = new MediEaseContext())
                    {
                        var user = db.Users.Find(currentUser.UserId);
                        if (user != null && !string.IsNullOrEmpty(user.AccessibilitySettings))
                        {
                            var settings = JsonConvert.DeserializeObject<AccessibilitySettingsModel>(user.AccessibilitySettings);
                            ApplySettingsToControls(settings);
                        }
                        else
                        {
                            // Load default settings
                            ApplyDefaultSettings();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "LoadUserAccessibilitySettings");
                ApplyDefaultSettings();
            }
        }

        private void ApplySettingsToControls(AccessibilitySettingsModel settings)
        {
            // Apply settings to page controls via JavaScript
            string script = $@"
                $(document).ready(function() {{
                    // Theme
                    $('input[name=""theme""][value=""{settings.Theme}""]').prop('checked', true);
                    
                    // Font size
                    $('#fontSizeSlider').val({settings.FontSize});
                    $('#fontSizeDisplay').text('{settings.FontSize}px');
                    
                    // Font family
                    $('#fontFamily').val('{settings.FontFamily}');
                    
                    // Color scheme
                    $('#colorScheme').val('{settings.ColorScheme}');
                    
                    // Checkboxes
                    $('#reduceMotion').prop('checked', {settings.ReduceMotion.ToString().ToLower()});
                    $('#increaseFocus').prop('checked', {settings.EnhancedFocus.ToString().ToLower()});
                    $('#underlineLinks').prop('checked', {settings.UnderlineLinks.ToString().ToLower()});
                    $('#enableTTS').prop('checked', {settings.TextToSpeech.ToString().ToLower()});
                    $('#enableSoundEffects').prop('checked', {settings.SoundEffects.ToString().ToLower()});
                    $('#enableKeyboardNav').prop('checked', {settings.KeyboardNavigation.ToString().ToLower()});
                    $('#skipLinks').prop('checked', {settings.SkipLinks.ToString().ToLower()});
                    $('#showShortcuts').prop('checked', {settings.ShowShortcuts.ToString().ToLower()});
                    
                    // Speech rate
                    $('#speechRate').val({settings.SpeechRate});
                    
                    // Update preview
                    updatePreview();
                    
                    // Show/hide panels based on settings
                    if ({settings.TextToSpeech.ToString().ToLower()}) {{
                        $('#ttsControls').show();
                    }}
                    if ({settings.ShowShortcuts.ToString().ToLower()}) {{
                        $('#shortcutsPanel').show();
                    }}
                }});
            ";

            ScriptManager.RegisterStartupScript(this, GetType(), "loadSettings", script, true);
        }

        private void ApplyDefaultSettings()
        {
            string script = @"
                $(document).ready(function() {
                    // Default settings
                    $('input[name=""theme""][value=""light""]').prop('checked', true);
                    $('#fontSizeSlider').val(16);
                    $('#fontSizeDisplay').text('16px');
                    $('#fontFamily').val('default');
                    $('#colorScheme').val('default');
                    $('#speechRate').val(1);
                    
                    // All checkboxes unchecked by default
                    $('.form-check-input').prop('checked', false);
                    
                    updatePreview();
                });
            ";

            ScriptManager.RegisterStartupScript(this, GetType(), "defaultSettings", script, true);
        }

        protected void btnSaveSettings_Click(object sender, EventArgs e)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null)
                {
                    ShowErrorMessage("Please log in to save settings.");
                    return;
                }

                // Get settings from client-side via hidden field or AJAX
                string settingsJson = Request.Form["accessibilitySettingsData"];
                
                if (string.IsNullOrEmpty(settingsJson))
                {
                    // If no data from client, create default settings
                    var defaultSettings = new AccessibilitySettingsModel();
                    settingsJson = JsonConvert.SerializeObject(defaultSettings);
                }

                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find(currentUser.UserId);
                    if (user != null)
                    {
                        user.AccessibilitySettings = settingsJson;
                        user.ModifiedDate = DateTime.Now;
                        
                        db.SaveChanges();
                        
                        ShowSuccessMessage("Accessibility settings saved successfully!");
                        
                        // Log the action
                        ErrorLogger.LogInfo($"User {user.Email} updated accessibility settings", "AccessibilitySettings");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "btnSaveSettings_Click");
                ShowErrorMessage("Error saving accessibility settings. Please try again.");
            }
        }

        private void ShowSuccessMessage(string message)
        {
            string script = $@"
                $(document).ready(function() {{
                    showSuccessToast('{message.Replace("'", "\\'")}');
                }});
            ";
            ScriptManager.RegisterStartupScript(this, GetType(), "success", script, true);
        }

        private void ShowErrorMessage(string message)
        {
            string script = $@"
                $(document).ready(function() {{
                    showErrorToast('{message.Replace("'", "\\'")}');
                }});
            ";
            ScriptManager.RegisterStartupScript(this, GetType(), "error", script, true);
        }
    }

    // Model for accessibility settings
    public class AccessibilitySettingsModel
    {
        public string Theme { get; set; } = "light";
        public string ColorScheme { get; set; } = "default";
        public int FontSize { get; set; } = 16;
        public string FontFamily { get; set; } = "default";
        public bool ReduceMotion { get; set; } = false;
        public bool EnhancedFocus { get; set; } = false;
        public bool UnderlineLinks { get; set; } = false;
        public bool TextToSpeech { get; set; } = false;
        public bool SoundEffects { get; set; } = false;
        public bool KeyboardNavigation { get; set; } = false;
        public bool SkipLinks { get; set; } = false;
        public bool ShowShortcuts { get; set; } = false;
        public double SpeechRate { get; set; } = 1.0;
        public string Voice { get; set; } = "default";
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }
}
