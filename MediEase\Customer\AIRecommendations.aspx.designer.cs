//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Customer
{
    public partial class AIRecommendations
    {
        /// <summary>
        /// txtSymptoms control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtSymptoms;

        /// <summary>
        /// ddlSeverity control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlSeverity;

        /// <summary>
        /// ddlDuration control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlDuration;

        /// <summary>
        /// ddlAgeGroup control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlAgeGroup;

        /// <summary>
        /// txtAllergies control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtAllergies;

        /// <summary>
        /// txtCurrentMedications control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtCurrentMedications;

        /// <summary>
        /// txtMedicalConditions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtMedicalConditions;

        /// <summary>
        /// btnGetRecommendations control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnGetRecommendations;

        /// <summary>
        /// pnlRecommendations control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlRecommendations;

        /// <summary>
        /// litRecommendations control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Literal litRecommendations;

        /// <summary>
        /// pnlAvailableMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlAvailableMedicines;

        /// <summary>
        /// rptRecommendedMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptRecommendedMedicines;
    }
}
