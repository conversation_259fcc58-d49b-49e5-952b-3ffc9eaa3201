<%@ Page Title="System Configuration" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="SystemConfiguration.aspx.cs" Inherits="MediEase.Admin.SystemConfiguration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-cogs me-2 text-primary"></i>System Configuration</h2>
                        <p class="text-muted">Manage system settings, integrations, and configurations</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" onclick="saveAllSettings()">
                            <i class="fas fa-save me-2"></i>Save All Changes
                        </button>
                        <button type="button" class="btn btn-warning" onclick="resetToDefaults()">
                            <i class="fas fa-undo me-2"></i>Reset to Defaults
                        </button>
                        <button type="button" class="btn btn-info" onclick="exportConfiguration()">
                            <i class="fas fa-download me-2"></i>Export Config
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="configTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                                    <i class="fas fa-cog me-2"></i>General
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                                    <i class="fas fa-envelope me-2"></i>Email Settings
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="sms-tab" data-bs-toggle="tab" data-bs-target="#sms" type="button" role="tab">
                                    <i class="fas fa-sms me-2"></i>SMS Settings
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                                    <i class="fas fa-credit-card me-2"></i>Payment
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="ai-tab" data-bs-toggle="tab" data-bs-target="#ai" type="button" role="tab">
                                    <i class="fas fa-robot me-2"></i>AI Settings
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                                    <i class="fas fa-shield-alt me-2"></i>Security
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="configTabContent">
                            <!-- General Settings -->
                            <div class="tab-pane fade show active" id="general" role="tabpanel">
                                <h5 class="mb-4">General Application Settings</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Application Name</label>
                                            <asp:TextBox ID="txtAppName" runat="server" CssClass="form-control" placeholder="MediEase"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Company Name</label>
                                            <asp:TextBox ID="txtCompanyName" runat="server" CssClass="form-control" placeholder="Your Pharmacy"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Contact Email</label>
                                            <asp:TextBox ID="txtContactEmail" runat="server" CssClass="form-control" TextMode="Email" placeholder="<EMAIL>"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Contact Phone</label>
                                            <asp:TextBox ID="txtContactPhone" runat="server" CssClass="form-control" placeholder="+****************"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Session Timeout (minutes)</label>
                                            <asp:TextBox ID="txtSessionTimeout" runat="server" CssClass="form-control" TextMode="Number" placeholder="30"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Max File Upload Size (MB)</label>
                                            <asp:TextBox ID="txtMaxFileSize" runat="server" CssClass="form-control" TextMode="Number" placeholder="5"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Default Currency</label>
                                            <asp:DropDownList ID="ddlCurrency" runat="server" CssClass="form-select">
                                                <asp:ListItem Value="USD">USD - US Dollar</asp:ListItem>
                                                <asp:ListItem Value="EUR">EUR - Euro</asp:ListItem>
                                                <asp:ListItem Value="GBP">GBP - British Pound</asp:ListItem>
                                                <asp:ListItem Value="CAD">CAD - Canadian Dollar</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Time Zone</label>
                                            <asp:DropDownList ID="ddlTimeZone" runat="server" CssClass="form-select">
                                                <asp:ListItem Value="UTC">UTC</asp:ListItem>
                                                <asp:ListItem Value="EST">Eastern Time</asp:ListItem>
                                                <asp:ListItem Value="PST">Pacific Time</asp:ListItem>
                                                <asp:ListItem Value="CST">Central Time</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">Company Address</label>
                                            <asp:TextBox ID="txtCompanyAddress" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                                placeholder="Enter complete company address..."></asp:TextBox>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Email Settings -->
                            <div class="tab-pane fade" id="email" role="tabpanel">
                                <h5 class="mb-4">Email Configuration</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">SMTP Server</label>
                                            <asp:TextBox ID="txtSmtpServer" runat="server" CssClass="form-control" placeholder="smtp.gmail.com"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">SMTP Port</label>
                                            <asp:TextBox ID="txtSmtpPort" runat="server" CssClass="form-control" TextMode="Number" placeholder="587"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Username</label>
                                            <asp:TextBox ID="txtSmtpUsername" runat="server" CssClass="form-control" placeholder="<EMAIL>"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Password</label>
                                            <asp:TextBox ID="txtSmtpPassword" runat="server" CssClass="form-control" TextMode="Password" placeholder="••••••••"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">From Email</label>
                                            <asp:TextBox ID="txtFromEmail" runat="server" CssClass="form-control" placeholder="<EMAIL>"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">From Name</label>
                                            <asp:TextBox ID="txtFromName" runat="server" CssClass="form-control" placeholder="Your Pharmacy"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkEnableSSL" runat="server" CssClass="form-check-input" Checked="true" />
                                                <label class="form-check-label" for="<%= chkEnableSSL.ClientID %>">
                                                    Enable SSL/TLS
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkEmailNotifications" runat="server" CssClass="form-check-input" Checked="true" />
                                                <label class="form-check-label" for="<%= chkEmailNotifications.ClientID %>">
                                                    Enable Email Notifications
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <button type="button" class="btn btn-outline-primary" onclick="testEmailConnection()">
                                            <i class="fas fa-paper-plane me-2"></i>Test Email Connection
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- SMS Settings -->
                            <div class="tab-pane fade" id="sms" role="tabpanel">
                                <h5 class="mb-4">SMS Configuration</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">SMS Provider</label>
                                            <asp:DropDownList ID="ddlSmsProvider" runat="server" CssClass="form-select">
                                                <asp:ListItem Value="">Select Provider</asp:ListItem>
                                                <asp:ListItem Value="twilio">Twilio</asp:ListItem>
                                                <asp:ListItem Value="nexmo">Nexmo/Vonage</asp:ListItem>
                                                <asp:ListItem Value="textmagic">TextMagic</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API Key</label>
                                            <asp:TextBox ID="txtSmsApiKey" runat="server" CssClass="form-control" TextMode="Password" placeholder="Your SMS API Key"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API Secret</label>
                                            <asp:TextBox ID="txtSmsApiSecret" runat="server" CssClass="form-control" TextMode="Password" placeholder="Your SMS API Secret"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">From Number</label>
                                            <asp:TextBox ID="txtSmsFromNumber" runat="server" CssClass="form-control" placeholder="+1234567890"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkSmsNotifications" runat="server" CssClass="form-check-input" />
                                                <label class="form-check-label" for="<%= chkSmsNotifications.ClientID %>">
                                                    Enable SMS Notifications
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-outline-primary" onclick="testSmsConnection()">
                                                <i class="fas fa-sms me-2"></i>Test SMS Connection
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Settings -->
                            <div class="tab-pane fade" id="payment" role="tabpanel">
                                <h5 class="mb-4">Payment Gateway Configuration</h5>
                                
                                <!-- Stripe Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Stripe Configuration</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Publishable Key</label>
                                                    <asp:TextBox ID="txtStripePublishableKey" runat="server" CssClass="form-control" placeholder="pk_test_..."></asp:TextBox>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Secret Key</label>
                                                    <asp:TextBox ID="txtStripeSecretKey" runat="server" CssClass="form-control" TextMode="Password" placeholder="sk_test_..."></asp:TextBox>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <asp:CheckBox ID="chkStripeEnabled" runat="server" CssClass="form-check-input" />
                                                        <label class="form-check-label" for="<%= chkStripeEnabled.ClientID %>">
                                                            Enable Stripe Payments
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <asp:CheckBox ID="chkStripeTestMode" runat="server" CssClass="form-check-input" Checked="true" />
                                                        <label class="form-check-label" for="<%= chkStripeTestMode.ClientID %>">
                                                            Test Mode
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- PayPal Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">PayPal Configuration</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Client ID</label>
                                                    <asp:TextBox ID="txtPayPalClientId" runat="server" CssClass="form-control" placeholder="PayPal Client ID"></asp:TextBox>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Client Secret</label>
                                                    <asp:TextBox ID="txtPayPalClientSecret" runat="server" CssClass="form-control" TextMode="Password" placeholder="PayPal Client Secret"></asp:TextBox>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <asp:CheckBox ID="chkPayPalEnabled" runat="server" CssClass="form-check-input" />
                                                        <label class="form-check-label" for="<%= chkPayPalEnabled.ClientID %>">
                                                            Enable PayPal Payments
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <asp:CheckBox ID="chkPayPalTestMode" runat="server" CssClass="form-check-input" Checked="true" />
                                                        <label class="form-check-label" for="<%= chkPayPalTestMode.ClientID %>">
                                                            Sandbox Mode
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Other Payment Options -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Other Payment Options</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <asp:CheckBox ID="chkCashOnDelivery" runat="server" CssClass="form-check-input" Checked="true" />
                                                    <label class="form-check-label" for="<%= chkCashOnDelivery.ClientID %>">
                                                        Enable Cash on Delivery
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">COD Service Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <asp:TextBox ID="txtCodFee" runat="server" CssClass="form-control" placeholder="0.00"></asp:TextBox>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- AI Settings -->
                            <div class="tab-pane fade" id="ai" role="tabpanel">
                                <h5 class="mb-4">AI & Chatbot Configuration</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">OpenRouter API Key</label>
                                            <asp:TextBox ID="txtOpenRouterApiKey" runat="server" CssClass="form-control" TextMode="Password" placeholder="sk-or-v1-..."></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">AI Model</label>
                                            <asp:DropDownList ID="ddlAiModel" runat="server" CssClass="form-select">
                                                <asp:ListItem Value="deepseek/deepseek-r1-0528-qwen3-8b:free">DeepSeek R1 (Free)</asp:ListItem>
                                                <asp:ListItem Value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</asp:ListItem>
                                                <asp:ListItem Value="openai/gpt-4">GPT-4</asp:ListItem>
                                                <asp:ListItem Value="anthropic/claude-3-haiku">Claude 3 Haiku</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Max Response Length</label>
                                            <asp:TextBox ID="txtMaxResponseLength" runat="server" CssClass="form-control" TextMode="Number" placeholder="500"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkChatbotEnabled" runat="server" CssClass="form-check-input" Checked="true" />
                                                <label class="form-check-label" for="<%= chkChatbotEnabled.ClientID %>">
                                                    Enable AI Chatbot
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkGuestChatbot" runat="server" CssClass="form-check-input" Checked="true" />
                                                <label class="form-check-label" for="<%= chkGuestChatbot.ClientID %>">
                                                    Allow Guest Access to Chatbot
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkAiRecommendations" runat="server" CssClass="form-check-input" Checked="true" />
                                                <label class="form-check-label" for="<%= chkAiRecommendations.ClientID %>">
                                                    Enable AI Medicine Recommendations
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-outline-primary" onclick="testAiConnection()">
                                                <i class="fas fa-robot me-2"></i>Test AI Connection
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">System Prompt</label>
                                            <asp:TextBox ID="txtSystemPrompt" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="4" 
                                                placeholder="Enter the system prompt for the AI chatbot..."></asp:TextBox>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Settings -->
                            <div class="tab-pane fade" id="security" role="tabpanel">
                                <h5 class="mb-4">Security Configuration</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Password Minimum Length</label>
                                            <asp:TextBox ID="txtPasswordMinLength" runat="server" CssClass="form-control" TextMode="Number" placeholder="8"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Login Attempt Limit</label>
                                            <asp:TextBox ID="txtLoginAttemptLimit" runat="server" CssClass="form-control" TextMode="Number" placeholder="5"></asp:TextBox>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Account Lockout Duration (minutes)</label>
                                            <asp:TextBox ID="txtLockoutDuration" runat="server" CssClass="form-control" TextMode="Number" placeholder="30"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkRequireUppercase" runat="server" CssClass="form-check-input" Checked="true" />
                                                <label class="form-check-label" for="<%= chkRequireUppercase.ClientID %>">
                                                    Require Uppercase Letters
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkRequireNumbers" runat="server" CssClass="form-check-input" Checked="true" />
                                                <label class="form-check-label" for="<%= chkRequireNumbers.ClientID %>">
                                                    Require Numbers
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkRequireSpecialChars" runat="server" CssClass="form-check-input" />
                                                <label class="form-check-label" for="<%= chkRequireSpecialChars.ClientID %>">
                                                    Require Special Characters
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <asp:CheckBox ID="chkEnableTwoFactor" runat="server" CssClass="form-check-input" />
                                                <label class="form-check-label" for="<%= chkEnableTwoFactor.ClientID %>">
                                                    Enable Two-Factor Authentication
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function saveAllSettings() {
            if (confirm('Are you sure you want to save all configuration changes?')) {
                // Collect all form data and submit
                __doPostBack('<%= btnSaveAll.UniqueID %>', '');
            }
        }

        function resetToDefaults() {
            if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
                // Reset to defaults
                __doPostBack('<%= btnResetDefaults.UniqueID %>', '');
            }
        }

        function exportConfiguration() {
            window.open('ExportConfiguration.aspx', '_blank');
        }

        function testEmailConnection() {
            // AJAX call to test email connection
            $.post('SystemConfiguration.aspx/TestEmailConnection', {}, function(response) {
                if (response.d.success) {
                    alert('Email connection successful!');
                } else {
                    alert('Email connection failed: ' + response.d.message);
                }
            });
        }

        function testSmsConnection() {
            // AJAX call to test SMS connection
            $.post('SystemConfiguration.aspx/TestSmsConnection', {}, function(response) {
                if (response.d.success) {
                    alert('SMS connection successful!');
                } else {
                    alert('SMS connection failed: ' + response.d.message);
                }
            });
        }

        function testAiConnection() {
            // AJAX call to test AI connection
            $.post('SystemConfiguration.aspx/TestAiConnection', {}, function(response) {
                if (response.d.success) {
                    alert('AI connection successful!');
                } else {
                    alert('AI connection failed: ' + response.d.message);
                }
            });
        }
    </script>

    <!-- Hidden buttons for postback -->
    <asp:Button ID="btnSaveAll" runat="server" style="display:none" OnClick="btnSaveAll_Click" />
    <asp:Button ID="btnResetDefaults" runat="server" style="display:none" OnClick="btnResetDefaults_Click" />
</asp:Content>
