using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.Services;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;
using System.Data.Entity;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace MediEase.Admin
{
    public partial class ChatbotManagement : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is admin
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || currentUser.Role != "Admin")
                {
                    Response.Redirect("~/Login.aspx");
                    return;
                }

                LoadChatbotStatistics();
                LoadChatLogs();
                LoadCommonQueries();
                LoadChatbotConfiguration();
            }
        }

        private void LoadChatbotStatistics()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var today = DateTime.Today;
                    
                    // Total conversations
                    var totalChats = db.ChatMessages.Count();
                    lblTotalChats.Text = totalChats.ToString();

                    // Active users today
                    var activeUsersToday = db.ChatMessages
                        .Where(m => m.Timestamp >= today)
                        .Select(m => m.SessionId)
                        .Distinct()
                        .Count();
                    lblActiveUsers.Text = activeUsersToday.ToString();

                    // Average response time (placeholder since we don't have ResponseTimeMs in current model)
                    var avgResponseTime = 1200; // Default value
                    lblAvgResponseTime.Text = $"{avgResponseTime}ms";

                    // Satisfaction rate (placeholder - would need rating system)
                    var satisfactionRate = 85; // Default value
                    lblSatisfactionRate.Text = $"{satisfactionRate}%";
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading chatbot statistics");
            }
        }

        private void LoadChatLogs()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var chatLogs = db.ChatMessages
                        .Include(m => m.User)
                        .OrderByDescending(m => m.CreatedDate)
                        .Take(100) // Limit to recent 100 messages
                        .ToList();

                    gvChatLogs.DataSource = chatLogs;
                    gvChatLogs.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading chat logs");
            }
        }

        private void LoadCommonQueries()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var commonQueries = db.ChatMessages
                        .Where(m => !string.IsNullOrEmpty(m.Message) && m.Sender == "User")
                        .GroupBy(m => m.Message.ToLower())
                        .Select(g => new { Query = g.Key, Count = g.Count() })
                        .OrderByDescending(x => x.Count)
                        .Take(10)
                        .ToList();

                    rptCommonQueries.DataSource = commonQueries;
                    rptCommonQueries.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading common queries");
            }
        }

        private void LoadChatbotConfiguration()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Load current configuration from SystemConfiguration table
                    var configs = db.SystemConfigurations
                        .Where(c => c.Category == "AI")
                        .ToDictionary(c => c.ConfigKey, c => c.ConfigValue);

                    if (configs.ContainsKey("ChatbotEnabled"))
                        chkChatbotEnabled.Checked = bool.Parse(configs["ChatbotEnabled"]);

                    if (configs.ContainsKey("AIModel"))
                        ddlAiModel.SelectedValue = configs["AIModel"];

                    if (configs.ContainsKey("ResponseTimeout"))
                        txtResponseTimeout.Text = configs["ResponseTimeout"];

                    if (configs.ContainsKey("MaxResponseLength"))
                        txtMaxResponseLength.Text = configs["MaxResponseLength"];

                    if (configs.ContainsKey("RateLimit"))
                        txtRateLimit.Text = configs["RateLimit"];

                    if (configs.ContainsKey("GuestAccess"))
                        chkGuestAccess.Checked = bool.Parse(configs["GuestAccess"]);

                    if (configs.ContainsKey("SystemPrompt"))
                        txtSystemPrompt.Text = configs["SystemPrompt"];
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading chatbot configuration");
            }
        }

        protected void btnSaveConfiguration_Click(object sender, EventArgs e)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var currentUser = SecurityHelper.GetCurrentUser();
                    var configs = new[]
                    {
                        new { Key = "ChatbotEnabled", Value = chkChatbotEnabled.Checked.ToString() },
                        new { Key = "AIModel", Value = ddlAiModel.SelectedValue },
                        new { Key = "ResponseTimeout", Value = txtResponseTimeout.Text },
                        new { Key = "MaxResponseLength", Value = txtMaxResponseLength.Text },
                        new { Key = "RateLimit", Value = txtRateLimit.Text },
                        new { Key = "GuestAccess", Value = chkGuestAccess.Checked.ToString() },
                        new { Key = "SystemPrompt", Value = txtSystemPrompt.Text }
                    };

                    foreach (var config in configs)
                    {
                        var existingConfig = db.SystemConfigurations
                            .FirstOrDefault(c => c.ConfigKey == config.Key && c.Category == "AI");

                        if (existingConfig != null)
                        {
                            existingConfig.ConfigValue = config.Value;
                            existingConfig.ModifiedDate = DateTime.Now;
                            existingConfig.ModifiedBy = currentUser.UserId;
                        }
                        else
                        {
                            db.SystemConfigurations.Add(new SystemConfiguration
                            {
                                ConfigKey = config.Key,
                                ConfigValue = config.Value,
                                Category = "AI",
                                Description = $"AI Chatbot {config.Key}",
                                ModifiedDate = DateTime.Now,
                                ModifiedBy = currentUser.UserId
                            });
                        }
                    }

                    db.SaveChanges();
                    
                    // Show success message
                    ScriptManager.RegisterStartupScript(this, GetType(), "success", 
                        "alert('Configuration saved successfully!');", true);
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error saving chatbot configuration");
                ScriptManager.RegisterStartupScript(this, GetType(), "error", 
                    "alert('Error saving configuration. Please try again.');", true);
            }
        }

        protected void gvChatLogs_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "Delete")
            {
                try
                {
                    int chatId = Convert.ToInt32(e.CommandArgument);
                    using (var db = new MediEaseContext())
                    {
                        var chatMessage = db.ChatMessages.Find(chatId);
                        if (chatMessage != null)
                        {
                            db.ChatMessages.Remove(chatMessage);
                            db.SaveChanges();
                            LoadChatLogs(); // Refresh the grid
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error deleting chat message");
                }
            }
        }

        protected void gvChatLogs_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvChatLogs.PageIndex = e.NewPageIndex;
            LoadChatLogs();
        }

        protected void btnSearchLogs_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtSearchLogs.Text.Trim();
                using (var db = new MediEaseContext())
                {
                    var chatLogs = db.ChatMessages
                        .Include(m => m.User)
                        .Where(m => string.IsNullOrEmpty(searchTerm) ||
                                   m.Message.Contains(searchTerm) ||
                                   (m.Response != null && m.Response.Contains(searchTerm)))
                        .OrderByDescending(m => m.CreatedDate)
                        .Take(100)
                        .ToList();

                    gvChatLogs.DataSource = chatLogs;
                    gvChatLogs.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error searching chat logs");
            }
        }

        // Helper methods for GridView
        protected string GetUserName(object userId)
        {
            if (userId == null || userId == DBNull.Value)
                return "Guest User";

            try
            {
                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find((int)userId);
                    return user != null ? $"{user.FirstName} {user.LastName}" : "Unknown User";
                }
            }
            catch
            {
                return "Unknown User";
            }
        }

        protected string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            return text.Length <= maxLength ? text : text.Substring(0, maxLength) + "...";
        }

        protected string GetResponseTimeColor(int responseTime)
        {
            if (responseTime < 1000) return "success";
            if (responseTime < 3000) return "warning";
            return "danger";
        }

        // Web Methods for AJAX calls
        [WebMethod]
        public static object GetChatAnalytics(string period)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    DateTime startDate;
                    switch (period.ToLower())
                    {
                        case "today":
                            startDate = DateTime.Today;
                            break;
                        case "week":
                            startDate = DateTime.Today.AddDays(-7);
                            break;
                        case "month":
                            startDate = DateTime.Today.AddDays(-30);
                            break;
                        default:
                            startDate = DateTime.Today;
                            break;
                    }

                    var analytics = db.ChatMessages
                        .Where(m => m.CreatedDate >= startDate)
                        .GroupBy(m => m.CreatedDate.Date)
                        .Select(g => new { Date = g.Key, Count = g.Count() })
                        .OrderBy(x => x.Date)
                        .ToList();

                    return new
                    {
                        success = true,
                        labels = analytics.Select(a => a.Date.ToString("MMM dd")).ToArray(),
                        data = analytics.Select(a => a.Count).ToArray()
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting chat analytics");
                return new { success = false, message = ex.Message };
            }
        }

        [WebMethod]
        public static object TestChatbotResponse(string message)
        {
            try
            {
                var response = AIHelper.GetChatbotResponseAsync(message).Result;
                return new { success = true, response = response };
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error testing chatbot response");
                return new { success = false, message = ex.Message };
            }
        }

        [WebMethod]
        public static object ClearChatHistory()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var allMessages = db.ChatMessages.ToList();
                    db.ChatMessages.RemoveRange(allMessages);
                    db.SaveChanges();
                    return new { success = true };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error clearing chat history");
                return new { success = false, message = ex.Message };
            }
        }

        [WebMethod]
        public static object GetConversation(string sessionId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var messages = db.ChatMessages
                        .Where(m => m.SessionId == sessionId)
                        .OrderBy(m => m.CreatedDate)
                        .ToList();

                    var html = string.Join("", messages.Select(m =>
                        $"<div class='chat-message mb-3'>" +
                        $"<div class='message'><strong>{m.Sender}:</strong> {m.Message}</div>" +
                        (m.Response != null ? $"<div class='response'><strong>Response:</strong> {m.Response}</div>" : "") +
                        $"<small class='text-muted'>{m.CreatedDate:MMM dd, yyyy hh:mm tt}</small>" +
                        $"</div>"));

                    return new { success = true, html = html };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting conversation");
                return new { success = false, message = ex.Message };
            }
        }

        [WebMethod]
        public static object CheckSystemHealth()
        {
            try
            {
                // Test AI API connection
                var testResponse = AIHelper.GetChatbotResponseAsync("Hello").Result;
                var status = !string.IsNullOrEmpty(testResponse) ? "Healthy" : "Unhealthy";
                
                return new { success = true, status = status };
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error checking system health");
                return new { success = false, message = ex.Message };
            }
        }

        [WebMethod]
        public static object ResetChatbot()
        {
            try
            {
                // Clear conversation contexts (implementation depends on your caching strategy)
                return new { success = true };
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error resetting chatbot");
                return new { success = false, message = ex.Message };
            }
        }
    }
}
