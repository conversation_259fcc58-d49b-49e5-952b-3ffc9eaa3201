using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Pharmacist
{
    public partial class OrderManagement : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a pharmacist
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadOrders();
                LoadKanbanOrders();
                UpdateStats();
            }
        }

        private void LoadOrders()
        {
            try
            {
                var statusFilter = ddlStatusFilter.SelectedValue;
                var priorityFilter = ddlPriorityFilter.SelectedValue;
                var dateFilter = ddlDateFilter.SelectedValue;
                var searchTerm = txtSearch.Text.Trim();

                using (var db = new MediEaseContext())
                {
                    var query = db.Orders.AsQueryable();

                    // Apply status filter
                    if (!string.IsNullOrEmpty(statusFilter))
                    {
                        query = query.Where(o => o.Status == statusFilter);
                    }

                    // Apply priority filter
                    if (!string.IsNullOrEmpty(priorityFilter))
                    {
                        var isUrgent = priorityFilter == "Urgent";
                        query = query.Where(o => o.IsUrgent == isUrgent);
                    }

                    // Apply date filter
                    var today = DateTime.Today;
                    switch (dateFilter)
                    {
                        case "today":
                            query = query.Where(o => o.OrderDate >= today);
                            break;
                        case "yesterday":
                            var yesterday = today.AddDays(-1);
                            query = query.Where(o => o.OrderDate >= yesterday && o.OrderDate < today);
                            break;
                        case "week":
                            var weekStart = today.AddDays(-(int)today.DayOfWeek);
                            query = query.Where(o => o.OrderDate >= weekStart);
                            break;
                        case "month":
                            var monthStart = new DateTime(today.Year, today.Month, 1);
                            query = query.Where(o => o.OrderDate >= monthStart);
                            break;
                    }

                    // Apply search filter
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(o => 
                            o.OrderNumber.Contains(searchTerm) ||
                            o.Customer.FirstName.Contains(searchTerm) ||
                            o.Customer.LastName.Contains(searchTerm) ||
                            o.Customer.Email.Contains(searchTerm));
                    }

                    var orders = query
                        .OrderByDescending(o => o.IsUrgent)
                        .ThenByDescending(o => o.OrderDate)
                        .Select(o => new
                        {
                            o.OrderId,
                            o.OrderNumber,
                            o.OrderDate,
                            o.TotalAmount,
                            o.Status,
                            o.PaymentMethod,
                            o.IsUrgent,
                            o.RequiresPrescription,
                            CustomerName = o.Customer.FirstName + " " + o.Customer.LastName,
                            CustomerEmail = o.Customer.Email,
                            CustomerPhone = o.Customer.PhoneNumber,
                            ItemCount = db.OrderItems.Count(oi => oi.OrderId == o.OrderId),
                            ItemSummary = db.OrderItems
                                .Where(oi => oi.OrderId == o.OrderId)
                                .Take(2)
                                .Select(oi => oi.Medicine.Name)
                                .ToList()
                        })
                        .ToList();

                    // Format item summary
                    var formattedOrders = orders.Select(o => new
                    {
                        o.OrderId,
                        o.OrderNumber,
                        o.OrderDate,
                        o.TotalAmount,
                        o.Status,
                        o.PaymentMethod,
                        o.IsUrgent,
                        o.RequiresPrescription,
                        o.CustomerName,
                        o.CustomerEmail,
                        o.CustomerPhone,
                        o.ItemCount,
                        ItemSummary = string.Join(", ", o.ItemSummary) + (o.ItemCount > 2 ? $" and {o.ItemCount - 2} more..." : "")
                    }).ToList();

                    if (formattedOrders.Any())
                    {
                        gvOrders.DataSource = formattedOrders;
                        gvOrders.DataBind();
                        pnlNoOrders.Visible = false;
                    }
                    else
                    {
                        gvOrders.DataSource = null;
                        gvOrders.DataBind();
                        pnlNoOrders.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading orders for management");
                ShowErrorMessage("Error loading orders. Please try again.");
            }
        }

        private void LoadKanbanOrders()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var today = DateTime.Today;
                    var baseQuery = db.Orders.Where(o => o.OrderDate >= today);

                    // Load orders by status for Kanban view
                    var pendingOrders = baseQuery.Where(o => o.Status == "Pending")
                        .Select(o => new { o.OrderId, o.OrderNumber, o.TotalAmount, o.OrderDate, CustomerName = o.Customer.FirstName + " " + o.Customer.LastName })
                        .ToList();
                    rptPendingOrders.DataSource = pendingOrders;
                    rptPendingOrders.DataBind();

                    var processingOrders = baseQuery.Where(o => o.Status == "Processing")
                        .Select(o => new { o.OrderId, o.OrderNumber, o.TotalAmount, o.OrderDate, CustomerName = o.Customer.FirstName + " " + o.Customer.LastName })
                        .ToList();
                    rptProcessingOrders.DataSource = processingOrders;
                    rptProcessingOrders.DataBind();

                    var verifiedOrders = baseQuery.Where(o => o.Status == "Verified")
                        .Select(o => new { o.OrderId, o.OrderNumber, o.TotalAmount, o.OrderDate, CustomerName = o.Customer.FirstName + " " + o.Customer.LastName })
                        .ToList();
                    rptVerifiedOrders.DataSource = verifiedOrders;
                    rptVerifiedOrders.DataBind();

                    var packedOrders = baseQuery.Where(o => o.Status == "Packed")
                        .Select(o => new { o.OrderId, o.OrderNumber, o.TotalAmount, o.OrderDate, CustomerName = o.Customer.FirstName + " " + o.Customer.LastName })
                        .ToList();
                    rptPackedOrders.DataSource = packedOrders;
                    rptPackedOrders.DataBind();

                    var shippedOrders = baseQuery.Where(o => o.Status == "Shipped")
                        .Select(o => new { o.OrderId, o.OrderNumber, o.TotalAmount, o.OrderDate, CustomerName = o.Customer.FirstName + " " + o.Customer.LastName })
                        .ToList();
                    rptShippedOrders.DataSource = shippedOrders;
                    rptShippedOrders.DataBind();

                    var deliveredOrders = baseQuery.Where(o => o.Status == "Delivered")
                        .Select(o => new { o.OrderId, o.OrderNumber, o.TotalAmount, o.OrderDate, CustomerName = o.Customer.FirstName + " " + o.Customer.LastName })
                        .ToList();
                    rptDeliveredOrders.DataSource = deliveredOrders;
                    rptDeliveredOrders.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading Kanban orders");
            }
        }

        private void UpdateStats()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var today = DateTime.Today;
                    var tomorrow = today.AddDays(1);

                    // Today's orders
                    var todayOrders = db.Orders.Count(o => o.OrderDate >= today && o.OrderDate < tomorrow);
                    lblTodayOrders.Text = todayOrders.ToString();

                    // Completed today
                    var completedToday = db.Orders.Count(o => o.OrderDate >= today && o.OrderDate < tomorrow && o.Status == "Delivered");
                    lblCompletedToday.Text = completedToday.ToString();

                    // Urgent orders
                    var urgentOrders = db.Orders.Count(o => o.IsUrgent && o.Status != "Delivered" && o.Status != "Cancelled");
                    lblUrgentOrders.Text = urgentOrders.ToString();

                    // Today's revenue
                    var todayRevenue = db.Orders
                        .Where(o => o.OrderDate >= today && o.OrderDate < tomorrow && o.PaymentStatus == "Paid")
                        .Sum(o => (decimal?)o.TotalAmount) ?? 0;
                    lblTodayRevenue.Text = todayRevenue.ToString("F2");

                    // Pending and processing counts
                    var pendingCount = db.Orders.Count(o => o.Status == "Pending");
                    var processingCount = db.Orders.Count(o => o.Status == "Processing");
                    
                    lblPendingOrders.Text = pendingCount.ToString();
                    lblProcessingOrders.Text = processingCount.ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating order stats");
            }
        }

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadOrders();
        }

        protected void ddlPriorityFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadOrders();
        }

        protected void ddlDateFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadOrders();
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadOrders();
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            // Clear filters and reload
            ddlStatusFilter.SelectedValue = "Pending";
            ddlPriorityFilter.SelectedValue = "";
            ddlDateFilter.SelectedValue = "today";
            txtSearch.Text = "";
            LoadOrders();
            LoadKanbanOrders();
            UpdateStats();
        }

        protected void gvOrders_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (int.TryParse(e.CommandArgument.ToString(), out int orderId))
            {
                switch (e.CommandName)
                {
                    case "Process":
                        UpdateOrderStatusInternal(orderId, "Processing");
                        break;
                    case "Verify":
                        UpdateOrderStatusInternal(orderId, "Verified");
                        break;
                    case "Pack":
                        UpdateOrderStatusInternal(orderId, "Packed");
                        break;
                    case "Ship":
                        UpdateOrderStatusInternal(orderId, "Shipped");
                        break;
                    case "Deliver":
                        UpdateOrderStatusInternal(orderId, "Delivered");
                        break;
                }
            }
        }

        private void UpdateOrderStatusInternal(int orderId, string newStatus)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.Find(orderId);
                    if (order != null)
                    {
                        order.Status = newStatus;
                        order.ModifiedDate = DateTime.Now;
                        order.ModifiedBy = currentUser.UserId;

                        // Set delivery date if delivered
                        if (newStatus == "Delivered")
                        {
                            order.ActualDeliveryDate = DateTime.Now;
                        }

                        db.SaveChanges();

                        ErrorLogger.LogUserActivity($"Updated order {order.OrderNumber} status to {newStatus}", currentUser.UserId);
                        ShowSuccessMessage($"Order status updated to {newStatus} successfully!");
                        
                        LoadOrders();
                        LoadKanbanOrders();
                        UpdateStats();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating order status");
                ShowErrorMessage("Error updating order status. Please try again.");
            }
        }

        [WebMethod]
        public static object GetOrderDetails(int orderId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin()))
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.Find(orderId);
                    if (order == null)
                    {
                        return new { success = false, message = "Order not found" };
                    }

                    var html = GenerateOrderDetailsHtml(order, db);
                    var actions = GenerateOrderActionsHtml(order);
                    return new { success = true, html = html, actions = actions };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting order details");
                return new { success = false, message = "Error loading order details" };
            }
        }

        [WebMethod]
        public static object UpdateOrderStatus(int orderId, string newStatus)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin()))
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.Find(orderId);
                    if (order == null)
                    {
                        return new { success = false, message = "Order not found" };
                    }

                    order.Status = newStatus;
                    order.ModifiedDate = DateTime.Now;
                    order.ModifiedBy = currentUser.UserId;

                    // Set delivery date if delivered
                    if (newStatus == "Delivered")
                    {
                        order.ActualDeliveryDate = DateTime.Now;
                    }

                    db.SaveChanges();

                    ErrorLogger.LogUserActivity($"Updated order {order.OrderNumber} status to {newStatus}", currentUser.UserId);
                    return new { success = true, message = $"Order status updated to {newStatus} successfully" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating order status");
                return new { success = false, message = "Error updating order status" };
            }
        }

        private static string GenerateOrderDetailsHtml(Order order, MediEaseContext db)
        {
            var orderItems = db.OrderItems.Where(oi => oi.OrderId == order.OrderId).ToList();
            
            var itemsHtml = string.Join("", orderItems.Select(item => $@"
                <tr>
                    <td>{item.Medicine.Name}</td>
                    <td>{item.Quantity}</td>
                    <td>${item.UnitPrice:F2}</td>
                    <td>${item.TotalPrice:F2}</td>
                </tr>"));

            return $@"
                <div class='row'>
                    <div class='col-md-6'>
                        <h6>Order Information</h6>
                        <p><strong>Order Number:</strong> {order.OrderNumber}</p>
                        <p><strong>Order Date:</strong> {order.OrderDate:MMM dd, yyyy hh:mm tt}</p>
                        <p><strong>Status:</strong> <span class='badge bg-primary'>{order.Status}</span></p>
                        <p><strong>Payment Method:</strong> {order.PaymentMethod}</p>
                        <p><strong>Payment Status:</strong> {order.PaymentStatus}</p>
                        {(order.IsUrgent ? "<p><span class='badge bg-danger'>URGENT ORDER</span></p>" : "")}
                    </div>
                    <div class='col-md-6'>
                        <h6>Customer Information</h6>
                        <p><strong>Name:</strong> {order.Customer.FirstName} {order.Customer.LastName}</p>
                        <p><strong>Email:</strong> {order.Customer.Email}</p>
                        <p><strong>Phone:</strong> {order.Customer.PhoneNumber}</p>
                        <p><strong>Address:</strong><br>{order.ShippingAddress}<br>{order.ShippingCity}, {order.ShippingPostalCode}</p>
                    </div>
                </div>
                <div class='row mt-3'>
                    <div class='col-12'>
                        <h6>Order Items</h6>
                        <table class='table table-sm'>
                            <thead>
                                <tr>
                                    <th>Medicine</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {itemsHtml}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan='3'>Total Amount:</th>
                                    <th>${order.TotalAmount:F2}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>";
        }

        private static string GenerateOrderActionsHtml(Order order)
        {
            var actions = new List<string>();

            switch (order.Status)
            {
                case "Pending":
                    actions.Add($"<button class='btn btn-primary' onclick='updateOrderStatus({order.OrderId}, \"Processing\")'>Start Processing</button>");
                    break;
                case "Processing":
                    actions.Add($"<button class='btn btn-success' onclick='updateOrderStatus({order.OrderId}, \"Verified\")'>Verify Order</button>");
                    break;
                case "Verified":
                    actions.Add($"<button class='btn btn-info' onclick='updateOrderStatus({order.OrderId}, \"Packed\")'>Mark as Packed</button>");
                    break;
                case "Packed":
                    actions.Add($"<button class='btn btn-warning' onclick='updateOrderStatus({order.OrderId}, \"Shipped\")'>Ship Order</button>");
                    break;
                case "Shipped":
                    actions.Add($"<button class='btn btn-success' onclick='updateOrderStatus({order.OrderId}, \"Delivered\")'>Mark as Delivered</button>");
                    break;
            }

            return string.Join(" ", actions);
        }

        // Helper methods for data binding
        protected string GetStatusColor(string status)
        {
            switch (status?.ToLower())
            {
                case "pending": return "warning";
                case "processing": return "info";
                case "verified": return "primary";
                case "packed": return "secondary";
                case "shipped": return "dark";
                case "delivered": return "success";
                case "cancelled": return "danger";
                default: return "secondary";
            }
        }

        protected string GetActionButtons(string status, object orderId)
        {
            var id = orderId.ToString();
            switch (status)
            {
                case "Pending":
                    return $"<asp:LinkButton runat='server' CssClass='btn btn-primary btn-sm' CommandName='Process' CommandArgument='{id}'><i class='fas fa-play me-1'></i>Process</asp:LinkButton>";
                case "Processing":
                    return $"<asp:LinkButton runat='server' CssClass='btn btn-success btn-sm' CommandName='Verify' CommandArgument='{id}'><i class='fas fa-check me-1'></i>Verify</asp:LinkButton>";
                case "Verified":
                    return $"<asp:LinkButton runat='server' CssClass='btn btn-info btn-sm' CommandName='Pack' CommandArgument='{id}'><i class='fas fa-box me-1'></i>Pack</asp:LinkButton>";
                case "Packed":
                    return $"<asp:LinkButton runat='server' CssClass='btn btn-warning btn-sm' CommandName='Ship' CommandArgument='{id}'><i class='fas fa-truck me-1'></i>Ship</asp:LinkButton>";
                case "Shipped":
                    return $"<asp:LinkButton runat='server' CssClass='btn btn-success btn-sm' CommandName='Deliver' CommandArgument='{id}'><i class='fas fa-check-circle me-1'></i>Deliver</asp:LinkButton>";
                default:
                    return $"<button class='btn btn-outline-info btn-sm view-order' data-order-id='{id}'><i class='fas fa-eye me-1'></i>View</button>";
            }
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Order Management - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Manage and process customer orders with MediEase pharmacist order management system.");
                master.AddMetaKeywords("order management, pharmacist tools, order processing, pharmacy orders");
            }
        }
    }
}
