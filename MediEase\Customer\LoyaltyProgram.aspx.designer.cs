//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Customer
{
    public partial class LoyaltyProgram
    {
        /// <summary>
        /// lblCurrentPoints control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblCurrentPoints;

        /// <summary>
        /// lblPointsValue control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPointsValue;

        /// <summary>
        /// lblTierName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTierName;

        /// <summary>
        /// lblPointsToNextReward control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPointsToNextReward;

        /// <summary>
        /// btnViewRewards control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnViewRewards;

        /// <summary>
        /// rptRewards control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptRewards;

        /// <summary>
        /// gvPointsHistory control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvPointsHistory;
    }
}
