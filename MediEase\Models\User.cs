using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MediEase.Models
{
    [Table("Users")]
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required(ErrorMessage = "First name is required")]
        [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
        [Display(Name = "First Name")]
        public string FirstName { get; set; }

        [Required(ErrorMessage = "Last name is required")]
        [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
        [Display(Name = "Last Name")]
        public string LastName { get; set; }

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        [Index(IsUnique = true)]
        public string Email { get; set; }

        [Required(ErrorMessage = "Password is required")]
        [StringLength(255, ErrorMessage = "Password hash cannot exceed 255 characters")]
        public string PasswordHash { get; set; }

        [Required(ErrorMessage = "Phone number is required")]
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(15, ErrorMessage = "Phone number cannot exceed 15 characters")]
        [Display(Name = "Phone Number")]
        public string PhoneNumber { get; set; }

        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; }

        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string City { get; set; }

        [StringLength(100, ErrorMessage = "State cannot exceed 100 characters")]
        public string State { get; set; }

        [StringLength(20, ErrorMessage = "Postal code cannot exceed 20 characters")]
        [Display(Name = "Postal Code")]
        public string PostalCode { get; set; }

        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string Country { get; set; }

        [Required]
        [StringLength(20)]
        public string Role { get; set; } // Customer, Pharmacist, Admin

        [Display(Name = "Date of Birth")]
        [DataType(DataType.Date)]
        public DateTime? DateOfBirth { get; set; }

        [StringLength(10)]
        public string Gender { get; set; } // Male, Female, Other

        [Display(Name = "Emergency Contact")]
        [StringLength(100)]
        public string EmergencyContact { get; set; }

        [Display(Name = "Emergency Phone")]
        [StringLength(15)]
        public string EmergencyPhone { get; set; }

        [Display(Name = "Profile Picture")]
        [StringLength(255)]
        public string ProfilePicture { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Email Verified")]
        public bool IsEmailVerified { get; set; } = false;

        [Display(Name = "Phone Verified")]
        public bool IsPhoneVerified { get; set; } = false;

        [Display(Name = "Two Factor Enabled")]
        public bool TwoFactorEnabled { get; set; } = false;

        [Display(Name = "Loyalty Points")]
        public int LoyaltyPoints { get; set; } = 0;

        [Display(Name = "Preferred Language")]
        [StringLength(10)]
        public string PreferredLanguage { get; set; } = "en";

        [Display(Name = "Notification Preferences")]
        [StringLength(100)]
        public string NotificationPreferences { get; set; } = "email,sms";

        // Health Information
        [Display(Name = "Blood Type")]
        [StringLength(5)]
        public string BloodType { get; set; }

        [Display(Name = "Known Allergies")]
        [StringLength(1000)]
        public string Allergies { get; set; }

        [Display(Name = "Current Medications")]
        [StringLength(1000)]
        public string CurrentMedications { get; set; }

        // Notification Settings
        [Display(Name = "Email Notifications")]
        public bool EmailNotifications { get; set; } = true;

        [Display(Name = "SMS Notifications")]
        public bool SMSNotifications { get; set; } = false;

        [Display(Name = "Newsletter Subscription")]
        public bool NewsletterSubscription { get; set; } = false;

        [Display(Name = "Auto-refill Reminders")]
        public bool AutoRefillReminders { get; set; } = false;

        [Display(Name = "Profile Image Path")]
        [StringLength(255)]
        public string ProfileImagePath { get; set; }

        [Display(Name = "Last Login")]
        public DateTime? LastLogin { get; set; }

        [Display(Name = "Failed Login Attempts")]
        public int FailedLoginAttempts { get; set; } = 0;

        [Display(Name = "Account Locked Until")]
        public DateTime? AccountLockedUntil { get; set; }

        [Display(Name = "Password Reset Token")]
        [StringLength(255)]
        public string PasswordResetToken { get; set; }

        [Display(Name = "Password Reset Expires")]
        public DateTime? PasswordResetExpires { get; set; }

        [Display(Name = "Email Verification Token")]
        [StringLength(255)]
        public string EmailVerificationToken { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "Created By")]
        public int? CreatedBy { get; set; }

        [Display(Name = "Modified By")]
        public int? ModifiedBy { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "Full Name")]
        public string FullName => $"{FirstName} {LastName}";

        [NotMapped]
        [Display(Name = "Is Account Locked")]
        public bool IsAccountLocked => AccountLockedUntil.HasValue && AccountLockedUntil > DateTime.Now;

        [NotMapped]
        [Display(Name = "Age")]
        public int? Age
        {
            get
            {
                if (!DateOfBirth.HasValue) return null;
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Value.Year;
                if (DateOfBirth.Value.Date > today.AddYears(-age)) age--;
                return age;
            }
        }
    }

    public enum UserRole
    {
        Customer,
        Pharmacist,
        Admin
    }

    public enum Gender
    {
        Male,
        Female,
        Other
    }
}
