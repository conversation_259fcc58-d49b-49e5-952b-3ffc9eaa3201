using System;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase.Customer
{
    public partial class Dashboard : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsCustomer() && !SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadDashboardData();
            }
        }

        private void LoadDashboardData()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    // Load user information
                    var user = db.Users.Find(currentUser.UserId);
                    if (user != null)
                    {
                        litUserName.Text = user.FirstName;
                        litLoyaltyPoints.Text = user.LoyaltyPoints.ToString();
                    }

                    // Load dashboard statistics
                    LoadDashboardStats(db, currentUser.UserId);

                    // Load recent orders
                    LoadRecentOrders(db, currentUser.UserId);

                    // Load recent prescriptions
                    LoadRecentPrescriptions(db, currentUser.UserId);

                    // Load health reminders
                    LoadHealthReminders(db, currentUser.UserId);

                    // Log dashboard access
                    ErrorLogger.LogUserActivity("Accessed customer dashboard", currentUser.UserId);
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading customer dashboard data");
                ShowErrorMessage("Error loading dashboard data. Please refresh the page.");
            }
        }

        private void LoadDashboardStats(MediEaseContext db, int userId)
        {
            try
            {
                // Total orders
                var totalOrders = db.Orders.Count(o => o.CustomerId == userId);
                litTotalOrders.Text = totalOrders.ToString();

                // Active prescriptions
                var activePrescriptions = db.Prescriptions.Count(p => 
                    p.PatientId == userId && 
                    p.Status == "Verified" && 
                    (!p.ValidUntil.HasValue || p.ValidUntil.Value >= DateTime.Today));
                litActivePrescriptions.Text = activePrescriptions.ToString();

                // Pending deliveries
                var pendingDeliveries = db.Orders.Count(o => 
                    o.CustomerId == userId && 
                    (o.Status == "Processing" || o.Status == "Shipped" || o.Status == "OutForDelivery"));
                litPendingDeliveries.Text = pendingDeliveries.ToString();

                // Medication reminders (placeholder - would need a reminders table)
                litReminders.Text = "0"; // TODO: Implement medication reminders
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading dashboard statistics");
                // Set default values if there's an error
                litTotalOrders.Text = "0";
                litActivePrescriptions.Text = "0";
                litPendingDeliveries.Text = "0";
                litReminders.Text = "0";
            }
        }

        private void LoadRecentOrders(MediEaseContext db, int userId)
        {
            try
            {
                var recentOrders = db.Orders
                    .Where(o => o.CustomerId == userId)
                    .OrderByDescending(o => o.OrderDate)
                    .Take(5)
                    .Select(o => new
                    {
                        o.OrderNumber,
                        o.OrderDate,
                        o.TotalAmount,
                        o.Status
                    })
                    .ToList();

                if (recentOrders.Any())
                {
                    rptRecentOrders.DataSource = recentOrders;
                    rptRecentOrders.DataBind();
                    pnlNoOrders.Visible = false;
                }
                else
                {
                    pnlNoOrders.Visible = true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading recent orders");
                pnlNoOrders.Visible = true;
            }
        }

        private void LoadRecentPrescriptions(MediEaseContext db, int userId)
        {
            try
            {
                var recentPrescriptions = db.Prescriptions
                    .Where(p => p.PatientId == userId)
                    .OrderByDescending(p => p.PrescriptionDate)
                    .Take(5)
                    .Select(p => new
                    {
                        p.PrescriptionNumber,
                        p.DoctorName,
                        p.PrescriptionDate,
                        p.Status
                    })
                    .ToList();

                if (recentPrescriptions.Any())
                {
                    rptRecentPrescriptions.DataSource = recentPrescriptions;
                    rptRecentPrescriptions.DataBind();
                    pnlNoPrescriptions.Visible = false;
                }
                else
                {
                    pnlNoPrescriptions.Visible = true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading recent prescriptions");
                pnlNoPrescriptions.Visible = true;
            }
        }

        private void LoadHealthReminders(MediEaseContext db, int userId)
        {
            try
            {
                // This is a placeholder implementation
                // In a real application, you would have a medication reminders table
                var reminders = new object[0].ToList();

                if (reminders.Any())
                {
                    rptHealthReminders.DataSource = reminders;
                    rptHealthReminders.DataBind();
                    pnlNoReminders.Visible = false;
                }
                else
                {
                    pnlNoReminders.Visible = true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading health reminders");
                pnlNoReminders.Visible = true;
            }
        }

        // Helper methods for data binding
        protected string GetStatusBadgeClass(string status)
        {
            if (string.IsNullOrEmpty(status))
                return "secondary";

            switch (status.ToLower())
            {
                case "pending":
                    return "warning";
                case "processing":
                    return "info";
                case "shipped":
                    return "primary";
                case "delivered":
                    return "success";
                case "cancelled":
                    return "danger";
                default:
                    return "secondary";
            }
        }

        protected string GetPrescriptionStatusBadgeClass(string status)
        {
            if (string.IsNullOrEmpty(status))
                return "secondary";

            switch (status.ToLower())
            {
                case "pending":
                    return "warning";
                case "verified":
                    return "success";
                case "dispensed":
                    return "info";
                case "expired":
                    return "danger";
                case "rejected":
                    return "danger";
                default:
                    return "secondary";
            }
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Dashboard - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Customer dashboard for MediEase pharmacy management system. View your orders, prescriptions, and health reminders.");
                master.AddMetaKeywords("customer dashboard, orders, prescriptions, health reminders, pharmacy account");
            }
        }
    }
}
