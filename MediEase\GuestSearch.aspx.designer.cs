//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase
{
    public partial class GuestSearch
    {
        /// <summary>
        /// txtSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtSearch;

        /// <summary>
        /// btnSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnSearch;

        /// <summary>
        /// ddlCategory control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlCategory;

        /// <summary>
        /// ddlSortBy control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlSortBy;

        /// <summary>
        /// lblResultCount control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblResultCount;

        /// <summary>
        /// rptMedicines control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptMedicines;

        /// <summary>
        /// pnlNoResults control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlNoResults;

        /// <summary>
        /// pnlPagination control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlPagination;

        /// <summary>
        /// rptPagination control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptPagination;
    }
}
