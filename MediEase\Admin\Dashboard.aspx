<%@ Page Title="Admin Dashboard" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Dashboard.aspx.cs" Inherits="MediEase.Admin.Dashboard" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">Admin Dashboard</h2>
                        <p class="text-muted">Manage your pharmacy system</p>
                    </div>
                    <div>
                        <asp:Button ID="btnRefresh" runat="server" Text="Refresh Data" CssClass="btn btn-outline-primary" OnClick="btnRefresh_Click" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Users</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <asp:Label ID="lblTotalUsers" runat="server" Text="0"></asp:Label>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Medicines</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <asp:Label ID="lblTotalMedicines" runat="server" Text="0"></asp:Label>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-pills fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Orders</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <asp:Label ID="lblTotalOrders" runat="server" Text="0"></asp:Label>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Revenue (This Month)</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    $<asp:Label ID="lblMonthlyRevenue" runat="server" Text="0.00"></asp:Label>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <asp:LinkButton ID="lnkManageUsers" runat="server" CssClass="btn btn-primary btn-block" OnClick="lnkManageUsers_Click">
                                    <i class="fas fa-users me-2"></i>Manage Users
                                </asp:LinkButton>
                            </div>
                            <div class="col-md-3 mb-3">
                                <asp:LinkButton ID="lnkManageMedicines" runat="server" CssClass="btn btn-success btn-block" OnClick="lnkManageMedicines_Click">
                                    <i class="fas fa-pills me-2"></i>Manage Medicines
                                </asp:LinkButton>
                            </div>
                            <div class="col-md-3 mb-3">
                                <asp:LinkButton ID="lnkViewOrders" runat="server" CssClass="btn btn-info btn-block" OnClick="lnkViewOrders_Click">
                                    <i class="fas fa-shopping-cart me-2"></i>View Orders
                                </asp:LinkButton>
                            </div>
                            <div class="col-md-3 mb-3">
                                <asp:LinkButton ID="lnkSystemSettings" runat="server" CssClass="btn btn-warning btn-block" OnClick="lnkSystemSettings_Click">
                                    <i class="fas fa-cog me-2"></i>System Settings
                                </asp:LinkButton>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity & Low Stock Alerts -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvRecentOrders" runat="server" CssClass="table table-striped" AutoGenerateColumns="false" EmptyDataText="No recent orders found.">
                            <Columns>
                                <asp:BoundField DataField="OrderNumber" HeaderText="Order #" />
                                <asp:BoundField DataField="CustomerName" HeaderText="Customer" />
                                <asp:BoundField DataField="TotalAmount" HeaderText="Amount" DataFormatString="{0:C}" />
                                <asp:BoundField DataField="Status" HeaderText="Status" />
                                <asp:BoundField DataField="OrderDate" HeaderText="Date" DataFormatString="{0:MM/dd/yyyy}" />
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-danger">Low Stock Alerts</h6>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvLowStock" runat="server" CssClass="table table-striped" AutoGenerateColumns="false" EmptyDataText="No low stock items.">
                            <Columns>
                                <asp:BoundField DataField="Name" HeaderText="Medicine" />
                                <asp:BoundField DataField="StockQuantity" HeaderText="Current Stock" />
                                <asp:BoundField DataField="MinimumStockLevel" HeaderText="Min Level" />
                                <asp:TemplateField HeaderText="Action">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lnkRestock" runat="server" CssClass="btn btn-sm btn-outline-primary" 
                                            CommandArgument='<%# Eval("MedicineId") %>' OnClick="lnkRestock_Click">
                                            Restock
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .border-left-primary { border-left: 0.25rem solid #4e73df !important; }
        .border-left-success { border-left: 0.25rem solid #1cc88a !important; }
        .border-left-info { border-left: 0.25rem solid #36b9cc !important; }
        .border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
        .text-gray-800 { color: #5a5c69 !important; }
        .text-gray-300 { color: #dddfeb !important; }
    </style>
</asp:Content>
