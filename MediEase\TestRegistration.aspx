<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestRegistration.aspx.cs" Inherits="MediEase.TestRegistration" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3>Test Registration & Database</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Test Database Connection</h5>
                                    <asp:Button ID="btnTestDB" runat="server" Text="Test Database" 
                                               CssClass="btn btn-primary mb-3" OnClick="btnTestDB_Click" />
                                    <br />
                                    
                                    <h5>Quick Registration Test</h5>
                                    <div class="mb-3">
                                        <label class="form-label">Email:</label>
                                        <asp:TextBox ID="txtTestEmail" runat="server" CssClass="form-control" 
                                                    Text="<EMAIL>" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Password:</label>
                                        <asp:TextBox ID="txtTestPassword" runat="server" CssClass="form-control" 
                                                    TextMode="Password" Text="Test123!@#" />
                                    </div>
                                    <asp:Button ID="btnTestRegister" runat="server" Text="Test Register User" 
                                               CssClass="btn btn-success mb-3" OnClick="btnTestRegister_Click" />
                                    <br />
                                    <asp:Button ID="btnTestLogin" runat="server" Text="Test Login User" 
                                               CssClass="btn btn-info mb-3" OnClick="btnTestLogin_Click" />
                                </div>
                                <div class="col-md-6">
                                    <h5>Results:</h5>
                                    <asp:Literal ID="litResults" runat="server"></asp:Literal>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</body>
</html>
