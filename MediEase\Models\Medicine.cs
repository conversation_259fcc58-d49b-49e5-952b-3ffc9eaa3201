using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MediEase.Models
{
    [Table("Medicines")]
    public class Medicine
    {
        [Key]
        public int MedicineId { get; set; }

        [Required(ErrorMessage = "Medicine name is required")]
        [StringLength(200, ErrorMessage = "Medicine name cannot exceed 200 characters")]
        [Display(Name = "Medicine Name")]
        public string Name { get; set; }

        [Required(ErrorMessage = "Generic name is required")]
        [StringLength(200, ErrorMessage = "Generic name cannot exceed 200 characters")]
        [Display(Name = "Generic Name")]
        public string GenericName { get; set; }

        [Required(ErrorMessage = "Brand is required")]
        [StringLength(100, ErrorMessage = "Brand cannot exceed 100 characters")]
        public string Brand { get; set; }

        [Required(ErrorMessage = "Category is required")]
        [StringLength(100, ErrorMessage = "Category cannot exceed 100 characters")]
        public string Category { get; set; }

        [StringLength(50, ErrorMessage = "Sub-category cannot exceed 50 characters")]
        [Display(Name = "Sub Category")]
        public string SubCategory { get; set; }

        [Required(ErrorMessage = "Dosage form is required")]
        [StringLength(50, ErrorMessage = "Dosage form cannot exceed 50 characters")]
        [Display(Name = "Dosage Form")]
        public string DosageForm { get; set; } // Tablet, Capsule, Syrup, Injection, etc.

        [Required(ErrorMessage = "Strength is required")]
        [StringLength(50, ErrorMessage = "Strength cannot exceed 50 characters")]
        public string Strength { get; set; } // 500mg, 10ml, etc.

        [Required(ErrorMessage = "Pack size is required")]
        [Display(Name = "Pack Size")]
        public int PackSize { get; set; } // Number of units per pack

        [StringLength(50, ErrorMessage = "Unit cannot exceed 50 characters")]
        public string Unit { get; set; } // tablets, capsules, ml, etc.

        [Required(ErrorMessage = "Price is required")]
        [Range(0.01, 999999.99, ErrorMessage = "Price must be between 0.01 and 999999.99")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Price { get; set; }

        [Range(0.00, 999999.99, ErrorMessage = "Discount must be between 0.00 and 999999.99")]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "Discount Amount")]
        public decimal DiscountAmount { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Discount percentage must be between 0 and 100")]
        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "Discount Percentage")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Required(ErrorMessage = "Stock quantity is required")]
        [Range(0, int.MaxValue, ErrorMessage = "Stock quantity cannot be negative")]
        [Display(Name = "Stock Quantity")]
        public int StockQuantity { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Minimum stock level cannot be negative")]
        [Display(Name = "Minimum Stock Level")]
        public int MinimumStockLevel { get; set; } = 10;

        [Range(0, int.MaxValue, ErrorMessage = "Maximum stock level cannot be negative")]
        [Display(Name = "Maximum Stock Level")]
        public int MaximumStockLevel { get; set; } = 1000;

        [StringLength(50, ErrorMessage = "Batch number cannot exceed 50 characters")]
        [Display(Name = "Batch Number")]
        public string BatchNumber { get; set; }

        [Display(Name = "Manufacturing Date")]
        [DataType(DataType.Date)]
        public DateTime? ManufacturingDate { get; set; }

        [Display(Name = "Expiry Date")]
        [DataType(DataType.Date)]
        public DateTime? ExpiryDate { get; set; }

        [Required(ErrorMessage = "Manufacturer is required")]
        [StringLength(200, ErrorMessage = "Manufacturer cannot exceed 200 characters")]
        public string Manufacturer { get; set; }

        [StringLength(100, ErrorMessage = "Supplier cannot exceed 100 characters")]
        public string Supplier { get; set; }

        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string Description { get; set; }

        [StringLength(2000, ErrorMessage = "Indications cannot exceed 2000 characters")]
        public string Indications { get; set; } // What it's used for

        [StringLength(2000, ErrorMessage = "Contraindications cannot exceed 2000 characters")]
        public string Contraindications { get; set; } // When not to use

        [StringLength(2000, ErrorMessage = "Side effects cannot exceed 2000 characters")]
        [Display(Name = "Side Effects")]
        public string SideEffects { get; set; }

        [StringLength(1000, ErrorMessage = "Dosage instructions cannot exceed 1000 characters")]
        [Display(Name = "Dosage Instructions")]
        public string DosageInstructions { get; set; }

        [StringLength(1000, ErrorMessage = "Storage instructions cannot exceed 1000 characters")]
        [Display(Name = "Storage Instructions")]
        public string StorageInstructions { get; set; }

        [Display(Name = "Prescription Required")]
        public bool PrescriptionRequired { get; set; } = false;

        [Display(Name = "Controlled Substance")]
        public bool ControlledSubstance { get; set; } = false;

        [StringLength(20, ErrorMessage = "Schedule cannot exceed 20 characters")]
        public string Schedule { get; set; } // For controlled substances

        [StringLength(255, ErrorMessage = "Image path cannot exceed 255 characters")]
        [Display(Name = "Image Path")]
        public string ImagePath { get; set; }

        [StringLength(50, ErrorMessage = "Barcode cannot exceed 50 characters")]
        public string Barcode { get; set; }

        [StringLength(50, ErrorMessage = "SKU cannot exceed 50 characters")]
        public string SKU { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Is Featured")]
        public bool IsFeatured { get; set; } = false;

        [Display(Name = "Is On Sale")]
        public bool IsOnSale { get; set; } = false;

        [Range(0, 5, ErrorMessage = "Rating must be between 0 and 5")]
        [Column(TypeName = "decimal(3,2)")]
        [Display(Name = "Average Rating")]
        public decimal AverageRating { get; set; } = 0;

        [Display(Name = "Review Count")]
        public int ReviewCount { get; set; } = 0;

        [Display(Name = "Purchase Count")]
        public int PurchaseCount { get; set; } = 0;

        [Display(Name = "View Count")]
        public int ViewCount { get; set; } = 0;

        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string Tags { get; set; } // Comma-separated search tags

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "Modified Date")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "Created By")]
        public int? CreatedBy { get; set; }

        [Display(Name = "Modified By")]
        public int? ModifiedBy { get; set; }

        // Computed Properties
        [NotMapped]
        [Display(Name = "Final Price")]
        public decimal FinalPrice
        {
            get
            {
                var discountAmount = DiscountAmount;
                if (DiscountPercentage > 0)
                {
                    discountAmount = Math.Max(discountAmount, Price * (DiscountPercentage / 100));
                }
                return Math.Max(0, Price - discountAmount);
            }
        }

        [NotMapped]
        [Display(Name = "Is In Stock")]
        public bool IsInStock => StockQuantity > 0;

        [NotMapped]
        [Display(Name = "Is Low Stock")]
        public bool IsLowStock => StockQuantity <= MinimumStockLevel && StockQuantity > 0;

        [NotMapped]
        [Display(Name = "Is Out of Stock")]
        public bool IsOutOfStock => StockQuantity <= 0;

        [NotMapped]
        [Display(Name = "Is Expired")]
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Today;

        [NotMapped]
        [Display(Name = "Is Near Expiry")]
        public bool IsNearExpiry => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Today.AddDays(30) && !IsExpired;

        [NotMapped]
        [Display(Name = "Days Until Expiry")]
        public int? DaysUntilExpiry
        {
            get
            {
                if (!ExpiryDate.HasValue) return null;
                return (int)(ExpiryDate.Value - DateTime.Today).TotalDays;
            }
        }

        [NotMapped]
        [Display(Name = "Stock Status")]
        public string StockStatus
        {
            get
            {
                if (IsOutOfStock) return "Out of Stock";
                if (IsLowStock) return "Low Stock";
                return "In Stock";
            }
        }
    }

    public enum DosageForm
    {
        Tablet,
        Capsule,
        Syrup,
        Injection,
        Cream,
        Ointment,
        Drops,
        Inhaler,
        Patch,
        Suppository
    }

    public enum MedicineCategory
    {
        Analgesics,
        Antibiotics,
        Antacids,
        Antihistamines,
        Antihypertensives,
        Antidiabetics,
        Vitamins,
        Supplements,
        Skincare,
        Respiratory,
        Cardiovascular,
        Gastrointestinal,
        Neurological,
        Hormonal,
        Other
    }
}
