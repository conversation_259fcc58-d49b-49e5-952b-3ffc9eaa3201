using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase
{
    public partial class GuestSearch : Page
    {
        private const int PageSize = 12;
        public int CurrentPage { get; set; } = 1;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadCategories();
                LoadMedicines();
            }
        }

        private void LoadCategories()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var categories = db.Categories
                        .Where(c => c.IsActive)
                        .OrderBy(c => c.Name)
                        .Select(c => new { c.CategoryId, c.Name })
                        .ToList();

                    ddlCategory.DataSource = categories;
                    ddlCategory.DataTextField = "Name";
                    ddlCategory.DataValueField = "CategoryId";
                    ddlCategory.DataBind();
                    ddlCategory.Items.Insert(0, new ListItem("All Categories", ""));
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading categories for guest search");
            }
        }

        private void LoadMedicines()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var query = db.Medicines.Where(m => m.IsActive);

                    // Apply search filter
                    var searchTerm = txtSearch.Text.Trim();
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(m => 
                            m.Name.Contains(searchTerm) || 
                            m.GenericName.Contains(searchTerm) ||
                            m.Description.Contains(searchTerm) ||
                            m.Category.Contains(searchTerm));
                    }

                    // Apply category filter
                    if (!string.IsNullOrEmpty(ddlCategory.SelectedValue))
                    {
                        var categoryName = ddlCategory.SelectedItem.Text;
                        query = query.Where(m => m.Category == categoryName);
                    }

                    // Apply sorting
                    switch (ddlSortBy.SelectedValue)
                    {
                        case "name":
                            query = query.OrderBy(m => m.Name);
                            break;
                        case "name_desc":
                            query = query.OrderByDescending(m => m.Name);
                            break;
                        case "price":
                            query = query.OrderBy(m => m.FinalPrice);
                            break;
                        case "price_desc":
                            query = query.OrderByDescending(m => m.FinalPrice);
                            break;
                        case "popular":
                            query = query.OrderByDescending(m => m.PurchaseCount);
                            break;
                        default:
                            query = query.OrderBy(m => m.Name);
                            break;
                    }

                    // Get total count for pagination
                    var totalCount = query.Count();
                    lblResultCount.Text = totalCount.ToString();

                    // Apply pagination
                    var pageNumber = GetCurrentPageNumber();
                    var medicines = query
                        .Skip((pageNumber - 1) * PageSize)
                        .Take(PageSize)
                        .ToList();

                    // Bind data
                    if (medicines.Any())
                    {
                        rptMedicines.DataSource = medicines;
                        rptMedicines.DataBind();
                        pnlNoResults.Visible = false;
                        
                        // Setup pagination
                        SetupPagination(totalCount, pageNumber);
                    }
                    else
                    {
                        rptMedicines.DataSource = null;
                        rptMedicines.DataBind();
                        pnlNoResults.Visible = true;
                        pnlPagination.Visible = false;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading medicines for guest search");
                ShowErrorMessage("Error loading medicines. Please try again.");
            }
        }

        private int GetCurrentPageNumber()
        {
            if (int.TryParse(Request.QueryString["page"], out int page) && page > 0)
            {
                CurrentPage = page;
                return page;
            }
            CurrentPage = 1;
            return 1;
        }

        private void SetupPagination(int totalCount, int currentPage)
        {
            var totalPages = (int)Math.Ceiling((double)totalCount / PageSize);
            
            if (totalPages <= 1)
            {
                pnlPagination.Visible = false;
                return;
            }

            var pages = new System.Collections.Generic.List<object>();
            
            // Add page numbers (show max 10 pages)
            var startPage = Math.Max(1, currentPage - 5);
            var endPage = Math.Min(totalPages, startPage + 9);
            
            for (int i = startPage; i <= endPage; i++)
            {
                pages.Add(new { PageNumber = i });
            }

            rptPagination.DataSource = pages;
            rptPagination.DataBind();
            pnlPagination.Visible = true;
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadMedicines();
        }

        protected void ddlCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadMedicines();
        }

        protected void ddlSortBy_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadMedicines();
        }

        protected void lnkPage_Click(object sender, EventArgs e)
        {
            var linkButton = (LinkButton)sender;
            var pageNumber = Convert.ToInt32(linkButton.CommandArgument);
            
            // Redirect to maintain URL structure
            var url = Request.Url.AbsolutePath + "?page=" + pageNumber;
            
            // Preserve search parameters
            if (!string.IsNullOrEmpty(txtSearch.Text))
                url += "&search=" + Server.UrlEncode(txtSearch.Text);
            if (!string.IsNullOrEmpty(ddlCategory.SelectedValue))
                url += "&category=" + ddlCategory.SelectedValue;
            if (!string.IsNullOrEmpty(ddlSortBy.SelectedValue))
                url += "&sort=" + ddlSortBy.SelectedValue;
                
            Response.Redirect(url);
        }

        protected string GetMedicineImage(object imagePath)
        {
            var path = imagePath?.ToString();
            if (string.IsNullOrEmpty(path))
                return ResolveUrl("~/Images/medicine-placeholder.jpg");
            
            return ResolveUrl("~/Images/Products/" + path);
        }

        private void ShowErrorMessage(string message)
        {
            // Since this is a guest page, we'll use client-side notification
            var script = $"alert('{message.Replace("'", "\\'")}');";
            ClientScript.RegisterStartupScript(this.GetType(), "ErrorMessage", script, true);
        }
    }
}
