<%@ Page Title="Price Comparison Tool" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="PriceComparison.aspx.cs" Inherits="MediEase.Customer.PriceComparison" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-balance-scale me-2"></i>Price Comparison Tool</h2>
                <p class="text-muted">Compare medicine prices across different brands and find the best deals</p>
            </div>
        </div>

        <!-- Search Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Search Medicine</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtMedicineSearch" runat="server" CssClass="form-control" 
                                        placeholder="Enter medicine name or generic name..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" 
                                        Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Category</label>
                                <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlCategory_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Categories</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Sort By</label>
                                <asp:DropDownList ID="ddlSortBy" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlSortBy_SelectedIndexChanged">
                                    <asp:ListItem Value="price_asc">Price: Low to High</asp:ListItem>
                                    <asp:ListItem Value="price_desc">Price: High to Low</asp:ListItem>
                                    <asp:ListItem Value="savings_desc">Best Savings</asp:ListItem>
                                    <asp:ListItem Value="name_asc">Name A-Z</asp:ListItem>
                                    <asp:ListItem Value="popularity">Most Popular</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Search Categories -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tags me-2"></i>Popular Categories</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6 col-md-2">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="searchCategory('Pain Relief')">
                                    <i class="fas fa-pills me-1"></i>Pain Relief
                                </button>
                            </div>
                            <div class="col-6 col-md-2">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="searchCategory('Antibiotics')">
                                    <i class="fas fa-capsules me-1"></i>Antibiotics
                                </button>
                            </div>
                            <div class="col-6 col-md-2">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="searchCategory('Vitamins')">
                                    <i class="fas fa-leaf me-1"></i>Vitamins
                                </button>
                            </div>
                            <div class="col-6 col-md-2">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="searchCategory('Cold & Flu')">
                                    <i class="fas fa-thermometer me-1"></i>Cold & Flu
                                </button>
                            </div>
                            <div class="col-6 col-md-2">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="searchCategory('Diabetes')">
                                    <i class="fas fa-syringe me-1"></i>Diabetes
                                </button>
                            </div>
                            <div class="col-6 col-md-2">
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="searchCategory('Heart Health')">
                                    <i class="fas fa-heartbeat me-1"></i>Heart Health
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparison Results -->
        <asp:Panel ID="pnlResults" runat="server" Visible="false">
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Comparison Results (<asp:Label ID="lblResultCount" runat="server" Text="0"></asp:Label> medicines found)</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="toggleView('comparison')">
                                <i class="fas fa-th-list"></i> Comparison View
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleView('grid')">
                                <i class="fas fa-th"></i> Grid View
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comparison Table View -->
            <div id="comparisonView" class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <asp:GridView ID="gvPriceComparison" runat="server" CssClass="table table-hover" 
                            AutoGenerateColumns="false" EmptyDataText="No medicines found for comparison.">
                            <Columns>
                                <asp:TemplateField HeaderText="Medicine">
                                    <ItemTemplate>
                                        <div class="d-flex align-items-center">
                                            <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' alt='<%# Eval("Name") %>' 
                                                 class="me-3 rounded" style="width: 50px; height: 50px; object-fit: cover;" />
                                            <div>
                                                <h6 class="mb-1"><%# Eval("Name") %></h6>
                                                <small class="text-muted"><%# Eval("GenericName") %></small><br>
                                                <small class="text-muted"><%# Eval("Manufacturer") %></small>
                                            </div>
                                        </div>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                
                                <asp:TemplateField HeaderText="Strength & Form">
                                    <ItemTemplate>
                                        <span class="fw-bold"><%# Eval("Strength") %></span><br>
                                        <small class="text-muted"><%# Eval("DosageForm") %></small>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                
                                <asp:TemplateField HeaderText="Original Price">
                                    <ItemTemplate>
                                        <span class="text-muted text-decoration-line-through">$<%# String.Format("{0:F2}", Eval("Price")) %></span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                
                                <asp:TemplateField HeaderText="Current Price">
                                    <ItemTemplate>
                                        <span class="fw-bold text-primary fs-5">$<%# String.Format("{0:F2}", Eval("FinalPrice")) %></span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                
                                <asp:TemplateField HeaderText="Savings">
                                    <ItemTemplate>
                                        <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ? 
                                            "<span class=\"badge bg-success\">Save " + Eval("DiscountPercentage") + "%</span><br>" +
                                            "<small class=\"text-success\">$" + String.Format("{0:F2}", Convert.ToDecimal(Eval("Price")) - Convert.ToDecimal(Eval("FinalPrice"))) + " off</small>" : 
                                            "<span class=\"text-muted\">No discount</span>" %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                
                                <asp:TemplateField HeaderText="Stock">
                                    <ItemTemplate>
                                        <%# Convert.ToInt32(Eval("StockQuantity")) > 0 ? 
                                            "<span class=\"badge bg-success\">In Stock</span><br><small class=\"text-muted\">" + Eval("StockQuantity") + " available</small>" : 
                                            "<span class=\"badge bg-danger\">Out of Stock</span>" %>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                
                                <asp:TemplateField HeaderText="Action">
                                    <ItemTemplate>
                                        <%# Convert.ToInt32(Eval("StockQuantity")) > 0 ? 
                                            "<button class=\"btn btn-primary btn-sm add-to-cart\" data-medicine-id=\"" + Eval("MedicineId") + "\">" +
                                            "<i class=\"fas fa-cart-plus me-1\"></i>Add to Cart</button>" :
                                            "<button class=\"btn btn-secondary btn-sm\" disabled>Out of Stock</button>" %>
                                        <br>
                                        <button class="btn btn-outline-info btn-sm mt-1 view-details" data-medicine-id='<%# Eval("MedicineId") %>'>
                                            <i class="fas fa-info-circle me-1"></i>Details
                                        </button>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>

            <!-- Grid View (Hidden by default) -->
            <div id="gridView" class="row g-4" style="display: none;">
                <asp:Repeater ID="rptMedicinesGrid" runat="server">
                    <ItemTemplate>
                        <div class="col-lg-4 col-md-6">
                            <div class="card medicine-card h-100 border-0 shadow-sm">
                                <div class="position-relative">
                                    <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' alt='<%# Eval("Name") %>' 
                                         class="card-img-top" style="height: 200px; object-fit: cover;" />
                                    <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                        "<div class=\"position-absolute top-0 end-0 bg-danger text-white px-2 py-1 m-2 rounded\">" + 
                                        Eval("DiscountPercentage") + "% OFF</div>" : "" %>
                                    <%# GetBestDealBadge(Container.DataItem) %>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title"><%# Eval("Name") %></h6>
                                    <p class="card-text text-muted small mb-1"><%# Eval("GenericName") %></p>
                                    <p class="card-text small mb-2"><%# Eval("Manufacturer") %></p>
                                    
                                    <div class="price-section mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="price-current fw-bold text-primary fs-5">$<%# String.Format("{0:F2}", Eval("FinalPrice")) %></span>
                                                <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                                    "<br><small class=\"price-original text-muted text-decoration-line-through\">$" + 
                                                    String.Format("{0:F2}", Eval("Price")) + "</small>" : "" %>
                                            </div>
                                            <div class="text-end">
                                                <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                                    "<span class=\"badge bg-success\">Save $" + 
                                                    String.Format("{0:F2}", Convert.ToDecimal(Eval("Price")) - Convert.ToDecimal(Eval("FinalPrice"))) + "</span>" : "" %>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <div class="d-grid gap-2">
                                            <%# Convert.ToInt32(Eval("StockQuantity")) > 0 ?
                                                "<button class=\"btn btn-primary add-to-cart\" data-medicine-id=\"" + Eval("MedicineId") + "\">" +
                                                "<i class=\"fas fa-cart-plus me-1\"></i>Add to Cart</button>" :
                                                "<button class=\"btn btn-secondary\" disabled>Out of Stock</button>" %>
                                            <button class="btn btn-outline-info btn-sm view-details" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                <i class="fas fa-info-circle me-1"></i>View Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                </asp:Repeater>
            </div>
        </asp:Panel>

        <!-- No Results -->
        <asp:Panel ID="pnlNoResults" runat="server" Visible="false">
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>No medicines found</h4>
                <p class="text-muted">Try adjusting your search criteria or browse popular categories above.</p>
            </div>
        </asp:Panel>

        <!-- Price Alert Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-bell me-2"></i>Price Alert</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">Get notified when medicine prices drop below your target price!</p>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <asp:TextBox ID="txtAlertMedicine" runat="server" CssClass="form-control" placeholder="Medicine name"></asp:TextBox>
                            </div>
                            <div class="col-md-3">
                                <asp:TextBox ID="txtTargetPrice" runat="server" CssClass="form-control" placeholder="Target price ($)" TextMode="Number" step="0.01"></asp:TextBox>
                            </div>
                            <div class="col-md-3">
                                <asp:Button ID="btnSetAlert" runat="server" CssClass="btn btn-info" Text="Set Price Alert" OnClick="btnSetAlert_Click" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function searchCategory(category) {
            document.getElementById('<%= txtMedicineSearch.ClientID %>').value = category;
            document.getElementById('<%= btnSearch.ClientID %>').click();
        }

        function toggleView(viewType) {
            const comparisonView = document.getElementById('comparisonView');
            const gridView = document.getElementById('gridView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'comparison') {
                comparisonView.style.display = 'block';
                gridView.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                comparisonView.style.display = 'none';
                gridView.style.display = 'flex';
                buttons[1].classList.add('active');
            }
        }

        // Add to cart functionality
        $(document).on('click', '.add-to-cart', function() {
            const medicineId = $(this).data('medicine-id');
            addToCart(medicineId, 1);
        });

        // View details functionality
        $(document).on('click', '.view-details', function() {
            const medicineId = $(this).data('medicine-id');
            window.open('/Medicines.aspx?id=' + medicineId, '_blank');
        });
    </script>
</asp:Content>
