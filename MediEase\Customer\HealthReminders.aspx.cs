using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Customer
{
    public partial class HealthReminders : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsCustomer() && !SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadReminders();
                LoadUpcomingReminders();
            }
        }

        private void LoadReminders()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    var reminders = GetUserReminders(db, currentUser.UserId);
                    // Implementation would bind to GridView
                    // gvReminders.DataSource = reminders;
                    // gvReminders.DataBind();

                    // Update statistics would be displayed in UI
                    // lblTotalReminders.Text = reminders.Count.ToString();
                    // lblActiveReminders.Text = reminders.Count(r => r.IsActive).ToString();
                    // lblTodayReminders.Text = reminders.Count(r => r.NextReminderDate.Date == DateTime.Today).ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading health reminders");
                ShowErrorMessage("Error loading reminders. Please try again.");
            }
        }

        private void LoadUpcomingReminders()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    var upcomingReminders = GetUpcomingReminders(db, currentUser.UserId);
                    // Implementation would bind to Repeater
                    // rptUpcomingReminders.DataSource = upcomingReminders;
                    // rptUpcomingReminders.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading upcoming reminders");
                ShowErrorMessage("Error loading upcoming reminders.");
            }
        }

        private List<HealthReminder> GetUserReminders(MediEaseContext db, int userId)
        {
            // Implementation would return user's health reminders
            // For now, return sample data
            return new List<HealthReminder>
            {
                new HealthReminder
                {
                    ReminderId = 1,
                    UserId = userId,
                    Title = "Take Morning Medication",
                    Description = "Take your blood pressure medication",
                    ReminderType = "Medication",
                    NextReminderDate = DateTime.Today.AddHours(8),
                    Frequency = "Daily",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-7)
                },
                new HealthReminder
                {
                    ReminderId = 2,
                    UserId = userId,
                    Title = "Doctor Appointment",
                    Description = "Annual checkup with Dr. Smith",
                    ReminderType = "Appointment",
                    NextReminderDate = DateTime.Today.AddDays(3).AddHours(14),
                    Frequency = "One-time",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-14)
                }
            };
        }

        private List<HealthReminder> GetUpcomingReminders(MediEaseContext db, int userId)
        {
            var reminders = GetUserReminders(db, userId);
            return reminders.Where(r => r.IsActive && r.NextReminderDate >= DateTime.Now && r.NextReminderDate <= DateTime.Now.AddDays(7))
                           .OrderBy(r => r.NextReminderDate)
                           .ToList();
        }

        protected void btnAddReminder_Click(object sender, EventArgs e)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                var reminder = new HealthReminder
                {
                    UserId = currentUser.UserId,
                    Title = "Sample Reminder",
                    Description = "Sample Description",
                    ReminderType = "Medication",
                    NextReminderDate = DateTime.Now.AddHours(1),
                    Frequency = "Daily",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                using (var db = new MediEaseContext())
                {
                    // Implementation would save to database
                    // db.HealthReminders.Add(reminder);
                    // db.SaveChanges();
                }

                ShowSuccessMessage("Health reminder added successfully.");
                ClearForm();
                LoadReminders();
                LoadUpcomingReminders();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error adding health reminder");
                ShowErrorMessage("Error adding reminder. Please try again.");
            }
        }

        protected void gvReminders_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                var reminderId = Convert.ToInt32(e.CommandArgument);

                switch (e.CommandName)
                {
                    case "ToggleActive":
                        ToggleReminderStatus(reminderId);
                        break;
                    case "EditReminder":
                        EditReminder(reminderId);
                        break;
                    case "DeleteReminder":
                        DeleteReminder(reminderId);
                        break;
                }

                LoadReminders();
                LoadUpcomingReminders();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error processing reminder command");
                ShowErrorMessage("Error processing request. Please try again.");
            }
        }

        private void ToggleReminderStatus(int reminderId)
        {
            using (var db = new MediEaseContext())
            {
                // Implementation would toggle reminder active status
                // var reminder = db.HealthReminders.Find(reminderId);
                // if (reminder != null)
                // {
                //     reminder.IsActive = !reminder.IsActive;
                //     db.SaveChanges();
                // }
            }
        }

        private void EditReminder(int reminderId)
        {
            using (var db = new MediEaseContext())
            {
                // Implementation would load reminder for editing
                // var reminder = db.HealthReminders.Find(reminderId);
                // if (reminder != null)
                // {
                //     PopulateEditForm(reminder);
                // }
            }
        }

        private void DeleteReminder(int reminderId)
        {
            using (var db = new MediEaseContext())
            {
                // Implementation would delete reminder
                // var reminder = db.HealthReminders.Find(reminderId);
                // if (reminder != null)
                // {
                //     db.HealthReminders.Remove(reminder);
                //     db.SaveChanges();
                // }
            }
        }

        [WebMethod]
        public static object MarkReminderComplete(int reminderId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Implementation would mark reminder as completed
                    // and schedule next occurrence if recurring
                    return new { success = true, message = "Reminder marked as complete" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error marking reminder complete");
                return new { success = false, message = "Error completing reminder" };
            }
        }

        [WebMethod]
        public static object SnoozeReminder(int reminderId, int snoozeMinutes)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Implementation would snooze reminder
                    return new { success = true, message = $"Reminder snoozed for {snoozeMinutes} minutes" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error snoozing reminder");
                return new { success = false, message = "Error snoozing reminder" };
            }
        }

        private void ClearForm()
        {
            // Implementation would clear form controls
            // txtReminderTitle.Text = "";
            // txtReminderDescription.Text = "";
            // ddlReminderType.SelectedIndex = 0;
            // txtReminderDate.Text = "";
            // txtReminderTime.Text = "";
            // ddlFrequency.SelectedIndex = 0;
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }
    }

    public class HealthReminder
    {
        public int ReminderId { get; set; }
        public int UserId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string ReminderType { get; set; }
        public DateTime NextReminderDate { get; set; }
        public string Frequency { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}
