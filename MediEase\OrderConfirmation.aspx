<%@ Page Title="Order Confirmation" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="OrderConfirmation.aspx.cs" Inherits="MediEase.OrderConfirmation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Success Header -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <div class="alert alert-success border-0 shadow-sm">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h2 class="mb-2">Order Placed Successfully!</h2>
                    <p class="mb-0">Thank you for your order. We'll process it shortly.</p>
                </div>
            </div>
        </div>

        <!-- Order Details -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>Order Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Order Summary -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted">Order Number</h6>
                                <p class="fw-bold fs-5">
                                    <asp:Label ID="lblOrderNumber" runat="server" Text=""></asp:Label>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Order Date</h6>
                                <p class="fw-bold">
                                    <asp:Label ID="lblOrderDate" runat="server" Text=""></asp:Label>
                                </p>
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted">Customer</h6>
                                <p class="mb-1">
                                    <asp:Label ID="lblCustomerName" runat="server" Text=""></asp:Label>
                                </p>
                                <p class="mb-0 text-muted">
                                    <asp:Label ID="lblCustomerEmail" runat="server" Text=""></asp:Label>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Shipping Address</h6>
                                <p class="mb-0">
                                    <asp:Label ID="lblShippingAddress" runat="server" Text=""></asp:Label>
                                </p>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Order Items</h6>
                            <div class="table-responsive">
                                <asp:GridView ID="gvOrderItems" runat="server" CssClass="table table-striped" AutoGenerateColumns="false" EmptyDataText="No items found.">
                                    <Columns>
                                        <asp:BoundField DataField="Medicine.Name" HeaderText="Medicine" />
                                        <asp:BoundField DataField="Medicine.GenericName" HeaderText="Generic Name" />
                                        <asp:BoundField DataField="Quantity" HeaderText="Quantity" />
                                        <asp:BoundField DataField="UnitPrice" HeaderText="Unit Price" DataFormatString="{0:C}" />
                                        <asp:BoundField DataField="TotalPrice" HeaderText="Total" DataFormatString="{0:C}" />
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>

                        <!-- Order Totals -->
                        <div class="row">
                            <div class="col-md-6 ms-auto">
                                <table class="table table-sm">
                                    <tr>
                                        <td>Subtotal:</td>
                                        <td class="text-end">$<asp:Label ID="lblSubtotal" runat="server" Text="0.00"></asp:Label></td>
                                    </tr>
                                    <tr>
                                        <td>Discount:</td>
                                        <td class="text-end text-success">-$<asp:Label ID="lblDiscount" runat="server" Text="0.00"></asp:Label></td>
                                    </tr>
                                    <tr>
                                        <td>Tax:</td>
                                        <td class="text-end">$<asp:Label ID="lblTax" runat="server" Text="0.00"></asp:Label></td>
                                    </tr>
                                    <tr>
                                        <td>Shipping:</td>
                                        <td class="text-end">$<asp:Label ID="lblShipping" runat="server" Text="0.00"></asp:Label></td>
                                    </tr>
                                    <tr class="fw-bold border-top">
                                        <td>Total:</td>
                                        <td class="text-end">$<asp:Label ID="lblTotal" runat="server" Text="0.00"></asp:Label></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted">Payment Method</h6>
                                <p class="mb-0">
                                    <asp:Label ID="lblPaymentMethod" runat="server" Text=""></asp:Label>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Payment Status</h6>
                                <p class="mb-0">
                                    <asp:Label ID="lblPaymentStatus" runat="server" CssClass="badge" Text=""></asp:Label>
                                </p>
                            </div>
                        </div>

                        <!-- Order Status -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted">Order Status</h6>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 25%"></div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small class="text-info fw-bold">Order Placed</small>
                                    <small class="text-muted">Processing</small>
                                    <small class="text-muted">Shipped</small>
                                    <small class="text-muted">Delivered</small>
                                </div>
                            </div>
                        </div>

                        <!-- Prescription Notice -->
                        <asp:Panel ID="pnlPrescriptionNotice" runat="server" Visible="false">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Prescription Required:</strong> Your order contains prescription medicines. 
                                Our pharmacist will verify your prescription before processing the order.
                            </div>
                        </asp:Panel>

                        <!-- Next Steps -->
                        <div class="alert alert-light">
                            <h6 class="alert-heading">What's Next?</h6>
                            <ul class="mb-0">
                                <li>You'll receive an email confirmation shortly</li>
                                <li>Our team will process your order within 24 hours</li>
                                <li>You'll get tracking information once shipped</li>
                                <li>Expected delivery: <asp:Label ID="lblExpectedDelivery" runat="server" Text="3-5 business days"></asp:Label></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mt-4">
                    <asp:LinkButton ID="lnkContinueShopping" runat="server" CssClass="btn btn-outline-primary me-3" OnClick="lnkContinueShopping_Click">
                        <i class="fas fa-shopping-cart me-2"></i>Continue Shopping
                    </asp:LinkButton>
                    <asp:LinkButton ID="lnkViewOrders" runat="server" CssClass="btn btn-primary" OnClick="lnkViewOrders_Click">
                        <i class="fas fa-list me-2"></i>View My Orders
                    </asp:LinkButton>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
