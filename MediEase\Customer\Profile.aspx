<%@ Page Title="My Profile" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Profile.aspx.cs" Inherits="MediEase.Customer.Profile" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-user-edit me-2"></i>My Profile</h2>
                        <p class="text-muted">Manage your personal information and preferences</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-primary" onclick="toggleEditMode()">
                            <i class="fas fa-edit me-2"></i>Edit Profile
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="row">
            <!-- Profile Sidebar -->
            <div class="col-lg-3">
                <div class="card shadow-sm">
                    <div class="card-body text-center">
                        <div class="profile-avatar mb-3">
                            <img id="profileImage" src="~/Images/default-avatar.png" alt="Profile Picture" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;" />
                            <div class="avatar-upload" style="display: none;">
                                <asp:FileUpload ID="fuProfileImage" runat="server" CssClass="d-none" accept="image/*" onchange="previewImage(this)" />
                                <button type="button" class="btn btn-sm btn-primary mt-2" onclick="$('#<%= fuProfileImage.ClientID %>').click()">
                                    <i class="fas fa-camera me-1"></i>Change Photo
                                </button>
                            </div>
                        </div>
                        <h5><asp:Label ID="lblUserName" runat="server" Text=""></asp:Label></h5>
                        <p class="text-muted"><asp:Label ID="lblUserEmail" runat="server" Text=""></asp:Label></p>
                        <div class="badge bg-success mb-2">Customer</div>
                        <div class="text-muted small">
                            Member since: <asp:Label ID="lblMemberSince" runat="server" Text=""></asp:Label>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card shadow-sm mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Quick Stats</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Orders:</span>
                            <strong><asp:Label ID="lblTotalOrders" runat="server" Text="0"></asp:Label></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Loyalty Points:</span>
                            <strong class="text-success"><asp:Label ID="lblLoyaltyPoints" runat="server" Text="0"></asp:Label></strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Prescriptions:</span>
                            <strong><asp:Label ID="lblTotalPrescriptions" runat="server" Text="0"></asp:Label></strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Form -->
            <div class="col-lg-9">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">First Name</label>
                                    <asp:TextBox ID="txtFirstName" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Last Name</label>
                                    <asp:TextBox ID="txtLastName" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email Address</label>
                                    <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Phone Number</label>
                                    <asp:TextBox ID="txtPhoneNumber" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Date of Birth</label>
                                    <asp:TextBox ID="txtDateOfBirth" runat="server" CssClass="form-control" TextMode="Date" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Gender</label>
                                    <asp:DropDownList ID="ddlGender" runat="server" CssClass="form-select" Enabled="false">
                                        <asp:ListItem Value="">Select Gender</asp:ListItem>
                                        <asp:ListItem Value="Male">Male</asp:ListItem>
                                        <asp:ListItem Value="Female">Female</asp:ListItem>
                                        <asp:ListItem Value="Other">Other</asp:ListItem>
                                        <asp:ListItem Value="PreferNotToSay">Prefer not to say</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="card shadow-sm mt-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Address Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Street Address</label>
                            <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" ReadOnly="true"></asp:TextBox>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">City</label>
                                    <asp:TextBox ID="txtCity" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Postal Code</label>
                                    <asp:TextBox ID="txtPostalCode" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Country</label>
                                    <asp:TextBox ID="txtCountry" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Health Information -->
                <div class="card shadow-sm mt-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>Health Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Blood Type</label>
                                    <asp:DropDownList ID="ddlBloodType" runat="server" CssClass="form-select" Enabled="false">
                                        <asp:ListItem Value="">Select Blood Type</asp:ListItem>
                                        <asp:ListItem Value="A+">A+</asp:ListItem>
                                        <asp:ListItem Value="A-">A-</asp:ListItem>
                                        <asp:ListItem Value="B+">B+</asp:ListItem>
                                        <asp:ListItem Value="B-">B-</asp:ListItem>
                                        <asp:ListItem Value="AB+">AB+</asp:ListItem>
                                        <asp:ListItem Value="AB-">AB-</asp:ListItem>
                                        <asp:ListItem Value="O+">O+</asp:ListItem>
                                        <asp:ListItem Value="O-">O-</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Emergency Contact</label>
                                    <asp:TextBox ID="txtEmergencyContact" runat="server" CssClass="form-control" ReadOnly="true"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Known Allergies</label>
                            <asp:TextBox ID="txtAllergies" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" ReadOnly="true" placeholder="List any known allergies..."></asp:TextBox>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Current Medications</label>
                            <asp:TextBox ID="txtCurrentMedications" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" ReadOnly="true" placeholder="List current medications..."></asp:TextBox>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="card shadow-sm mt-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Preferences</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <asp:CheckBox ID="chkEmailNotifications" runat="server" CssClass="form-check-input" Enabled="false" />
                                    <label class="form-check-label">Email Notifications</label>
                                </div>
                                <div class="form-check mb-3">
                                    <asp:CheckBox ID="chkSMSNotifications" runat="server" CssClass="form-check-input" Enabled="false" />
                                    <label class="form-check-label">SMS Notifications</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <asp:CheckBox ID="chkNewsletterSubscription" runat="server" CssClass="form-check-input" Enabled="false" />
                                    <label class="form-check-label">Newsletter Subscription</label>
                                </div>
                                <div class="form-check mb-3">
                                    <asp:CheckBox ID="chkAutoRefill" runat="server" CssClass="form-check-input" Enabled="false" />
                                    <label class="form-check-label">Auto-refill Reminders</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card shadow-sm mt-3" id="actionButtons" style="display: none;">
                    <div class="card-body">
                        <div class="d-flex gap-2">
                            <asp:Button ID="btnSaveProfile" runat="server" CssClass="btn btn-primary" Text="Save Changes" OnClick="btnSaveProfile_Click" />
                            <button type="button" class="btn btn-secondary" onclick="cancelEdit()">Cancel</button>
                            <asp:Button ID="btnChangePassword" runat="server" CssClass="btn btn-outline-warning ms-auto" Text="Change Password" OnClick="btnChangePassword_Click" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleEditMode() {
            const isReadOnly = $('#<%= txtFirstName.ClientID %>').prop('readonly');
            
            // Toggle readonly state
            $('.form-control, .form-select').prop('readonly', !isReadOnly).prop('disabled', !isReadOnly);
            $('.form-check-input').prop('disabled', !isReadOnly);
            
            // Show/hide elements
            if (isReadOnly) {
                $('#actionButtons').show();
                $('.avatar-upload').show();
                $('button:contains("Edit Profile")').html('<i class="fas fa-times me-2"></i>Cancel Edit');
            } else {
                cancelEdit();
            }
        }

        function cancelEdit() {
            $('.form-control, .form-select').prop('readonly', true).prop('disabled', true);
            $('.form-check-input').prop('disabled', true);
            $('#actionButtons').hide();
            $('.avatar-upload').hide();
            $('button:contains("Cancel Edit")').html('<i class="fas fa-edit me-2"></i>Edit Profile');
            
            // Reset form to original values
            location.reload();
        }

        function previewImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#profileImage').attr('src', e.target.result);
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</asp:Content>
