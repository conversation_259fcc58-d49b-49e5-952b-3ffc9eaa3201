using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Text;
using Newtonsoft.Json;
using MediEase.Models;
using MediEase.DAL;

namespace MediEase.Utilities
{
    public class PaymentRequest
    {
        public decimal Amount { get; set; }
        public string CardNumber { get; set; }
        public string ExpiryDate { get; set; }
        public string CVV { get; set; }
        public string CardholderName { get; set; }
        public string OrderNumber { get; set; }
        public string CustomerEmail { get; set; }
        public string ReturnUrl { get; set; }
        public string CancelUrl { get; set; }
    }
}

namespace MediEase.Utilities
{
    public static class PaymentService
    {
        private static readonly string StripeApiKey = ConfigurationManager.AppSettings["StripeSecretKey"] ?? "";
        private static readonly string StripePublishableKey = ConfigurationManager.AppSettings["StripePublishableKey"] ?? "";
        private static readonly string PayPalClientId = ConfigurationManager.AppSettings["PayPalClientId"] ?? "";
        private static readonly string PayPalClientSecret = ConfigurationManager.AppSettings["PayPalClientSecret"] ?? "";
        private static readonly bool IsTestMode = Convert.ToBoolean(ConfigurationManager.AppSettings["PaymentTestMode"] ?? "true");

        #region Stripe Integration (Simplified for Demo)

        public static PaymentResult ProcessStripePayment(decimal amount, string currency, string token, string description)
        {
            try
            {
                // Simulate Stripe processing for demo purposes
                // In production, use actual Stripe API calls

                System.Threading.Thread.Sleep(1000); // Simulate processing time

                // Simulate 95% success rate
                var isSuccess = new Random().Next(1, 101) <= 95;

                if (isSuccess)
                {
                    var transactionId = GenerateTransactionId();
                    ErrorLogger.LogInfo($"Stripe payment processed successfully. Transaction ID: {transactionId}, Amount: ${amount:F2}", "PaymentService");

                    return new PaymentResult
                    {
                        IsSuccess = true,
                        TransactionId = transactionId,
                        Amount = amount,
                        Currency = currency,
                        PaymentMethod = "Stripe",
                        Message = "Payment processed successfully"
                    };
                }
                else
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Payment declined by processor"
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Stripe payment processing error");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "Payment processing error. Please try again."
                };
            }
        }

        #endregion

        #region PayPal Integration (Simplified for Demo)

        public static PaymentResult ProcessPayPalPayment(decimal amount, string currency, string paymentId, string payerId)
        {
            try
            {
                // Simulate PayPal processing for demo purposes
                // In production, use actual PayPal API calls

                System.Threading.Thread.Sleep(1500); // Simulate processing time

                // Simulate 98% success rate
                var isSuccess = new Random().Next(1, 101) <= 98;

                if (isSuccess)
                {
                    var transactionId = GenerateTransactionId();
                    ErrorLogger.LogInfo($"PayPal payment processed successfully. Transaction ID: {transactionId}, Amount: ${amount:F2}", "PaymentService");

                    return new PaymentResult
                    {
                        IsSuccess = true,
                        TransactionId = transactionId,
                        Amount = amount,
                        Currency = currency,
                        PaymentMethod = "PayPal",
                        Message = "Payment processed successfully"
                    };
                }
                else
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "PayPal payment execution failed"
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "PayPal payment processing error");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "Payment processing error. Please try again."
                };
            }
        }

        #endregion

        #region Cash on Delivery

        public static PaymentResult ProcessCashOnDeliveryPayment(decimal amount, string currency)
        {
            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = $"COD_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid().ToString("N").Substring(0, 8)}",
                Amount = amount,
                Currency = currency,
                PaymentMethod = "Cash on Delivery",
                Message = "Cash on Delivery order placed successfully"
            };
        }

        #endregion

        #region Payment Recording

        public static bool RecordPayment(int orderId, PaymentResult paymentResult)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Update order payment status
                    var order = db.Orders.Find(orderId);
                    if (order != null)
                    {
                        order.PaymentStatus = paymentResult.IsSuccess ? "Paid" : "Failed";
                        order.PaymentMethod = paymentResult.PaymentMethod;
                        order.PaymentTransactionId = paymentResult.TransactionId;
                        if (paymentResult.IsSuccess)
                        {
                            order.Status = "Processing";
                            order.PaymentDate = DateTime.Now;
                        }
                        db.SaveChanges();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error recording payment");
                return false;
            }
        }

        #endregion

        #region Refund Processing (Simplified for Demo)

        public static PaymentResult ProcessRefund(string transactionId, decimal amount, string reason)
        {
            try
            {
                // Simulate refund processing for demo purposes
                // In production, use actual payment gateway APIs

                System.Threading.Thread.Sleep(1000); // Simulate processing time

                var refundTransactionId = $"REF_{DateTime.Now:yyyyMMddHHmmss}_{new Random().Next(1000, 9999)}";

                ErrorLogger.LogInfo($"Refund processed successfully. Transaction ID: {refundTransactionId}, Amount: ${amount:F2}, Reason: {reason}", "PaymentService");

                return new PaymentResult
                {
                    IsSuccess = true,
                    TransactionId = refundTransactionId,
                    Amount = amount,
                    PaymentMethod = "Refund",
                    Message = "Refund processed successfully"
                };
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error processing refund");
                return new PaymentResult { IsSuccess = false, Message = "Refund processing error" };
            }
        }

        #endregion

        #region Utility Methods

        public static string GetStripePublishableKey()
        {
            return StripePublishableKey;
        }

        public static bool IsPaymentConfigured()
        {
            return !string.IsNullOrEmpty(StripeApiKey) || !string.IsNullOrEmpty(PayPalClientId);
        }

        public static decimal CalculateProcessingFee(decimal amount, string paymentMethod)
        {
            switch (paymentMethod.ToLower())
            {
                case "stripe":
                    return Math.Round(amount * 0.029m + 0.30m, 2); // 2.9% + $0.30
                case "paypal":
                    return Math.Round(amount * 0.0349m + 0.49m, 2); // 3.49% + $0.49
                default:
                    return 0;
            }
        }

        // Methods for checkout compatibility
        public static PaymentResult ProcessCreditCardPayment(PaymentRequest request)
        {
            try
            {
                // Validate request
                if (request == null || request.Amount <= 0)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Invalid payment request"
                    };
                }

                // For demo purposes, simulate credit card processing
                // In production, use ProcessStripePaymentAsync or similar

                // Basic validation
                var cardNumber = request.CardNumber?.Replace(" ", "").Replace("-", "");
                if (string.IsNullOrEmpty(cardNumber) || cardNumber.Length < 13)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Invalid card number"
                    };
                }

                // Simulate processing
                System.Threading.Thread.Sleep(2000);

                // Simulate success/failure based on card number
                var lastDigit = int.Parse(cardNumber.Substring(cardNumber.Length - 1));

                if (lastDigit == 1)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Card declined by issuer"
                    };
                }
                else if (lastDigit == 2)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Insufficient funds"
                    };
                }
                else
                {
                    var transactionId = GenerateTransactionId();

                    ErrorLogger.LogInfo($"Credit card payment processed successfully. Transaction ID: {transactionId}, Amount: ${request.Amount:F2}", "PaymentService");

                    return new PaymentResult
                    {
                        IsSuccess = true,
                        TransactionId = transactionId,
                        PaymentMethod = "Credit Card",
                        ProcessedDate = DateTime.Now,
                        Amount = request.Amount,
                        Message = "Payment processed successfully"
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "PaymentService.ProcessCreditCardPayment");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "Payment processing error occurred"
                };
            }
        }

        public static PaymentResult ProcessPayPalPayment(PaymentRequest request)
        {
            try
            {
                // Validate request
                if (request == null || request.Amount <= 0)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "Invalid payment request"
                    };
                }

                // Simulate PayPal processing
                System.Threading.Thread.Sleep(1500);

                // Simulate 98% success rate
                var isSuccess = new Random().Next(1, 101) <= 98;

                if (isSuccess)
                {
                    var transactionId = GenerateTransactionId();

                    ErrorLogger.LogInfo($"PayPal payment processed successfully. Transaction ID: {transactionId}, Amount: ${request.Amount:F2}", "PaymentService");

                    return new PaymentResult
                    {
                        IsSuccess = true,
                        TransactionId = transactionId,
                        PaymentMethod = "PayPal",
                        ProcessedDate = DateTime.Now,
                        Amount = request.Amount,
                        Message = "Payment processed successfully"
                    };
                }
                else
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        Message = "PayPal payment was cancelled or failed"
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "PaymentService.ProcessPayPalPayment");
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "PayPal processing error occurred"
                };
            }
        }

        public static string GenerateTransactionId()
        {
            return "TXN" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999);
        }

        #endregion
    }

    public class PaymentResult
    {
        public bool IsSuccess { get; set; }
        public string TransactionId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public string PaymentMethod { get; set; }
        public string Message { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime ProcessedDate { get; set; } = DateTime.Now;
    }
}
