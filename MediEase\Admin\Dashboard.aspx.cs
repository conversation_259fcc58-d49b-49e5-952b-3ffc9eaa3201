using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase.Admin
{
    public partial class Dashboard : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is admin
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null || currentUser.Role != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadDashboardData();
            }
        }

        private void LoadDashboardData()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Load statistics
                    LoadStatistics(db);
                    
                    // Load recent orders
                    LoadRecentOrders(db);
                    
                    // Load low stock alerts
                    LoadLowStockAlerts(db);
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading admin dashboard data");
                ShowErrorMessage("Error loading dashboard data. Please try again.");
            }
        }

        private void LoadStatistics(MediEaseContext db)
        {
            // Total users
            var totalUsers = db.Users.Count(u => u.IsActive);
            lblTotalUsers.Text = totalUsers.ToString();

            // Total medicines
            var totalMedicines = db.Medicines.Count(m => m.IsActive);
            lblTotalMedicines.Text = totalMedicines.ToString();

            // Total orders
            var totalOrders = db.Orders.Count();
            lblTotalOrders.Text = totalOrders.ToString();

            // Monthly revenue
            var currentMonth = DateTime.Now.Month;
            var currentYear = DateTime.Now.Year;
            var monthlyRevenue = db.Orders
                .Where(o => o.OrderDate.Month == currentMonth && o.OrderDate.Year == currentYear)
                .Sum(o => (decimal?)o.TotalAmount) ?? 0;
            lblMonthlyRevenue.Text = monthlyRevenue.ToString("N2");
        }

        private void LoadRecentOrders(MediEaseContext db)
        {
            var recentOrders = db.Orders
                .OrderByDescending(o => o.OrderDate)
                .Take(10)
                .Select(o => new
                {
                    o.OrderNumber,
                    CustomerName = o.Customer.FirstName + " " + o.Customer.LastName,
                    o.TotalAmount,
                    o.Status,
                    o.OrderDate
                })
                .ToList();

            gvRecentOrders.DataSource = recentOrders;
            gvRecentOrders.DataBind();
        }

        private void LoadLowStockAlerts(MediEaseContext db)
        {
            var lowStockItems = db.Medicines
                .Where(m => m.IsActive && m.StockQuantity <= m.MinimumStockLevel)
                .OrderBy(m => m.StockQuantity)
                .Select(m => new
                {
                    m.MedicineId,
                    m.Name,
                    m.StockQuantity,
                    m.MinimumStockLevel
                })
                .ToList();

            gvLowStock.DataSource = lowStockItems;
            gvLowStock.DataBind();
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDashboardData();
            ShowSuccessMessage("Dashboard data refreshed successfully.");
        }

        protected void lnkManageUsers_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Admin/UserManagement.aspx");
        }

        protected void lnkManageMedicines_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Admin/Medicines.aspx");
        }

        protected void lnkViewOrders_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Admin/Orders.aspx");
        }

        protected void lnkSystemSettings_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Admin/Settings.aspx");
        }

        protected void lnkRestock_Click(object sender, EventArgs e)
        {
            var linkButton = (LinkButton)sender;
            var medicineId = Convert.ToInt32(linkButton.CommandArgument);
            
            // Redirect to medicine management page with the specific medicine
            Response.Redirect($"~/Admin/Medicines.aspx?id={medicineId}&action=restock");
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }
    }
}
