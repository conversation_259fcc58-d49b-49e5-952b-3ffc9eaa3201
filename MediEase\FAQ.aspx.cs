using System;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.Utilities;

namespace MediEase
{
    public partial class FAQ : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SetPageMetadata();
                
                // Check if there's a search query from URL
                var searchQuery = Request.QueryString["search"];
                if (!string.IsNullOrEmpty(searchQuery))
                {
                    txtSearchFAQ.Text = searchQuery;
                    // The client-side search will handle filtering
                    ClientScript.RegisterStartupScript(this.GetType(), "searchFAQ", 
                        $"searchFAQs('{searchQuery.Replace("'", "\\'")}');", true);
                }
                
                // Check if there's a category filter from URL
                var category = Request.QueryString["category"];
                if (!string.IsNullOrEmpty(category))
                {
                    SetActiveCategory(category);
                }
            }
        }

        protected void btnSearchFAQ_Click(object sender, EventArgs e)
        {
            var searchTerm = txtSearchFAQ.Text.Trim();
            
            // Log search activity
            ErrorLogger.LogUserActivity($"FAQ search performed: {searchTerm}", SecurityHelper.GetCurrentUser()?.UserId);
            
            // Use client-side search for better user experience
            ClientScript.RegisterStartupScript(this.GetType(), "searchFAQ", 
                $"searchFAQs('{searchTerm.Replace("'", "\\'")}');", true);
        }

        protected void btnCategory_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            var category = button.CommandArgument;
            
            // Reset all buttons
            ResetCategoryButtons();
            
            // Set active button
            button.CssClass = "btn btn-primary";
            
            // Log category filter activity
            ErrorLogger.LogUserActivity($"FAQ category filtered: {category}", SecurityHelper.GetCurrentUser()?.UserId);
            
            // Use client-side filtering
            ClientScript.RegisterStartupScript(this.GetType(), "filterCategory", 
                $"filterByCategory('{category}');", true);
        }

        private void ResetCategoryButtons()
        {
            btnAllCategories.CssClass = "btn btn-outline-primary";
            btnOrdering.CssClass = "btn btn-outline-primary";
            btnPrescriptions.CssClass = "btn btn-outline-primary";
            btnDelivery.CssClass = "btn btn-outline-primary";
            btnPayment.CssClass = "btn btn-outline-primary";
            btnAccount.CssClass = "btn btn-outline-primary";
            btnTechnical.CssClass = "btn btn-outline-primary";
        }

        private void SetActiveCategory(string category)
        {
            ResetCategoryButtons();
            
            switch (category.ToLower())
            {
                case "ordering":
                    btnOrdering.CssClass = "btn btn-primary";
                    break;
                case "prescriptions":
                    btnPrescriptions.CssClass = "btn btn-primary";
                    break;
                case "delivery":
                    btnDelivery.CssClass = "btn btn-primary";
                    break;
                case "payment":
                    btnPayment.CssClass = "btn btn-primary";
                    break;
                case "account":
                    btnAccount.CssClass = "btn btn-primary";
                    break;
                case "technical":
                    btnTechnical.CssClass = "btn btn-primary";
                    break;
                default:
                    btnAllCategories.CssClass = "btn btn-primary";
                    break;
            }
            
            // Use client-side filtering
            ClientScript.RegisterStartupScript(this.GetType(), "filterCategory", 
                $"filterByCategory('{category}');", true);
        }

        private void SetPageMetadata()
        {
            Page.Title = "FAQ - Frequently Asked Questions - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Find answers to frequently asked questions about MediEase pharmacy services, ordering, prescriptions, delivery, payments, and technical support.");
                master.AddMetaKeywords("FAQ, frequently asked questions, pharmacy help, MediEase support, ordering help, prescription questions, delivery information");
            }
        }
    }
}
