<%@ Page Title="Loyalty Program" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="LoyaltyProgram.aspx.cs" Inherits="MediEase.Customer.LoyaltyProgram" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-star me-2 text-warning"></i>MediEase Loyalty Program</h2>
                <p class="text-muted">Earn points with every purchase and unlock exclusive rewards!</p>
            </div>
        </div>

        <!-- Current Points Overview -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow-sm border-0 bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h3 class="mb-1">Your Current Points</h3>
                                <h1 class="display-4 fw-bold">
                                    <asp:Label ID="lblCurrentPoints" runat="server" Text="0"></asp:Label>
                                    <small class="fs-6">points</small>
                                </h1>
                                <p class="mb-0 opacity-75">
                                    Worth $<asp:Label ID="lblPointsValue" runat="server" Text="0.00"></asp:Label> in rewards
                                </p>
                            </div>
                            <div class="col-md-6 text-center">
                                <div class="position-relative">
                                    <canvas id="pointsChart" width="150" height="150"></canvas>
                                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                                        <div class="fw-bold">
                                            <asp:Label ID="lblTierName" runat="server" Text="Bronze"></asp:Label>
                                        </div>
                                        <small>Member</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-gift fa-3x text-primary mb-3"></i>
                        <h5>Next Reward</h5>
                        <p class="text-muted">
                            <asp:Label ID="lblPointsToNextReward" runat="server" Text="0"></asp:Label> points away
                        </p>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-primary" role="progressbar" 
                                 style="width: <%# GetProgressPercentage() %>%" 
                                 aria-valuenow="<%# GetProgressPercentage() %>" 
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <asp:Button ID="btnViewRewards" runat="server" CssClass="btn btn-primary btn-sm" 
                            Text="View Rewards" OnClick="btnViewRewards_Click" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Membership Tiers -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Membership Tiers</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-3">
                                <div class="text-center tier-card bronze <%# GetTierClass("Bronze") %>">
                                    <div class="tier-icon mb-3">
                                        <i class="fas fa-medal fa-3x text-bronze"></i>
                                    </div>
                                    <h6 class="fw-bold">Bronze</h6>
                                    <p class="small text-muted">0 - 999 points</p>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-check text-success me-1"></i>1 point per $1 spent</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Birthday discount</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Member-only deals</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center tier-card silver <%# GetTierClass("Silver") %>">
                                    <div class="tier-icon mb-3">
                                        <i class="fas fa-medal fa-3x text-silver"></i>
                                    </div>
                                    <h6 class="fw-bold">Silver</h6>
                                    <p class="small text-muted">1,000 - 2,999 points</p>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-check text-success me-1"></i>1.25 points per $1 spent</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Free shipping on orders $50+</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Priority customer support</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center tier-card gold <%# GetTierClass("Gold") %>">
                                    <div class="tier-icon mb-3">
                                        <i class="fas fa-medal fa-3x text-warning"></i>
                                    </div>
                                    <h6 class="fw-bold">Gold</h6>
                                    <p class="small text-muted">3,000 - 4,999 points</p>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-check text-success me-1"></i>1.5 points per $1 spent</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Free shipping on all orders</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Exclusive product previews</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center tier-card platinum <%# GetTierClass("Platinum") %>">
                                    <div class="tier-icon mb-3">
                                        <i class="fas fa-crown fa-3x text-primary"></i>
                                    </div>
                                    <h6 class="fw-bold">Platinum</h6>
                                    <p class="small text-muted">5,000+ points</p>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-check text-success me-1"></i>2 points per $1 spent</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Personal health advisor</li>
                                        <li><i class="fas fa-check text-success me-1"></i>VIP customer service</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Rewards -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-gift me-2"></i>Available Rewards</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="rewardFilter" id="allRewards" autocomplete="off" checked>
                            <label class="btn btn-outline-primary" for="allRewards">All</label>
                            
                            <input type="radio" class="btn-check" name="rewardFilter" id="discountRewards" autocomplete="off">
                            <label class="btn btn-outline-primary" for="discountRewards">Discounts</label>
                            
                            <input type="radio" class="btn-check" name="rewardFilter" id="freeShipping" autocomplete="off">
                            <label class="btn btn-outline-primary" for="freeShipping">Free Shipping</label>
                            
                            <input type="radio" class="btn-check" name="rewardFilter" id="products" autocomplete="off">
                            <label class="btn btn-outline-primary" for="products">Products</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <asp:Repeater ID="rptRewards" runat="server">
                                <ItemTemplate>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card reward-card h-100 border-0 shadow-sm">
                                            <div class="card-body text-center">
                                                <div class="reward-icon mb-3">
                                                    <i class="<%# GetRewardIcon(Eval("Type").ToString()) %> fa-3x text-primary"></i>
                                                </div>
                                                <h6 class="card-title"><%# Eval("Title") %></h6>
                                                <p class="card-text text-muted small"><%# Eval("Description") %></p>
                                                <div class="points-required mb-3">
                                                    <span class="badge bg-primary fs-6"><%# Eval("PointsRequired") %> points</span>
                                                </div>
                                                <div class="d-grid">
                                                    <%# Convert.ToInt32(Eval("PointsRequired")) <= GetCurrentUserPoints() ?
                                                        "<button class=\"btn btn-success redeem-reward\" data-reward-id=\"" + Eval("RewardId") + "\">" +
                                                        "<i class=\"fas fa-check me-1\"></i>Redeem Now</button>" :
                                                        "<button class=\"btn btn-outline-secondary\" disabled>" +
                                                        "<i class=\"fas fa-lock me-1\"></i>Need " + (Convert.ToInt32(Eval("PointsRequired")) - GetCurrentUserPoints()) + " more points</button>" %>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Points History -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Points History</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <asp:GridView ID="gvPointsHistory" runat="server" CssClass="table table-hover" 
                                AutoGenerateColumns="false" EmptyDataText="No points history available.">
                                <Columns>
                                    <asp:TemplateField HeaderText="Date">
                                        <ItemTemplate>
                                            <span class="fw-bold"><%# Convert.ToDateTime(Eval("Date")).ToString("MMM dd, yyyy") %></span><br>
                                            <small class="text-muted"><%# Convert.ToDateTime(Eval("Date")).ToString("hh:mm tt") %></small>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Activity">
                                        <ItemTemplate>
                                            <div class="d-flex align-items-center">
                                                <i class="<%# GetActivityIcon(Eval("Type").ToString()) %> me-2"></i>
                                                <div>
                                                    <span class="fw-bold"><%# Eval("Description") %></span>
                                                    <%# !string.IsNullOrEmpty(Eval("OrderNumber")?.ToString()) ? 
                                                        "<br><small class=\"text-muted\">Order #" + Eval("OrderNumber") + "</small>" : "" %>
                                                </div>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Points">
                                        <ItemTemplate>
                                            <span class="<%# Convert.ToInt32(Eval("Points")) > 0 ? "text-success fw-bold" : "text-danger fw-bold" %>">
                                                <%# Convert.ToInt32(Eval("Points")) > 0 ? "+" : "" %><%# Eval("Points") %>
                                            </span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Balance">
                                        <ItemTemplate>
                                            <span class="fw-bold"><%# Eval("Balance") %></span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- How to Earn Points -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-info">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How to Earn More Points</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-3 text-center">
                                <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                                <h6>Make Purchases</h6>
                                <p class="small text-muted">Earn 1-2 points for every $1 spent based on your tier</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <i class="fas fa-user-friends fa-2x text-primary mb-2"></i>
                                <h6>Refer Friends</h6>
                                <p class="small text-muted">Get 500 bonus points for each friend who makes their first purchase</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <i class="fas fa-star fa-2x text-primary mb-2"></i>
                                <h6>Write Reviews</h6>
                                <p class="small text-muted">Earn 50 points for each product review you write</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <i class="fas fa-birthday-cake fa-2x text-primary mb-2"></i>
                                <h6>Birthday Bonus</h6>
                                <p class="small text-muted">Receive 200 bonus points on your birthday month</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        
        .tier-card {
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .tier-card.active {
            border-color: #007bff;
            background-color: #f8f9ff;
            transform: scale(1.05);
        }
        
        .tier-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .text-bronze { color: #cd7f32; }
        .text-silver { color: #c0c0c0; }
        
        .reward-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .points-chart {
            position: relative;
        }
    </style>

    <script>
        // Redeem reward functionality
        $(document).on('click', '.redeem-reward', function() {
            const rewardId = $(this).data('reward-id');
            if (confirm('Are you sure you want to redeem this reward?')) {
                redeemReward(rewardId);
            }
        });

        function redeemReward(rewardId) {
            // AJAX call to redeem reward
            $.ajax({
                type: 'POST',
                url: 'LoyaltyProgram.aspx/RedeemReward',
                data: JSON.stringify({ rewardId: rewardId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Reward redeemed successfully!');
                        location.reload();
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('An error occurred while redeeming the reward.');
                }
            });
        }

        // Filter rewards
        $('input[name="rewardFilter"]').change(function() {
            const filter = $(this).attr('id');
            filterRewards(filter);
        });

        function filterRewards(filter) {
            $('.reward-card').parent().show();
            
            if (filter !== 'allRewards') {
                $('.reward-card').parent().hide();
                $(`.reward-card[data-type="${filter}"]`).parent().show();
            }
        }

        // Draw points chart
        function drawPointsChart() {
            const canvas = document.getElementById('pointsChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 60;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw background circle
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 8;
            ctx.stroke();
            
            // Draw progress arc
            const progress = <%# GetTierProgress() %> / 100;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, -Math.PI / 2, (-Math.PI / 2) + (2 * Math.PI * progress));
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 8;
            ctx.lineCap = 'round';
            ctx.stroke();
        }

        // Initialize chart when page loads
        $(document).ready(function() {
            drawPointsChart();
        });
    </script>
</asp:Content>
