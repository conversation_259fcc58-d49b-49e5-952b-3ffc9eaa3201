using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.SessionState;
using Newtonsoft.Json;
using MediEase.Utilities;
using MediEase.DAL;
using MediEase.Models;

namespace MediEase.Handlers
{
    public class ChatHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequiresSessionState
    {
        public bool IsReusable => false;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "application/json";
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
            context.Response.Headers.Add("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            try
            {
                if (context.Request.HttpMethod == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    return;
                }

                if (context.Request.HttpMethod == "POST")
                {
                    ProcessChatMessage(context);
                }
                else
                {
                    context.Response.StatusCode = 405; // Method Not Allowed
                    context.Response.Write(JsonConvert.SerializeObject(new { success = false, message = "Method not allowed" }));
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error in ChatHandler");
                context.Response.StatusCode = 500;
                context.Response.Write(JsonConvert.SerializeObject(new { success = false, message = "Internal server error" }));
            }
        }

        private async void ProcessChatMessage(HttpContext context)
        {
            try
            {
                var message = context.Request.Form["message"] ?? context.Request.QueryString["message"];

                if (string.IsNullOrWhiteSpace(message))
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { success = false, message = "Message is required" }));
                    return;
                }

                // Sanitize input
                message = SecurityHelper.SanitizeInput(message);

                // Get session ID for conversation context
                var sessionId = context.Session.SessionID;
                var currentUser = SecurityHelper.GetCurrentUser();

                // Get AI response using direct API call (working implementation)
                var aiResponse = await CallOpenRouterAPIDirectly(message);

                // Save chat message to database
                SaveChatMessage(sessionId, currentUser?.UserId, message, aiResponse);

                // Return response
                var response = new
                {
                    success = true,
                    message = aiResponse,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                context.Response.Write(JsonConvert.SerializeObject(response));
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error processing chat message");

                var errorResponse = new
                {
                    success = false,
                    message = "I apologize, but I'm experiencing technical difficulties. Please try again later or contact our support team."
                };

                context.Response.Write(JsonConvert.SerializeObject(errorResponse));
            }
        }

        private string GetConversationContext(string sessionId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var recentMessages = db.ChatMessages
                        .Where(m => m.SessionId == sessionId)
                        .OrderByDescending(m => m.CreatedDate)
                        .Take(5) // Last 5 messages for context
                        .OrderBy(m => m.CreatedDate)
                        .ToList();

                    if (recentMessages.Any())
                    {
                        var context = string.Join("\n", recentMessages.Select(m =>
                            $"{m.Sender}: {m.Message}"));
                        return context;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting conversation context");
            }

            return null;
        }

        private async Task<string> CallOpenRouterAPIDirectly(string userMessage)
        {
            try
            {
                var apiKey = System.Configuration.ConfigurationManager.AppSettings["OpenRouterApiKey"];
                var apiUrl = System.Configuration.ConfigurationManager.AppSettings["OpenRouterApiUrl"];
                var model = System.Configuration.ConfigurationManager.AppSettings["OpenRouterModel"];

                if (string.IsNullOrEmpty(apiKey))
                {
                    return "I apologize, but the AI service is not properly configured. Please contact support.";
                }

                var requestBody = new
                {
                    model = model,
                    messages = new[]
                    {
                        new {
                            role = "system",
                            content = @"You are MediBot, a helpful AI assistant for MediEase Pharmacy. You provide information about medications, health conditions, and pharmacy services.

IMPORTANT GUIDELINES:
- Always emphasize that your advice is for informational purposes only
- Recommend consulting healthcare professionals for medical decisions
- Never diagnose medical conditions
- Be cautious about drug recommendations - suggest consulting a pharmacist
- Provide helpful, accurate information about medications and health
- Be empathetic and professional
- Keep responses concise but informative
- Focus on general health education and medication information

You can help with:
- General medication information and side effects
- Medication storage and handling tips
- General health education
- Pharmacy services information
- Basic drug interaction warnings (with professional consultation advice)

Always maintain a helpful, professional, and caring tone."
                        },
                        new {
                            role = "user",
                            content = userMessage
                        }
                    },
                    max_tokens = 300,
                    temperature = 0.7,
                    top_p = 0.9
                };

                var json = JsonConvert.SerializeObject(requestBody);

                using (var webClient = new WebClient())
                {
                    webClient.Headers[HttpRequestHeader.Authorization] = $"Bearer {apiKey}";
                    webClient.Headers[HttpRequestHeader.ContentType] = "application/json";
                    webClient.Headers["HTTP-Referer"] = "https://mediease.com";
                    webClient.Headers["X-Title"] = "MediEase Pharmacy System";

                    try
                    {
                        var responseContent = await webClient.UploadStringTaskAsync(apiUrl, json);
                        var apiResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);

                        if (apiResponse?.choices != null && apiResponse.choices.Count > 0)
                        {
                            var botResponse = apiResponse.choices[0].message.content.ToString();
                            return botResponse;
                        }
                        else
                        {
                            ErrorLogger.LogError(new Exception($"Invalid API response structure: {responseContent}"), "CallOpenRouterAPIDirectly");
                            return "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.";
                        }
                    }
                    catch (WebException webEx)
                    {
                        string errorDetails = "";
                        if (webEx.Response != null)
                        {
                            using (var reader = new System.IO.StreamReader(webEx.Response.GetResponseStream()))
                            {
                                errorDetails = reader.ReadToEnd();
                            }
                        }

                        ErrorLogger.LogError(new Exception($"WebException: {webEx.Message}, Details: {errorDetails}"), "CallOpenRouterAPIDirectly");
                        return "I'm experiencing connectivity issues with the AI service. Please try again in a moment.";
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "CallOpenRouterAPIDirectly");
                return "I apologize, but I'm experiencing technical difficulties. Please try again later or contact our support team.";
            }
        }

        private void SaveChatMessage(string sessionId, int? userId, string userMessage, string botResponse)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Save user message
                    var userChatMessage = new ChatMessage
                    {
                        SessionId = sessionId,
                        UserId = userId,
                        Message = userMessage,
                        Sender = "User",
                        MessageType = "Text",
                        CreatedDate = DateTime.Now
                    };

                    db.ChatMessages.Add(userChatMessage);

                    // Save bot response
                    var botChatMessage = new ChatMessage
                    {
                        SessionId = sessionId,
                        UserId = userId,
                        Message = botResponse,
                        Sender = "Bot",
                        MessageType = "Text",
                        Response = botResponse,
                        CreatedDate = DateTime.Now
                    };

                    db.ChatMessages.Add(botChatMessage);
                    db.SaveChanges();

                    ErrorLogger.LogInfo($"Chat messages saved successfully for session {sessionId}", "ChatHandler");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error saving chat message to database");
                // Don't fail the chat if saving fails
            }
        }
    }
}
