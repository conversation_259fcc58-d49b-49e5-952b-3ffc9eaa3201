using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Customer
{
    public partial class OrderTracking : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsCustomer() && !SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadOrders();
                
                // Check if there's an order ID in the query string
                var orderIdParam = Request.QueryString["orderId"];
                if (!string.IsNullOrEmpty(orderIdParam) && int.TryParse(orderIdParam, out int orderId))
                {
                    LoadOrderDetails(orderId);
                }
            }
        }

        private void LoadOrders()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    var orders = db.Orders
                        .Where(o => o.CustomerId == currentUser.UserId)
                        .OrderByDescending(o => o.OrderDate)
                        .Select(o => new
                        {
                            o.OrderId,
                            o.OrderNumber,
                            o.OrderDate,
                            o.TotalAmount,
                            o.Status,
                            ExpectedDelivery = o.ExpectedDeliveryDate,
                            DeliveredDate = o.ActualDeliveryDate,
                            ItemCount = db.OrderItems.Count(oi => oi.OrderId == o.OrderId),
                            ItemSummary = db.OrderItems
                                .Where(oi => oi.OrderId == o.OrderId)
                                .Take(2)
                                .Select(oi => oi.Medicine.Name)
                                .ToList()
                        })
                        .ToList();

                    // Format item summary
                    var formattedOrders = orders.Select(o => new
                    {
                        o.OrderId,
                        o.OrderNumber,
                        o.OrderDate,
                        o.TotalAmount,
                        o.Status,
                        o.ExpectedDelivery,
                        o.DeliveredDate,
                        o.ItemCount,
                        ItemSummary = string.Join(", ", o.ItemSummary) + (o.ItemCount > 2 ? $" and {o.ItemCount - 2} more..." : "")
                    }).ToList();

                    if (formattedOrders.Any())
                    {
                        gvOrders.DataSource = formattedOrders;
                        gvOrders.DataBind();
                        pnlNoOrders.Visible = false;
                    }
                    else
                    {
                        pnlNoOrders.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading orders for tracking");
                ShowErrorMessage("Error loading orders. Please try again.");
                pnlNoOrders.Visible = true;
            }
        }

        protected void btnTrackOrder_Click(object sender, EventArgs e)
        {
            var orderNumber = txtOrderNumber.Text.Trim();
            if (string.IsNullOrEmpty(orderNumber))
            {
                ShowErrorMessage("Please enter an order number.");
                return;
            }

            TrackOrderByNumber(orderNumber);
        }

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterOrdersByStatus();
        }

        protected void gvOrders_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "TrackOrder")
            {
                if (int.TryParse(e.CommandArgument.ToString(), out int orderId))
                {
                    LoadOrderDetails(orderId);
                }
            }
        }

        private void TrackOrderByNumber(string orderNumber)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.FirstOrDefault(o => 
                        o.OrderNumber == orderNumber && o.CustomerId == currentUser.UserId);

                    if (order != null)
                    {
                        LoadOrderDetails(order.OrderId);
                    }
                    else
                    {
                        ShowErrorMessage("Order not found. Please check the order number and try again.");
                        pnlOrderDetails.Visible = false;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error tracking order by number");
                ShowErrorMessage("Error tracking order. Please try again.");
            }
        }

        private void LoadOrderDetails(int orderId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.Find(orderId);
                    if (order == null) return;

                    // Load order information
                    lblOrderNumber.Text = order.OrderNumber;
                    lblOrderStatus.Text = order.Status;
                    lblOrderDate.Text = order.OrderDate.ToString("MMM dd, yyyy hh:mm tt");
                    lblTotalAmount.Text = order.TotalAmount.ToString("F2");
                    lblPaymentMethod.Text = order.PaymentMethod ?? "Credit Card";
                    lblExpectedDelivery.Text = order.ExpectedDeliveryDate?.ToString("MMM dd, yyyy") ?? "TBD";
                    
                    // Load delivery address
                    lblDeliveryAddress.Text = $"{order.ShippingAddress}<br>{order.ShippingCity}, {order.ShippingPostalCode}";
                    lblContactNumber.Text = order.ContactPhone ?? "N/A";

                    // Load tracking timeline
                    LoadTrackingTimeline(order);

                    // Load order items
                    LoadOrderItems(orderId);

                    pnlOrderDetails.Visible = true;

                    // Scroll to order details
                    ClientScript.RegisterStartupScript(this.GetType(), "scrollToDetails", 
                        "document.querySelector('#pnlOrderDetails').scrollIntoView({ behavior: 'smooth' });", true);
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading order details");
                ShowErrorMessage("Error loading order details. Please try again.");
            }
        }

        private void LoadTrackingTimeline(Order order)
        {
            var timeline = new List<object>();

            // Define timeline steps based on order status
            var steps = new[]
            {
                new { Status = "Pending", Title = "Order Placed", Description = "Your order has been received and is being processed." },
                new { Status = "Processing", Title = "Processing", Description = "Your order is being prepared by our pharmacy team." },
                new { Status = "Shipped", Title = "Shipped", Description = "Your order has been shipped and is on its way." },
                new { Status = "OutForDelivery", Title = "Out for Delivery", Description = "Your order is out for delivery and will arrive soon." },
                new { Status = "Delivered", Title = "Delivered", Description = "Your order has been successfully delivered." }
            };

            var currentStatusIndex = Array.FindIndex(steps, s => s.Status == order.Status);

            for (int i = 0; i < steps.Length; i++)
            {
                var step = steps[i];
                var isCompleted = i <= currentStatusIndex;
                var completedDate = isCompleted ? order.OrderDate.AddHours(i * 6) : (DateTime?)null; // Mock dates

                timeline.Add(new
                {
                    Status = step.Status,
                    Title = step.Title,
                    Description = step.Description,
                    IsCompleted = isCompleted,
                    CompletedDate = completedDate
                });
            }

            rptTrackingTimeline.DataSource = timeline;
            rptTrackingTimeline.DataBind();
        }

        private void LoadOrderItems(int orderId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var orderItems = db.OrderItems
                        .Where(oi => oi.OrderId == orderId)
                        .Select(oi => new
                        {
                            oi.MedicineId,
                            MedicineName = oi.Medicine.Name,
                            GenericName = oi.Medicine.GenericName,
                            ImagePath = oi.Medicine.ImagePath,
                            oi.Quantity,
                            oi.UnitPrice,
                            TotalPrice = oi.Quantity * oi.UnitPrice,
                            Status = "In Progress" // You could have item-level status
                        })
                        .ToList();

                    gvOrderItems.DataSource = orderItems;
                    gvOrderItems.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading order items");
            }
        }

        private void FilterOrdersByStatus()
        {
            var selectedStatus = ddlStatusFilter.SelectedValue;
            
            // This would typically filter the GridView data source
            // For now, we'll reload the orders with the filter
            LoadOrders(); // You could modify this to apply the filter
        }

        [WebMethod]
        public static object CancelOrder(int orderId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null)
                {
                    return new { success = false, message = "User not authenticated" };
                }

                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.FirstOrDefault(o => 
                        o.OrderId == orderId && o.CustomerId == currentUser.UserId);

                    if (order == null)
                    {
                        return new { success = false, message = "Order not found" };
                    }

                    if (order.Status != "Pending" && order.Status != "Processing")
                    {
                        return new { success = false, message = "Order cannot be cancelled at this stage" };
                    }

                    order.Status = "Cancelled";
                    order.ModifiedDate = DateTime.Now;
                    db.SaveChanges();

                    ErrorLogger.LogUserActivity($"Cancelled order {order.OrderNumber}", currentUser.UserId);

                    return new { success = true, message = "Order cancelled successfully" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error cancelling order");
                return new { success = false, message = "An error occurred while cancelling the order" };
            }
        }

        [WebMethod]
        public static object ReorderItems(int orderId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null)
                {
                    return new { success = false, message = "User not authenticated" };
                }

                using (var db = new MediEaseContext())
                {
                    var orderItems = db.OrderItems
                        .Where(oi => oi.OrderId == orderId)
                        .ToList();

                    if (!orderItems.Any())
                    {
                        return new { success = false, message = "No items found in the order" };
                    }

                    // Add items to cart (this would typically use a cart service)
                    foreach (var item in orderItems)
                    {
                        // Add to cart logic here
                        // For now, just log the action
                        ErrorLogger.LogUserActivity($"Added {item.Medicine.Name} to cart from reorder", currentUser.UserId);
                    }

                    return new { success = true, message = $"{orderItems.Count} items added to cart" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error reordering items");
                return new { success = false, message = "An error occurred while adding items to cart" };
            }
        }

        // Helper methods for data binding
        protected string GetMedicineImage(object imagePath)
        {
            var path = imagePath?.ToString();
            if (string.IsNullOrEmpty(path))
                return "~/Images/medicine-placeholder.jpg";
            
            return path.StartsWith("~/") ? path : "~/Images/Medicines/" + path;
        }

        protected string GetStatusColor(string status)
        {
            switch (status?.ToLower())
            {
                case "pending": return "warning";
                case "processing": return "info";
                case "shipped": return "primary";
                case "outfordelivery": return "success";
                case "delivered": return "success";
                case "cancelled": return "danger";
                default: return "secondary";
            }
        }

        protected string GetItemStatusColor(string status)
        {
            return GetStatusColor(status);
        }

        protected string GetTimelineItemClass(string status, object isCompleted)
        {
            var completed = Convert.ToBoolean(isCompleted);
            if (completed)
                return "completed";
            
            // Check if this is the current active step
            // This would need more logic to determine the current step
            return "active";
        }

        protected string GetStatusIcon(string status)
        {
            switch (status?.ToLower())
            {
                case "pending": return "fas fa-clock";
                case "processing": return "fas fa-cogs";
                case "shipped": return "fas fa-truck";
                case "outfordelivery": return "fas fa-shipping-fast";
                case "delivered": return "fas fa-check";
                default: return "fas fa-circle";
            }
        }

        protected string GetDeliveryInfo(string status, object expectedDelivery, object deliveredDate)
        {
            switch (status?.ToLower())
            {
                case "delivered":
                    var delivered = Convert.ToDateTime(deliveredDate);
                    return $"<span class='text-success fw-bold'>Delivered</span><br><small>{delivered:MMM dd, yyyy}</small>";
                case "outfordelivery":
                    return "<span class='text-primary fw-bold'>Today</span><br><small>Out for delivery</small>";
                case "shipped":
                    var expected = Convert.ToDateTime(expectedDelivery);
                    return $"<span class='text-info fw-bold'>{expected:MMM dd}</span><br><small>Expected</small>";
                default:
                    return "<span class='text-muted'>TBD</span>";
            }
        }

        protected bool CanCancelOrder(string status)
        {
            return status == "Pending" || status == "Processing";
        }

        protected bool CanReorder(string status)
        {
            return status == "Delivered" || status == "Cancelled";
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Order Tracking - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Track your medicine orders and view delivery status in real-time with MediEase order tracking system.");
                master.AddMetaKeywords("order tracking, delivery status, medicine orders, pharmacy tracking");
            }
        }
    }
}
