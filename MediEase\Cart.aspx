<%@ Page Title="Shopping Cart" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Cart.aspx.cs" Inherits="MediEase.Cart" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="mb-0">Shopping Cart</h2>
                <p class="text-muted">Review your items before checkout</p>
            </div>
        </div>

        <!-- Cart Items -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0">Cart Items</h5>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlEmptyCart" runat="server" Visible="false" CssClass="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h4>Your cart is empty</h4>
                            <p class="text-muted">Add some medicines to your cart to get started.</p>
                            <asp:LinkButton ID="lnkContinueShopping" runat="server" CssClass="btn btn-primary" OnClick="lnkContinueShopping_Click">
                                Continue Shopping
                            </asp:LinkButton>
                        </asp:Panel>

                        <asp:Panel ID="pnlCartItems" runat="server">
                            <asp:Repeater ID="rptCartItems" runat="server" OnItemCommand="rptCartItems_ItemCommand">
                                <ItemTemplate>
                                    <div class="row border-bottom py-3">
                                        <div class="col-md-2">
                                            <img src='<%# GetMedicineImage(Eval("Medicine.ImagePath")) %>' 
                                                 alt='<%# Eval("Medicine.Name") %>' 
                                                 class="img-fluid rounded" style="max-height: 80px;">
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="mb-1"><%# Eval("Medicine.Name") %></h6>
                                            <small class="text-muted"><%# Eval("Medicine.GenericName") %></small><br>
                                            <small class="text-muted"><%# Eval("Medicine.DosageForm") %> - <%# Eval("Medicine.Strength") %></small>
                                            <%# (bool)Eval("Medicine.PrescriptionRequired") ? "<span class='badge bg-warning text-dark'>Prescription Required</span>" : "" %>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="input-group input-group-sm">
                                                <asp:Button ID="btnDecrease" runat="server" Text="-" CssClass="btn btn-outline-secondary" 
                                                    CommandName="UpdateQuantity" CommandArgument='<%# Eval("CartItemId") + ",-1" %>' />
                                                <input type="text" class="form-control text-center" value='<%# Eval("Quantity") %>' readonly>
                                                <asp:Button ID="btnIncrease" runat="server" Text="+" CssClass="btn btn-outline-secondary" 
                                                    CommandName="UpdateQuantity" CommandArgument='<%# Eval("CartItemId") + ",1" %>' />
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <div class="fw-bold">$<%# Eval("TotalPrice", "{0:N2}") %></div>
                                            <%# (decimal)Eval("Medicine.DiscountPercentage") > 0 ? 
                                                "<small class='text-success'>Save " + Eval("Medicine.DiscountPercentage", "{0:N0}") + "%</small>" : "" %>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <asp:Button ID="btnRemove" runat="server" Text="Remove" CssClass="btn btn-sm btn-outline-danger" 
                                                CommandName="RemoveItem" CommandArgument='<%# Eval("CartItemId") %>' 
                                                OnClientClick="return confirm('Are you sure you want to remove this item?');" />
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </asp:Panel>
                    </div>
                </div>

                <!-- Continue Shopping -->
                <div class="mt-3">
                    <asp:LinkButton ID="lnkContinueShoppingBottom" runat="server" CssClass="btn btn-outline-primary" OnClick="lnkContinueShopping_Click">
                        <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                    </asp:LinkButton>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card shadow">
                    <div class="card-header">
                        <h5 class="mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>$<asp:Label ID="lblSubtotal" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Discount:</span>
                            <span class="text-success">-$<asp:Label ID="lblDiscount" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span>$<asp:Label ID="lblTax" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span>$<asp:Label ID="lblShipping" runat="server" Text="0.00"></asp:Label></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total:</strong>
                            <strong>$<asp:Label ID="lblTotal" runat="server" Text="0.00"></asp:Label></strong>
                        </div>

                        <!-- Coupon Code -->
                        <div class="mb-3">
                            <label class="form-label">Coupon Code</label>
                            <div class="input-group">
                                <asp:TextBox ID="txtCouponCode" runat="server" CssClass="form-control" placeholder="Enter coupon code"></asp:TextBox>
                                <asp:Button ID="btnApplyCoupon" runat="server" Text="Apply" CssClass="btn btn-outline-secondary" OnClick="btnApplyCoupon_Click" />
                            </div>
                        </div>

                        <!-- Checkout Button -->
                        <asp:Button ID="btnCheckout" runat="server" Text="Proceed to Checkout" CssClass="btn btn-success btn-lg w-100" OnClick="btnCheckout_Click" />
                        
                        <!-- Guest Checkout -->
                        <asp:Panel ID="pnlGuestCheckout" runat="server" Visible="false" CssClass="mt-2">
                            <div class="text-center">
                                <small class="text-muted">or</small>
                            </div>
                            <asp:LinkButton ID="lnkGuestCheckout" runat="server" CssClass="btn btn-outline-secondary w-100 mt-2" OnClick="lnkGuestCheckout_Click">
                                Checkout as Guest
                            </asp:LinkButton>
                        </asp:Panel>
                    </div>
                </div>

                <!-- Security Info -->
                <div class="card shadow mt-3">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt text-success fa-2x mb-2"></i>
                        <h6>Secure Checkout</h6>
                        <small class="text-muted">Your information is protected with 256-bit SSL encryption</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
