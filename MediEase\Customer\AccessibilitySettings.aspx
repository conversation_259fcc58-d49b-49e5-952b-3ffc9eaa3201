<%@ Page Title="Accessibility Settings" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AccessibilitySettings.aspx.cs" Inherits="MediEase.Customer.AccessibilitySettings" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-universal-access me-2 text-primary"></i>Accessibility Settings</h2>
                        <p class="text-muted">Customize your experience for better accessibility</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                            <i class="fas fa-undo me-2"></i>Reset to Defaults
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Accessibility Options -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Accessibility Options</h5>
                    </div>
                    <div class="card-body">
                        <!-- Visual Settings -->
                        <div class="accessibility-section mb-4">
                            <h6 class="text-primary"><i class="fas fa-eye me-2"></i>Visual Settings</h6>
                            <hr>
                            
                            <!-- Theme Selection -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Theme</label>
                                    <div class="btn-group w-100" role="group" id="themeSelector">
                                        <input type="radio" class="btn-check" name="theme" id="lightTheme" value="light" checked>
                                        <label class="btn btn-outline-primary" for="lightTheme">
                                            <i class="fas fa-sun me-2"></i>Light Mode
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="theme" id="darkTheme" value="dark">
                                        <label class="btn btn-outline-primary" for="darkTheme">
                                            <i class="fas fa-moon me-2"></i>Dark Mode
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="theme" id="highContrastTheme" value="high-contrast">
                                        <label class="btn btn-outline-primary" for="highContrastTheme">
                                            <i class="fas fa-adjust me-2"></i>High Contrast
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Color Scheme</label>
                                    <select class="form-select" id="colorScheme">
                                        <option value="default">Default</option>
                                        <option value="blue">Blue Friendly</option>
                                        <option value="green">Green Friendly</option>
                                        <option value="monochrome">Monochrome</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Font Size -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Font Size</label>
                                    <div class="font-size-controls">
                                        <input type="range" class="form-range" id="fontSizeSlider" min="12" max="24" value="16" step="1">
                                        <div class="d-flex justify-content-between">
                                            <small>Small (12px)</small>
                                            <small>Normal (16px)</small>
                                            <small>Large (24px)</small>
                                        </div>
                                        <div class="text-center mt-2">
                                            <span class="badge bg-primary" id="fontSizeDisplay">16px</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Font Family</label>
                                    <select class="form-select" id="fontFamily">
                                        <option value="default">Default</option>
                                        <option value="arial">Arial</option>
                                        <option value="helvetica">Helvetica</option>
                                        <option value="verdana">Verdana</option>
                                        <option value="georgia">Georgia</option>
                                        <option value="times">Times New Roman</option>
                                        <option value="dyslexic">OpenDyslexic</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Visual Enhancements -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="reduceMotion">
                                        <label class="form-check-label" for="reduceMotion">
                                            <strong>Reduce Motion</strong> - Minimize animations and transitions
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="increaseFocus">
                                        <label class="form-check-label" for="increaseFocus">
                                            <strong>Enhanced Focus</strong> - Highlight focused elements
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="underlineLinks">
                                        <label class="form-check-label" for="underlineLinks">
                                            <strong>Underline Links</strong> - Always underline clickable links
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Audio Settings -->
                        <div class="accessibility-section mb-4">
                            <h6 class="text-success"><i class="fas fa-volume-up me-2"></i>Audio Settings</h6>
                            <hr>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableTTS">
                                        <label class="form-check-label" for="enableTTS">
                                            <strong>Text-to-Speech</strong> - Read page content aloud
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableSoundEffects">
                                        <label class="form-check-label" for="enableSoundEffects">
                                            <strong>Sound Effects</strong> - Audio feedback for actions
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Speech Rate</label>
                                    <input type="range" class="form-range" id="speechRate" min="0.5" max="2" value="1" step="0.1">
                                    <div class="d-flex justify-content-between">
                                        <small>Slow</small>
                                        <small>Normal</small>
                                        <small>Fast</small>
                                    </div>
                                    
                                    <label class="form-label mt-3">Voice</label>
                                    <select class="form-select" id="voiceSelection">
                                        <option value="default">Default Voice</option>
                                    </select>
                                </div>
                            </div>

                            <!-- TTS Controls -->
                            <div class="tts-controls mt-3" id="ttsControls" style="display: none;">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>Text-to-Speech Controls</h6>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-success btn-sm" id="ttsPlay">
                                                <i class="fas fa-play"></i> Play
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" id="ttsPause">
                                                <i class="fas fa-pause"></i> Pause
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" id="ttsStop">
                                                <i class="fas fa-stop"></i> Stop
                                            </button>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">Click on any text to have it read aloud</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Settings -->
                        <div class="accessibility-section mb-4">
                            <h6 class="text-info"><i class="fas fa-keyboard me-2"></i>Navigation Settings</h6>
                            <hr>
                            
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableKeyboardNav">
                                        <label class="form-check-label" for="enableKeyboardNav">
                                            <strong>Enhanced Keyboard Navigation</strong> - Improved keyboard shortcuts
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="skipLinks">
                                        <label class="form-check-label" for="skipLinks">
                                            <strong>Skip Links</strong> - Quick navigation to main content
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="showShortcuts">
                                        <label class="form-check-label" for="showShortcuts">
                                            <strong>Show Keyboard Shortcuts</strong> - Display available shortcuts
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Save Settings -->
                        <div class="text-center">
                            <asp:Button ID="btnSaveSettings" runat="server" CssClass="btn btn-primary btn-lg" 
                                Text="Save Accessibility Settings" OnClick="btnSaveSettings_Click" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Panel -->
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Live Preview</h6>
                    </div>
                    <div class="card-body" id="previewPanel">
                        <div class="preview-content">
                            <h5>Sample Content</h5>
                            <p>This is how your text will appear with the current settings. You can see the font size, color scheme, and other visual changes in real-time.</p>
                            <a href="#" class="sample-link">Sample Link</a>
                            <div class="mt-3">
                                <button class="btn btn-primary btn-sm">Sample Button</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Keyboard Shortcuts -->
                <div class="card shadow-sm mt-4" id="shortcutsPanel" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-keyboard me-2"></i>Keyboard Shortcuts</h6>
                    </div>
                    <div class="card-body">
                        <div class="shortcut-list">
                            <div class="shortcut-item mb-2">
                                <kbd>Alt + H</kbd> <span class="ms-2">Go to Home</span>
                            </div>
                            <div class="shortcut-item mb-2">
                                <kbd>Alt + M</kbd> <span class="ms-2">Browse Medicines</span>
                            </div>
                            <div class="shortcut-item mb-2">
                                <kbd>Alt + C</kbd> <span class="ms-2">View Cart</span>
                            </div>
                            <div class="shortcut-item mb-2">
                                <kbd>Alt + S</kbd> <span class="ms-2">Search</span>
                            </div>
                            <div class="shortcut-item mb-2">
                                <kbd>Ctrl + /</kbd> <span class="ms-2">Show/Hide Shortcuts</span>
                            </div>
                            <div class="shortcut-item mb-2">
                                <kbd>Space</kbd> <span class="ms-2">Play/Pause TTS</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Accessibility Toolbar (Fixed Position) -->
    <div class="accessibility-toolbar" id="accessibilityToolbar">
        <button type="button" class="btn btn-primary btn-sm" id="toolbarToggle" title="Accessibility Options">
            <i class="fas fa-universal-access"></i>
        </button>
        <div class="toolbar-content" id="toolbarContent" style="display: none;">
            <div class="toolbar-item">
                <button type="button" class="btn btn-outline-light btn-sm" onclick="adjustFontSize(1)" title="Increase Font Size">
                    <i class="fas fa-plus"></i> A+
                </button>
            </div>
            <div class="toolbar-item">
                <button type="button" class="btn btn-outline-light btn-sm" onclick="adjustFontSize(-1)" title="Decrease Font Size">
                    <i class="fas fa-minus"></i> A-
                </button>
            </div>
            <div class="toolbar-item">
                <button type="button" class="btn btn-outline-light btn-sm" onclick="toggleTheme()" title="Toggle Dark Mode">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
            <div class="toolbar-item">
                <button type="button" class="btn btn-outline-light btn-sm" onclick="toggleTTS()" title="Toggle Text-to-Speech">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
        </div>
    </div>

    <style>
        .accessibility-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
        }

        .font-size-controls .form-range {
            margin-bottom: 10px;
        }

        .preview-content {
            transition: all 0.3s ease;
        }

        .accessibility-toolbar {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1050;
            background: rgba(0, 123, 255, 0.9);
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .toolbar-content {
            margin-top: 10px;
        }

        .toolbar-item {
            margin-bottom: 5px;
        }

        .shortcut-item {
            display: flex;
            align-items: center;
        }

        .shortcut-item kbd {
            min-width: 80px;
            text-align: center;
        }

        /* Dark Theme Styles */
        .dark-theme {
            background-color: #121212 !important;
            color: #ffffff !important;
        }

        .dark-theme .card {
            background-color: #1e1e1e !important;
            border-color: #333 !important;
            color: #ffffff !important;
        }

        .dark-theme .card-header {
            background-color: #2d2d2d !important;
            border-color: #333 !important;
        }

        .dark-theme .form-control,
        .dark-theme .form-select {
            background-color: #2d2d2d !important;
            border-color: #555 !important;
            color: #ffffff !important;
        }

        /* High Contrast Theme */
        .high-contrast-theme {
            background-color: #000000 !important;
            color: #ffffff !important;
        }

        .high-contrast-theme .card {
            background-color: #000000 !important;
            border: 2px solid #ffffff !important;
            color: #ffffff !important;
        }

        .high-contrast-theme .btn-primary {
            background-color: #ffffff !important;
            color: #000000 !important;
            border-color: #ffffff !important;
        }

        /* Enhanced Focus */
        .enhanced-focus *:focus {
            outline: 3px solid #ff6b35 !important;
            outline-offset: 2px !important;
        }

        /* Reduced Motion */
        .reduced-motion * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }

        /* Underlined Links */
        .underlined-links a {
            text-decoration: underline !important;
        }

        /* Large Font Sizes */
        .font-size-18 { font-size: 18px !important; }
        .font-size-20 { font-size: 20px !important; }
        .font-size-22 { font-size: 22px !important; }
        .font-size-24 { font-size: 24px !important; }
    </style>

    <script>
        // Text-to-Speech functionality
        let speechSynthesis = window.speechSynthesis;
        let currentUtterance = null;

        // Initialize accessibility settings
        $(document).ready(function() {
            loadAccessibilitySettings();
            initializeTTS();
            setupEventListeners();
            populateVoices();
        });

        function setupEventListeners() {
            // Font size slider
            $('#fontSizeSlider').on('input', function() {
                const fontSize = $(this).val();
                $('#fontSizeDisplay').text(fontSize + 'px');
                updatePreview();
            });

            // Theme selection
            $('input[name="theme"]').on('change', function() {
                updatePreview();
            });

            // Color scheme
            $('#colorScheme').on('change', function() {
                updatePreview();
            });

            // Font family
            $('#fontFamily').on('change', function() {
                updatePreview();
            });

            // Accessibility toggles
            $('.form-check-input').on('change', function() {
                updatePreview();
                if (this.id === 'enableTTS') {
                    $('#ttsControls').toggle(this.checked);
                }
                if (this.id === 'showShortcuts') {
                    $('#shortcutsPanel').toggle(this.checked);
                }
            });

            // TTS controls
            $('#ttsPlay').on('click', function() {
                const text = $('#previewPanel .preview-content').text();
                speakText(text);
            });

            $('#ttsPause').on('click', function() {
                if (speechSynthesis.speaking) {
                    speechSynthesis.pause();
                }
            });

            $('#ttsStop').on('click', function() {
                speechSynthesis.cancel();
            });

            // Toolbar toggle
            $('#toolbarToggle').on('click', function() {
                $('#toolbarContent').toggle();
            });

            // Click to speak
            $(document).on('click', '.preview-content *', function(e) {
                if ($('#enableTTS').is(':checked')) {
                    e.preventDefault();
                    const text = $(this).text().trim();
                    if (text) {
                        speakText(text);
                    }
                }
            });
        }

        function updatePreview() {
            const preview = $('#previewPanel .preview-content');
            const fontSize = $('#fontSizeSlider').val();
            const theme = $('input[name="theme"]:checked').val();
            const colorScheme = $('#colorScheme').val();
            const fontFamily = $('#fontFamily').val();

            // Apply font size
            preview.css('font-size', fontSize + 'px');

            // Apply font family
            if (fontFamily !== 'default') {
                preview.css('font-family', fontFamily);
            }

            // Apply theme
            $('body').removeClass('dark-theme high-contrast-theme');
            if (theme === 'dark') {
                $('body').addClass('dark-theme');
            } else if (theme === 'high-contrast') {
                $('body').addClass('high-contrast-theme');
            }

            // Apply accessibility options
            $('body').toggleClass('enhanced-focus', $('#increaseFocus').is(':checked'));
            $('body').toggleClass('reduced-motion', $('#reduceMotion').is(':checked'));
            $('body').toggleClass('underlined-links', $('#underlineLinks').is(':checked'));
        }

        function initializeTTS() {
            if ('speechSynthesis' in window) {
                $('#enableTTS').prop('disabled', false);
            } else {
                $('#enableTTS').prop('disabled', true);
                $('#enableTTS').parent().append('<small class="text-muted d-block">Text-to-Speech not supported in this browser</small>');
            }
        }

        function populateVoices() {
            const voices = speechSynthesis.getVoices();
            const voiceSelect = $('#voiceSelection');
            voiceSelect.empty();
            
            voices.forEach((voice, index) => {
                const option = $('<option></option>');
                option.val(index);
                option.text(`${voice.name} (${voice.lang})`);
                voiceSelect.append(option);
            });
        }

        function speakText(text) {
            if (!$('#enableTTS').is(':checked')) return;

            speechSynthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            const voices = speechSynthesis.getVoices();
            const selectedVoice = $('#voiceSelection').val();
            
            if (selectedVoice && voices[selectedVoice]) {
                utterance.voice = voices[selectedVoice];
            }
            
            utterance.rate = parseFloat($('#speechRate').val());
            utterance.pitch = 1;
            utterance.volume = 1;
            
            speechSynthesis.speak(utterance);
            currentUtterance = utterance;
        }

        // Toolbar functions
        function adjustFontSize(delta) {
            const slider = $('#fontSizeSlider');
            const currentSize = parseInt(slider.val());
            const newSize = Math.max(12, Math.min(24, currentSize + delta));
            slider.val(newSize);
            $('#fontSizeDisplay').text(newSize + 'px');
            updatePreview();
        }

        function toggleTheme() {
            const currentTheme = $('input[name="theme"]:checked').val();
            if (currentTheme === 'light') {
                $('#darkTheme').prop('checked', true);
            } else {
                $('#lightTheme').prop('checked', true);
            }
            updatePreview();
        }

        function toggleTTS() {
            const ttsEnabled = $('#enableTTS').is(':checked');
            $('#enableTTS').prop('checked', !ttsEnabled);
            $('#ttsControls').toggle(!ttsEnabled);
        }

        function resetToDefaults() {
            if (confirm('Reset all accessibility settings to defaults?')) {
                $('#fontSizeSlider').val(16);
                $('#lightTheme').prop('checked', true);
                $('#colorScheme').val('default');
                $('#fontFamily').val('default');
                $('.form-check-input').prop('checked', false);
                $('#speechRate').val(1);
                updatePreview();
            }
        }

        function loadAccessibilitySettings() {
            // Load saved settings from localStorage or server
            const settings = JSON.parse(localStorage.getItem('accessibilitySettings') || '{}');
            
            if (settings.fontSize) $('#fontSizeSlider').val(settings.fontSize);
            if (settings.theme) $(`input[name="theme"][value="${settings.theme}"]`).prop('checked', true);
            if (settings.colorScheme) $('#colorScheme').val(settings.colorScheme);
            if (settings.fontFamily) $('#fontFamily').val(settings.fontFamily);
            
            Object.keys(settings).forEach(key => {
                if (key.startsWith('enable') || key.startsWith('increase') || key.startsWith('underline') || key.startsWith('reduce') || key.startsWith('show')) {
                    $(`#${key}`).prop('checked', settings[key]);
                }
            });
            
            updatePreview();
        }

        function saveAccessibilitySettings() {
            const settings = {
                fontSize: $('#fontSizeSlider').val(),
                theme: $('input[name="theme"]:checked').val(),
                colorScheme: $('#colorScheme').val(),
                fontFamily: $('#fontFamily').val(),
                enableTTS: $('#enableTTS').is(':checked'),
                enableSoundEffects: $('#enableSoundEffects').is(':checked'),
                enableKeyboardNav: $('#enableKeyboardNav').is(':checked'),
                skipLinks: $('#skipLinks').is(':checked'),
                showShortcuts: $('#showShortcuts').is(':checked'),
                reduceMotion: $('#reduceMotion').is(':checked'),
                increaseFocus: $('#increaseFocus').is(':checked'),
                underlineLinks: $('#underlineLinks').is(':checked'),
                speechRate: $('#speechRate').val(),
                voiceSelection: $('#voiceSelection').val()
            };
            
            localStorage.setItem('accessibilitySettings', JSON.stringify(settings));
            return settings;
        }

        // Keyboard shortcuts
        $(document).keydown(function(e) {
            if (!$('#enableKeyboardNav').is(':checked')) return;
            
            if (e.altKey) {
                switch(e.key.toLowerCase()) {
                    case 'h':
                        e.preventDefault();
                        window.location.href = '/Default.aspx';
                        break;
                    case 'm':
                        e.preventDefault();
                        window.location.href = '/Medicines.aspx';
                        break;
                    case 'c':
                        e.preventDefault();
                        window.location.href = '/Cart.aspx';
                        break;
                    case 's':
                        e.preventDefault();
                        $('input[type="search"], input[placeholder*="search"]').first().focus();
                        break;
                }
            }
            
            if (e.ctrlKey && e.key === '/') {
                e.preventDefault();
                $('#shortcutsPanel').toggle();
            }
            
            if (e.key === ' ' && $('#enableTTS').is(':checked')) {
                e.preventDefault();
                if (speechSynthesis.speaking) {
                    if (speechSynthesis.paused) {
                        speechSynthesis.resume();
                    } else {
                        speechSynthesis.pause();
                    }
                }
            }
        });

        // Voice loading event
        if (speechSynthesis.onvoiceschanged !== undefined) {
            speechSynthesis.onvoiceschanged = populateVoices;
        }
    </script>
</asp:Content>
