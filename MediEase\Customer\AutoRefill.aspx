<%@ Page Title="Auto-Refill Setup" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AutoRefill.aspx.cs" Inherits="MediEase.Customer.AutoRefill" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-sync-alt me-2 text-success"></i>Auto-Refill Management</h2>
                        <p class="text-muted">Set up automatic refills for your regular medications</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAutoRefillModal">
                            <i class="fas fa-plus me-2"></i>Add Auto-Refill
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Auto-Refill Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-sync fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActiveRefills" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Auto-Refills</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblUpcomingRefills" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Upcoming This Week</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblPendingApproval" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Pending Approval</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>$<asp:Label ID="lblMonthlySavings" runat="server" Text="0.00"></asp:Label></h4>
                        <p class="mb-0">Monthly Savings</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Auto-Refills -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Your Auto-Refills</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <asp:Button ID="btnFilterActive" runat="server" CssClass="btn btn-outline-primary active" Text="Active" OnClick="btnFilterActive_Click" />
                            <asp:Button ID="btnFilterPaused" runat="server" CssClass="btn btn-outline-warning" Text="Paused" OnClick="btnFilterPaused_Click" />
                            <asp:Button ID="btnFilterAll" runat="server" CssClass="btn btn-outline-secondary" Text="All" OnClick="btnFilterAll_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <asp:Repeater ID="rptAutoRefills" runat="server" OnItemCommand="rptAutoRefills_ItemCommand">
                                <ItemTemplate>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="card auto-refill-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %> fs-6">
                                                    <%# Eval("Status") %>
                                                </span>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item edit-refill" href="#" data-refill-id='<%# Eval("AutoRefillId") %>'>
                                                            <i class="fas fa-edit me-2"></i>Edit Settings</a></li>
                                                        <li><a class="dropdown-item view-history" href="#" data-refill-id='<%# Eval("AutoRefillId") %>'>
                                                            <i class="fas fa-history me-2"></i>View History</a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><asp:LinkButton runat="server" CssClass="dropdown-item" 
                                                            CommandName="ToggleStatus" CommandArgument='<%# Eval("AutoRefillId") %>'>
                                                            <i class="fas fa-<%# Convert.ToBoolean(Eval("IsActive")) ? "pause" : "play" %> me-2"></i>
                                                            <%# Convert.ToBoolean(Eval("IsActive")) ? "Pause" : "Resume" %>
                                                        </asp:LinkButton></li>
                                                        <li><asp:LinkButton runat="server" CssClass="dropdown-item text-danger" 
                                                            CommandName="Cancel" CommandArgument='<%# Eval("AutoRefillId") %>'
                                                            OnClientClick="return confirm('Are you sure you want to cancel this auto-refill?');">
                                                            <i class="fas fa-times me-2"></i>Cancel
                                                        </asp:LinkButton></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="medicine-info mb-3">
                                                    <div class="d-flex align-items-center">
                                                        <img src='<%# GetMedicineImage(Eval("Medicine.ImagePath")) %>' 
                                                             alt="Medicine" class="rounded me-3" width="60" height="60" />
                                                        <div>
                                                            <h6 class="text-primary mb-1"><%# Eval("Medicine.Name") %></h6>
                                                            <small class="text-muted"><%# Eval("Medicine.GenericName") %></small><br>
                                                            <small class="text-muted"><%# Eval("Medicine.Strength") %></small>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="refill-details">
                                                    <div class="row text-center mb-3">
                                                        <div class="col-4">
                                                            <small class="text-muted">Quantity</small>
                                                            <div class="fw-bold"><%# Eval("Quantity") %></div>
                                                        </div>
                                                        <div class="col-4">
                                                            <small class="text-muted">Frequency</small>
                                                            <div class="fw-bold"><%# GetFrequencyText(Convert.ToInt32(Eval("RefillFrequencyDays"))) %></div>
                                                        </div>
                                                        <div class="col-4">
                                                            <small class="text-muted">Price</small>
                                                            <div class="fw-bold text-success">$<%# String.Format("{0:F2}", Eval("UnitPrice")) %></div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="next-refill-info p-3 bg-light rounded">
                                                        <div class="row">
                                                            <div class="col-6">
                                                                <small class="text-muted">Next Refill</small>
                                                                <div class="fw-bold text-<%# GetNextRefillColor(Convert.ToDateTime(Eval("NextRefillDate"))) %>">
                                                                    <%# Convert.ToDateTime(Eval("NextRefillDate")).ToString("MMM dd, yyyy") %>
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <small class="text-muted">Days Remaining</small>
                                                                <div class="fw-bold">
                                                                    <%# GetDaysUntilRefill(Convert.ToDateTime(Eval("NextRefillDate"))) %> days
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <%# !string.IsNullOrEmpty(Eval("Notes")?.ToString()) ? 
                                                    "<div class='mt-3'><small class='text-muted'><strong>Notes:</strong> " + Eval("Notes") + "</small></div>" : "" %>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-grid gap-2">
                                                    <div class="btn-group">
                                                        <button class="btn btn-outline-primary btn-sm refill-now" data-refill-id='<%# Eval("AutoRefillId") %>'>
                                                            <i class="fas fa-shopping-cart me-1"></i>Refill Now
                                                        </button>
                                                        <button class="btn btn-outline-info btn-sm edit-refill" data-refill-id='<%# Eval("AutoRefillId") %>'>
                                                            <i class="fas fa-edit me-1"></i>Edit
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>

                        <!-- No Auto-Refills Message -->
                        <asp:Panel ID="pnlNoRefills" runat="server" Visible="false">
                            <div class="text-center py-5">
                                <i class="fas fa-sync-alt fa-3x text-muted mb-3"></i>
                                <h4>No auto-refills set up yet</h4>
                                <p class="text-muted">Set up automatic refills for your regular medications to never run out.</p>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAutoRefillModal">
                                    <i class="fas fa-plus me-2"></i>Set Up Your First Auto-Refill
                                </button>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>

        <!-- Benefits Section -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Auto-Refill Benefits</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <i class="fas fa-clock fa-3x text-success mb-3"></i>
                                <h6>Never Run Out</h6>
                                <p class="small text-muted">Automatic refills ensure you never miss your medication.</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <i class="fas fa-percentage fa-3x text-success mb-3"></i>
                                <h6>Save Money</h6>
                                <p class="small text-muted">Get discounts on auto-refill orders and bulk purchases.</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <i class="fas fa-shipping-fast fa-3x text-success mb-3"></i>
                                <h6>Free Delivery</h6>
                                <p class="small text-muted">Free shipping on all auto-refill orders.</p>
                            </div>
                            <div class="col-md-3 text-center">
                                <i class="fas fa-mobile-alt fa-3x text-success mb-3"></i>
                                <h6>Smart Reminders</h6>
                                <p class="small text-muted">Get notifications before each refill is processed.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Auto-Refill Modal -->
    <div class="modal fade" id="addAutoRefillModal" tabindex="-1" aria-labelledby="addAutoRefillModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAutoRefillModalLabel">Set Up Auto-Refill</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="autoRefillForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Select Medicine *</label>
                                    <asp:DropDownList ID="ddlMedicine" runat="server" CssClass="form-select" required>
                                        <asp:ListItem Value="">Choose a medicine...</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Quantity *</label>
                                    <asp:TextBox ID="txtQuantity" runat="server" CssClass="form-control" TextMode="Number" min="1" required></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Refill Frequency *</label>
                                    <asp:DropDownList ID="ddlFrequency" runat="server" CssClass="form-select" required>
                                        <asp:ListItem Value="">Select frequency...</asp:ListItem>
                                        <asp:ListItem Value="7">Weekly (7 days)</asp:ListItem>
                                        <asp:ListItem Value="14">Bi-weekly (14 days)</asp:ListItem>
                                        <asp:ListItem Value="30">Monthly (30 days)</asp:ListItem>
                                        <asp:ListItem Value="60">Bi-monthly (60 days)</asp:ListItem>
                                        <asp:ListItem Value="90">Quarterly (90 days)</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">First Refill Date *</label>
                                    <asp:TextBox ID="txtFirstRefillDate" runat="server" CssClass="form-control" TextMode="Date" required></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Delivery Address</label>
                                    <asp:DropDownList ID="ddlDeliveryAddress" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="">Use default address</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Payment Method</label>
                                    <asp:DropDownList ID="ddlPaymentMethod" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="">Use default payment</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Special Instructions</label>
                            <asp:TextBox ID="txtNotes" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                placeholder="Any special delivery instructions or notes..."></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkEmailReminders" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkEmailReminders.ClientID %>">
                                    Send email reminders 3 days before each refill
                                </label>
                            </div>
                            <div class="form-check">
                                <asp:CheckBox ID="chkSMSReminders" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkSMSReminders.ClientID %>">
                                    Send SMS reminders (additional charges may apply)
                                </label>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> Auto-refills will be processed automatically on the scheduled date. 
                            You can pause or cancel at any time. Free shipping is included for all auto-refill orders.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSaveAutoRefill" runat="server" CssClass="btn btn-success" Text="Set Up Auto-Refill" OnClick="btnSaveAutoRefill_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Auto-Refill Details Modal -->
    <div class="modal fade" id="refillDetailsModal" tabindex="-1" aria-labelledby="refillDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="refillDetailsModalLabel">Auto-Refill Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="refillDetails">
                        <!-- Refill details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="refillActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .auto-refill-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .auto-refill-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .next-refill-info {
            border-left: 4px solid #28a745;
        }

        .medicine-info img {
            border: 2px solid #e9ecef;
        }
    </style>

    <script>
        // Edit auto-refill
        $(document).on('click', '.edit-refill', function(e) {
            e.preventDefault();
            const refillId = $(this).data('refill-id');
            loadEditRefillForm(refillId);
        });

        // View refill history
        $(document).on('click', '.view-history', function(e) {
            e.preventDefault();
            const refillId = $(this).data('refill-id');
            loadRefillHistory(refillId);
        });

        // Refill now
        $(document).on('click', '.refill-now', function(e) {
            e.preventDefault();
            const refillId = $(this).data('refill-id');
            processImmediateRefill(refillId);
        });

        function loadEditRefillForm(refillId) {
            $.ajax({
                type: 'POST',
                url: 'AutoRefill.aspx/GetEditRefillForm',
                data: JSON.stringify({ autoRefillId: refillId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#refillDetails').html(response.d.html);
                        $('#refillDetailsModalLabel').text('Edit Auto-Refill');
                        $('#refillDetailsModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading edit form.');
                }
            });
        }

        function loadRefillHistory(refillId) {
            $.ajax({
                type: 'POST',
                url: 'AutoRefill.aspx/GetRefillHistory',
                data: JSON.stringify({ autoRefillId: refillId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#refillDetails').html(response.d.html);
                        $('#refillDetailsModalLabel').text('Refill History');
                        $('#refillDetailsModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading refill history.');
                }
            });
        }

        function processImmediateRefill(refillId) {
            if (confirm('Process this refill immediately? This will create an order and charge your default payment method.')) {
                $.ajax({
                    type: 'POST',
                    url: 'AutoRefill.aspx/ProcessImmediateRefill',
                    data: JSON.stringify({ autoRefillId: refillId }),
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    success: function(response) {
                        if (response.d.success) {
                            showSuccessMessage('Refill processed successfully! Order #' + response.d.orderNumber);
                            location.reload();
                        } else {
                            showErrorMessage(response.d.message);
                        }
                    },
                    error: function() {
                        showErrorMessage('Error processing refill.');
                    }
                });
            }
        }

        // Set minimum date for first refill
        $(document).ready(function() {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            const dateString = tomorrow.toISOString().split('T')[0];
            $('#<%= txtFirstRefillDate.ClientID %>').attr('min', dateString);
        });
    </script>
</asp:Content>
