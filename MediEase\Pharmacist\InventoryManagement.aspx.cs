using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Pharmacist
{
    public partial class InventoryManagement : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a pharmacist
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadCategories();
                LoadInventory();
                UpdateInventoryStats();
            }
        }

        private void LoadCategories()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var categories = db.Medicines
                        .Where(m => !string.IsNullOrEmpty(m.Category))
                        .Select(m => m.Category)
                        .Distinct()
                        .OrderBy(c => c)
                        .ToList();

                    foreach (var category in categories)
                    {
                        ddlCategoryFilter.Items.Add(new ListItem(category, category));
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading categories");
            }
        }

        private void LoadInventory()
        {
            try
            {
                var categoryFilter = ddlCategoryFilter.SelectedValue;
                var stockFilter = ddlStockFilter.SelectedValue;
                var expiryFilter = ddlExpiryFilter.SelectedValue;
                var searchTerm = txtSearch.Text.Trim();

                using (var db = new MediEaseContext())
                {
                    var query = db.Medicines.AsQueryable();

                    // Apply category filter
                    if (!string.IsNullOrEmpty(categoryFilter))
                    {
                        query = query.Where(m => m.Category == categoryFilter);
                    }

                    // Apply stock filter
                    if (!string.IsNullOrEmpty(stockFilter))
                    {
                        switch (stockFilter)
                        {
                            case "InStock":
                                query = query.Where(m => m.StockQuantity > m.MinimumStockLevel);
                                break;
                            case "LowStock":
                                query = query.Where(m => m.StockQuantity <= m.MinimumStockLevel && m.StockQuantity > 0);
                                break;
                            case "OutOfStock":
                                query = query.Where(m => m.StockQuantity <= 0);
                                break;
                        }
                    }

                    // Apply expiry filter
                    if (!string.IsNullOrEmpty(expiryFilter))
                    {
                        var today = DateTime.Today;
                        var thisMonth = today.AddMonths(1);
                        var threeMonths = today.AddMonths(3);

                        switch (expiryFilter)
                        {
                            case "Expired":
                                query = query.Where(m => m.ExpiryDate < today);
                                break;
                            case "ExpiringThisMonth":
                                query = query.Where(m => m.ExpiryDate >= today && m.ExpiryDate < thisMonth);
                                break;
                            case "Expiring3Months":
                                query = query.Where(m => m.ExpiryDate >= today && m.ExpiryDate < threeMonths);
                                break;
                        }
                    }

                    // Apply search filter
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(m => 
                            m.Name.Contains(searchTerm) ||
                            m.GenericName.Contains(searchTerm) ||
                            m.SKU.Contains(searchTerm) ||
                            m.BatchNumber.Contains(searchTerm) ||
                            m.Barcode.Contains(searchTerm));
                    }

                    var medicines = query
                        .OrderBy(m => m.Category)
                        .ThenBy(m => m.Name)
                        .Select(m => new
                        {
                            m.MedicineId,
                            m.Name,
                            m.GenericName,
                            m.SKU,
                            m.Category,
                            m.Brand,
                            m.Strength,
                            m.StockQuantity,
                            MinimumStock = m.MinimumStockLevel,
                            m.Price,
                            CostPrice = (decimal?)null, // Medicine model doesn't have CostPrice
                            m.BatchNumber,
                            ManufactureDate = m.ManufacturingDate,
                            m.ExpiryDate,
                            m.IsActive,
                            RequiresPrescription = m.PrescriptionRequired,
                            m.ImagePath
                        })
                        .ToList();

                    if (medicines.Any())
                    {
                        gvInventory.DataSource = medicines;
                        gvInventory.DataBind();
                        
                        rptInventoryGrid.DataSource = medicines;
                        rptInventoryGrid.DataBind();

                        pnlNoMedicines.Visible = false;
                    }
                    else
                    {
                        gvInventory.DataSource = null;
                        gvInventory.DataBind();
                        
                        rptInventoryGrid.DataSource = null;
                        rptInventoryGrid.DataBind();

                        pnlNoMedicines.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading inventory");
                ShowErrorMessage("Error loading inventory. Please try again.");
            }
        }

        private void UpdateInventoryStats()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var totalMedicines = db.Medicines.Count();
                    var inStock = db.Medicines.Count(m => m.StockQuantity > m.MinimumStockLevel);
                    var lowStock = db.Medicines.Count(m => m.StockQuantity <= m.MinimumStockLevel && m.StockQuantity > 0);
                    var outOfStock = db.Medicines.Count(m => m.StockQuantity <= 0);

                    var today = DateTime.Today;
                    var thisMonth = today.AddMonths(1);
                    var threeMonths = today.AddMonths(3);

                    var expired = db.Medicines.Count(m => m.ExpiryDate < today);
                    var expiringThisMonth = db.Medicines.Count(m => m.ExpiryDate >= today && m.ExpiryDate < thisMonth);
                    var expiringNext3Months = db.Medicines.Count(m => m.ExpiryDate >= today && m.ExpiryDate < threeMonths);

                    lblTotalMedicines.Text = totalMedicines.ToString();
                    lblInStock.Text = inStock.ToString();
                    lblLowStock.Text = lowStock.ToString();
                    lblOutOfStock.Text = outOfStock.ToString();

                    lblExpiredMedicines.Text = expired.ToString();
                    lblExpiringThisMonth.Text = expiringThisMonth.ToString();
                    lblExpiringNext3Months.Text = expiringNext3Months.ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating inventory stats");
            }
        }

        protected void ddlCategoryFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadInventory();
        }

        protected void ddlStockFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadInventory();
        }

        protected void ddlExpiryFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadInventory();
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadInventory();
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            // Clear filters and reload
            ddlCategoryFilter.SelectedValue = "";
            ddlStockFilter.SelectedValue = "";
            ddlExpiryFilter.SelectedValue = "";
            txtSearch.Text = "";
            LoadInventory();
            UpdateInventoryStats();
        }

        protected void gvInventory_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (int.TryParse(e.CommandArgument.ToString(), out int medicineId))
            {
                switch (e.CommandName)
                {
                    case "SetReorderAlert":
                        SetReorderAlert(medicineId);
                        break;
                }
            }
        }

        private void SetReorderAlert(int medicineId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var medicine = db.Medicines.Find(medicineId);
                    if (medicine != null)
                    {
                        // Create a reorder alert (you would implement this based on your requirements)
                        ErrorLogger.LogUserActivity($"Reorder alert set for medicine: {medicine.Name}", currentUser.UserId);
                        ShowSuccessMessage($"Reorder alert set for {medicine.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error setting reorder alert");
                ShowErrorMessage("Error setting reorder alert. Please try again.");
            }
        }

        [WebMethod]
        public static object GetMedicineDetails(int medicineId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin()))
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var medicine = db.Medicines.Find(medicineId);
                    if (medicine == null)
                    {
                        return new { success = false, message = "Medicine not found" };
                    }

                    var html = GenerateMedicineDetailsHtml(medicine);
                    var actions = GenerateMedicineActionsHtml(medicine);
                    return new { success = true, html = html, actions = actions };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting medicine details");
                return new { success = false, message = "Error loading medicine details" };
            }
        }

        [WebMethod]
        public static object GetStockUpdateForm(int medicineId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin()))
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var medicine = db.Medicines.Find(medicineId);
                    if (medicine == null)
                    {
                        return new { success = false, message = "Medicine not found" };
                    }

                    var html = GenerateStockUpdateFormHtml(medicine);
                    return new { success = true, html = html };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting stock update form");
                return new { success = false, message = "Error loading stock update form" };
            }
        }

        private static string GenerateMedicineDetailsHtml(Medicine medicine)
        {
            return $@"
                <div class='row'>
                    <div class='col-md-4 text-center'>
                        <img src='{GetMedicineImageStatic(medicine.ImagePath)}' alt='Medicine' class='rounded mb-3' width='200' height='200' />
                        <h4>{medicine.Name}</h4>
                        <p class='text-muted'>{medicine.GenericName}</p>
                    </div>
                    <div class='col-md-8'>
                        <div class='row'>
                            <div class='col-md-6'>
                                <h6>Basic Information</h6>
                                <p><strong>SKU:</strong> {medicine.SKU}</p>
                                <p><strong>Category:</strong> {medicine.Category}</p>
                                <p><strong>Brand:</strong> {medicine.Brand}</p>
                                <p><strong>Strength:</strong> {medicine.Strength}</p>
                                <p><strong>Form:</strong> {medicine.DosageForm ?? "Not specified"}</p>
                                <p><strong>Manufacturer:</strong> {medicine.Manufacturer}</p>
                            </div>
                            <div class='col-md-6'>
                                <h6>Stock Information</h6>
                                <p><strong>Current Stock:</strong> <span class='text-{GetStockColorStatic(medicine.StockQuantity, medicine.MinimumStockLevel)}'>{medicine.StockQuantity}</span></p>
                                <p><strong>Minimum Stock:</strong> {medicine.MinimumStockLevel}</p>
                                <p><strong>Unit:</strong> {medicine.Unit}</p>
                                <p><strong>Location:</strong> Not specified</p>
                                <p><strong>Status:</strong> {(medicine.IsActive ? "<span class='badge bg-success'>Active</span>" : "<span class='badge bg-danger'>Inactive</span>")}</p>
                            </div>
                        </div>
                        <div class='row mt-3'>
                            <div class='col-md-6'>
                                <h6>Pricing</h6>
                                <p><strong>Selling Price:</strong> ${medicine.Price:F2}</p>
                                <p><strong>Cost Price:</strong> Not available</p>
                                <p><strong>Profit Margin:</strong> Not calculated</p>
                            </div>
                            <div class='col-md-6'>
                                <h6>Batch Information</h6>
                                <p><strong>Batch Number:</strong> {medicine.BatchNumber}</p>
                                <p><strong>Manufacture Date:</strong> {(medicine.ManufacturingDate?.ToString("MM/dd/yyyy") ?? "Not specified")}</p>
                                <p><strong>Expiry Date:</strong> <span class='text-{GetExpiryColorStatic(medicine.ExpiryDate ?? DateTime.MaxValue)}'>{(medicine.ExpiryDate?.ToString("MM/dd/yyyy") ?? "Not specified")}</span></p>
                            </div>
                        </div>
                        <div class='row mt-3'>
                            <div class='col-12'>
                                <h6>Description</h6>
                                <p>{medicine.Description ?? "No description available"}</p>
                                {(medicine.PrescriptionRequired ? "<div class='alert alert-warning'><i class='fas fa-prescription me-2'></i>Prescription Required</div>" : "")}
                            </div>
                        </div>
                    </div>
                </div>";
        }

        private static string GenerateMedicineActionsHtml(Medicine medicine)
        {
            var actions = new List<string>();

            actions.Add($"<button class='btn btn-primary' onclick='editMedicine({medicine.MedicineId})'>Edit Medicine</button>");
            actions.Add($"<button class='btn btn-success' onclick='updateStock({medicine.MedicineId})'>Update Stock</button>");
            actions.Add($"<button class='btn btn-info' onclick='viewStockHistory({medicine.MedicineId})'>Stock History</button>");

            return string.Join(" ", actions);
        }

        private static string GenerateStockUpdateFormHtml(Medicine medicine)
        {
            return $@"
                <form id='stockUpdateForm'>
                    <input type='hidden' id='medicineId' value='{medicine.MedicineId}' />
                    <div class='mb-3'>
                        <h6>{medicine.Name}</h6>
                        <p class='text-muted'>Current Stock: {medicine.StockQuantity} {medicine.Unit}</p>
                    </div>
                    <div class='mb-3'>
                        <label class='form-label'>Update Type</label>
                        <select class='form-select' id='updateType' required>
                            <option value=''>Select update type</option>
                            <option value='Add'>Add Stock</option>
                            <option value='Remove'>Remove Stock</option>
                            <option value='Set'>Set Stock Level</option>
                        </select>
                    </div>
                    <div class='mb-3'>
                        <label class='form-label'>Quantity</label>
                        <input type='number' class='form-control' id='quantity' min='0' required />
                    </div>
                    <div class='mb-3'>
                        <label class='form-label'>Reason</label>
                        <textarea class='form-control' id='reason' rows='2' placeholder='Enter reason for stock update...'></textarea>
                    </div>
                    <div class='d-grid'>
                        <button type='button' class='btn btn-primary' onclick='saveStockUpdate()'>Update Stock</button>
                    </div>
                </form>";
        }

        // Helper methods for data binding
        protected string GetStockColor(int stockQuantity, int minimumStock)
        {
            return GetStockColorStatic(stockQuantity, minimumStock);
        }

        private static string GetStockColorStatic(int stockQuantity, int minimumStock)
        {
            if (stockQuantity <= 0) return "danger";
            if (stockQuantity <= minimumStock) return "warning";
            return "success";
        }

        protected string GetStockStatus(int stockQuantity, int minimumStock)
        {
            if (stockQuantity <= 0) return "Out of Stock";
            if (stockQuantity <= minimumStock) return "Low Stock";
            return "In Stock";
        }

        protected string GetStockStatusColor(int stockQuantity, int minimumStock)
        {
            if (stockQuantity <= 0) return "danger";
            if (stockQuantity <= minimumStock) return "warning";
            return "success";
        }

        protected string GetExpiryColor(DateTime expiryDate)
        {
            return GetExpiryColorStatic(expiryDate);
        }

        private static string GetExpiryColorStatic(DateTime expiryDate)
        {
            var today = DateTime.Today;
            var oneMonth = today.AddMonths(1);
            var threeMonths = today.AddMonths(3);

            if (expiryDate < today) return "danger";
            if (expiryDate < oneMonth) return "warning";
            if (expiryDate < threeMonths) return "info";
            return "success";
        }

        protected decimal GetProfitMargin(decimal price, decimal costPrice)
        {
            return GetProfitMarginStatic(price, costPrice);
        }

        private static decimal GetProfitMarginStatic(decimal price, decimal costPrice)
        {
            if (costPrice == 0) return 0;
            return ((price - costPrice) / costPrice) * 100;
        }

        protected string GetMedicineImage(object imagePath)
        {
            return GetMedicineImageStatic(imagePath?.ToString());
        }

        private static string GetMedicineImageStatic(string imagePath)
        {
            return string.IsNullOrEmpty(imagePath) ? "/Images/default-medicine.png" : imagePath;
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Inventory Management - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Manage medicine inventory, stock levels, batches, and expiry dates with MediEase inventory management system.");
                master.AddMetaKeywords("inventory management, medicine stock, batch management, expiry tracking, pharmacy inventory");
            }
        }
    }
}
