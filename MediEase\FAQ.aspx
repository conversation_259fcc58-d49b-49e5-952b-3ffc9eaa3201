<%@ Page Title="Frequently Asked Questions" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="FAQ.aspx.cs" Inherits="MediEase.FAQ" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="display-4 text-primary">Frequently Asked Questions</h1>
                <p class="lead text-muted">Find answers to common questions about MediEase services</p>
            </div>
        </div>

        <!-- Search FAQ -->
        <div class="row mb-4">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="input-group">
                            <asp:TextBox ID="txtSearchFAQ" runat="server" CssClass="form-control" placeholder="Search FAQs..."></asp:TextBox>
                            <asp:Button ID="btnSearchFAQ" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearchFAQ_Click" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Categories -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-center gap-2">
                    <asp:Button ID="btnAllCategories" runat="server" CssClass="btn btn-outline-primary active" Text="All" OnClick="btnCategory_Click" CommandArgument="All" />
                    <asp:Button ID="btnOrdering" runat="server" CssClass="btn btn-outline-primary" Text="Ordering" OnClick="btnCategory_Click" CommandArgument="Ordering" />
                    <asp:Button ID="btnPrescriptions" runat="server" CssClass="btn btn-outline-primary" Text="Prescriptions" OnClick="btnCategory_Click" CommandArgument="Prescriptions" />
                    <asp:Button ID="btnDelivery" runat="server" CssClass="btn btn-outline-primary" Text="Delivery" OnClick="btnCategory_Click" CommandArgument="Delivery" />
                    <asp:Button ID="btnPayment" runat="server" CssClass="btn btn-outline-primary" Text="Payment" OnClick="btnCategory_Click" CommandArgument="Payment" />
                    <asp:Button ID="btnAccount" runat="server" CssClass="btn btn-outline-primary" Text="Account" OnClick="btnCategory_Click" CommandArgument="Account" />
                    <asp:Button ID="btnTechnical" runat="server" CssClass="btn btn-outline-primary" Text="Technical" OnClick="btnCategory_Click" CommandArgument="Technical" />
                </div>
            </div>
        </div>

        <!-- FAQ Accordion -->
        <div class="row">
            <div class="col-12">
                <div class="accordion" id="faqAccordion">
                    
                    <!-- General Questions -->
                    <div class="accordion-item faq-item" data-category="General">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                <i class="fas fa-question-circle me-2 text-primary"></i>
                                What is MediEase and how does it work?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>MediEase is a comprehensive online pharmacy management system that allows you to:</p>
                                <ul>
                                    <li>Browse and search for medicines online</li>
                                    <li>Upload prescriptions for verification</li>
                                    <li>Get AI-powered medicine recommendations</li>
                                    <li>Track your orders in real-time</li>
                                    <li>Manage your health records and family profiles</li>
                                    <li>Earn loyalty points and get discounts</li>
                                </ul>
                                <p>Simply register for an account, upload your prescription or browse our medicine catalog, add items to your cart, and place your order for convenient home delivery.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Ordering Questions -->
                    <div class="accordion-item faq-item" data-category="Ordering">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                <i class="fas fa-shopping-cart me-2 text-primary"></i>
                                How do I place an order?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Placing an order is simple:</p>
                                <ol>
                                    <li><strong>Register/Login:</strong> Create an account or log in to your existing account</li>
                                    <li><strong>Browse or Search:</strong> Find medicines using our search feature or browse categories</li>
                                    <li><strong>Add to Cart:</strong> Select the medicines you need and add them to your cart</li>
                                    <li><strong>Upload Prescription:</strong> If required, upload your prescription for verification</li>
                                    <li><strong>Checkout:</strong> Review your order, enter delivery details, and choose payment method</li>
                                    <li><strong>Confirm:</strong> Complete your order and receive confirmation</li>
                                </ol>
                                <p>You'll receive order updates via email and SMS throughout the process.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item faq-item" data-category="Ordering">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                <i class="fas fa-edit me-2 text-primary"></i>
                                Can I modify or cancel my order?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Yes, you can modify or cancel your order under certain conditions:</p>
                                <ul>
                                    <li><strong>Before Processing:</strong> Orders can be modified or cancelled within 30 minutes of placement</li>
                                    <li><strong>During Processing:</strong> Contact our customer service team immediately</li>
                                    <li><strong>After Shipping:</strong> Orders cannot be cancelled but can be returned following our return policy</li>
                                </ul>
                                <p>To modify or cancel an order, go to "My Orders" in your account dashboard or contact our support team at +****************.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Prescription Questions -->
                    <div class="accordion-item faq-item" data-category="Prescriptions">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                <i class="fas fa-prescription me-2 text-primary"></i>
                                How do I upload my prescription?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Uploading your prescription is easy:</p>
                                <ol>
                                    <li><strong>Take a Clear Photo:</strong> Use your phone or scanner to capture a clear image of your prescription</li>
                                    <li><strong>Upload:</strong> Go to "Upload Prescription" in your account or during checkout</li>
                                    <li><strong>Fill Details:</strong> Enter patient information and any special instructions</li>
                                    <li><strong>Submit:</strong> Our pharmacists will verify your prescription within 2-4 hours</li>
                                </ol>
                                <p><strong>Supported formats:</strong> JPG, PNG, PDF (max 5MB per file)</p>
                                <p><strong>Tips for clear photos:</strong></p>
                                <ul>
                                    <li>Ensure good lighting</li>
                                    <li>Keep the prescription flat</li>
                                    <li>Include all pages if multiple</li>
                                    <li>Make sure text is readable</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item faq-item" data-category="Prescriptions">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                                <i class="fas fa-clock me-2 text-primary"></i>
                                How long does prescription verification take?
                            </button>
                        </h2>
                        <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Prescription verification times:</p>
                                <ul>
                                    <li><strong>Standard Prescriptions:</strong> 2-4 hours during business hours</li>
                                    <li><strong>Complex Prescriptions:</strong> 4-8 hours (may require doctor consultation)</li>
                                    <li><strong>Emergency Prescriptions:</strong> 30-60 minutes (marked as urgent)</li>
                                    <li><strong>After Hours:</strong> Next business day morning</li>
                                </ul>
                                <p>You'll receive notifications via email and SMS once your prescription is verified. Our pharmacists may contact you if they need clarification on any details.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Delivery Questions -->
                    <div class="accordion-item faq-item" data-category="Delivery">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq6">
                                <i class="fas fa-truck me-2 text-primary"></i>
                                What are your delivery options and charges?
                            </button>
                        </h2>
                        <div id="faq6" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>We offer multiple delivery options:</p>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Delivery Type</th>
                                                <th>Time</th>
                                                <th>Charge</th>
                                                <th>Minimum Order</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Standard Delivery</td>
                                                <td>2-3 business days</td>
                                                <td>$5.99</td>
                                                <td>None</td>
                                            </tr>
                                            <tr>
                                                <td>Express Delivery</td>
                                                <td>Next business day</td>
                                                <td>$12.99</td>
                                                <td>None</td>
                                            </tr>
                                            <tr>
                                                <td>Same Day Delivery</td>
                                                <td>Within 4-6 hours</td>
                                                <td>$19.99</td>
                                                <td>$25</td>
                                            </tr>
                                            <tr>
                                                <td>Free Delivery</td>
                                                <td>3-5 business days</td>
                                                <td>Free</td>
                                                <td>$50</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <p><strong>Note:</strong> Same day delivery is available in select areas only. Emergency prescriptions get priority delivery.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Questions -->
                    <div class="accordion-item faq-item" data-category="Payment">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq7">
                                <i class="fas fa-credit-card me-2 text-primary"></i>
                                What payment methods do you accept?
                            </button>
                        </h2>
                        <div id="faq7" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>We accept various secure payment methods:</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-credit-card me-2 text-primary"></i>Credit/Debit Cards</h6>
                                        <ul>
                                            <li>Visa</li>
                                            <li>Mastercard</li>
                                            <li>American Express</li>
                                            <li>Discover</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-mobile-alt me-2 text-primary"></i>Digital Wallets</h6>
                                        <ul>
                                            <li>PayPal</li>
                                            <li>Apple Pay</li>
                                            <li>Google Pay</li>
                                            <li>Samsung Pay</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-university me-2 text-primary"></i>Bank Transfer</h6>
                                        <ul>
                                            <li>ACH Transfer</li>
                                            <li>Wire Transfer</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-money-bill me-2 text-primary"></i>Other Options</h6>
                                        <ul>
                                            <li>Cash on Delivery (COD)</li>
                                            <li>Insurance Claims</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    All payments are processed securely using 256-bit SSL encryption.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Questions -->
                    <div class="accordion-item faq-item" data-category="Account">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq8">
                                <i class="fas fa-user me-2 text-primary"></i>
                                How do I reset my password?
                            </button>
                        </h2>
                        <div id="faq8" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>To reset your password:</p>
                                <ol>
                                    <li>Go to the login page and click "Forgot Password"</li>
                                    <li>Enter your registered email address</li>
                                    <li>Check your email for a password reset link</li>
                                    <li>Click the link and create a new password</li>
                                    <li>Log in with your new password</li>
                                </ol>
                                <p><strong>Password Requirements:</strong></p>
                                <ul>
                                    <li>At least 8 characters long</li>
                                    <li>Include uppercase and lowercase letters</li>
                                    <li>Include at least one number</li>
                                    <li>Include at least one special character</li>
                                </ul>
                                <p>If you don't receive the reset email, check your spam folder or contact our support team.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Questions -->
                    <div class="accordion-item faq-item" data-category="Technical">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq9">
                                <i class="fas fa-mobile-alt me-2 text-primary"></i>
                                Is MediEase mobile-friendly?
                            </button>
                        </h2>
                        <div id="faq9" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Yes! MediEase is fully responsive and works seamlessly on:</p>
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6><i class="fas fa-mobile-alt me-2 text-primary"></i>Mobile Devices</h6>
                                        <ul>
                                            <li>iOS (Safari)</li>
                                            <li>Android (Chrome)</li>
                                            <li>All screen sizes</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6><i class="fas fa-tablet-alt me-2 text-primary"></i>Tablets</h6>
                                        <ul>
                                            <li>iPad</li>
                                            <li>Android tablets</li>
                                            <li>Windows tablets</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6><i class="fas fa-desktop me-2 text-primary"></i>Desktop</h6>
                                        <ul>
                                            <li>Chrome</li>
                                            <li>Firefox</li>
                                            <li>Safari</li>
                                            <li>Edge</li>
                                        </ul>
                                    </div>
                                </div>
                                <p>We also offer accessibility features including:</p>
                                <ul>
                                    <li>Night mode for easier viewing</li>
                                    <li>Large font options</li>
                                    <li>Text-to-speech functionality</li>
                                    <li>Keyboard navigation support</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body text-center py-4">
                        <h4>Still have questions?</h4>
                        <p class="text-muted">Our customer support team is here to help you 24/7</p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="~/Contact.aspx" runat="server" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>Contact Us
                            </a>
                            <a href="tel:+1234567890" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>Call Support
                            </a>
                            <button type="button" class="btn btn-outline-success" onclick="openChatbot()">
                                <i class="fas fa-comments me-2"></i>Live Chat
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // FAQ Search functionality
        function searchFAQs(searchTerm) {
            const faqItems = document.querySelectorAll('.faq-item');
            const searchLower = searchTerm.toLowerCase();
            
            faqItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchLower) || searchTerm === '') {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Category filtering
        function filterByCategory(category) {
            const faqItems = document.querySelectorAll('.faq-item');
            const buttons = document.querySelectorAll('.btn-outline-primary');
            
            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Filter items
            faqItems.forEach(item => {
                const itemCategory = item.getAttribute('data-category');
                if (category === 'All' || itemCategory === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Open chatbot
        function openChatbot() {
            // This would integrate with your existing chatbot
            alert('Chatbot feature will open here');
        }
    </script>
</asp:Content>
