using System;
using System.Linq;
using System.Text;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase.Customer
{
    public partial class AIRecommendations : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null || currentUser.Role.ToLower() != "customer")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadUserHealthInfo(currentUser);
            }
        }

        private void LoadUserHealthInfo(UserInfo currentUser)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var user = db.Users.FirstOrDefault(u => u.UserId == currentUser.UserId);
                    if (user != null)
                    {
                        // Pre-populate known allergies and current medications from profile
                        if (!string.IsNullOrEmpty(user.Allergies))
                            txtAllergies.Text = user.Allergies;
                        
                        if (!string.IsNullOrEmpty(user.CurrentMedications))
                            txtCurrentMedications.Text = user.CurrentMedications;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading user health info");
            }
        }

        protected void btnGetRecommendations_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSymptoms.Text))
            {
                ShowErrorMessage("Please describe your symptoms to get recommendations.");
                return;
            }

            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                // Prepare AI prompt with user information
                var aiPrompt = BuildAIPrompt();
                
                // Get AI recommendations
                var aiResponse = GetAIRecommendations(aiPrompt);
                
                if (!string.IsNullOrEmpty(aiResponse))
                {
                    DisplayRecommendations(aiResponse);
                    LoadRecommendedMedicines();
                    
                    // Log the recommendation request
                    LogRecommendationRequest(currentUser.UserId, txtSymptoms.Text, aiResponse);
                }
                else
                {
                    ShowErrorMessage("Unable to get AI recommendations at this time. Please try again later.");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting AI recommendations");
                ShowErrorMessage("Error processing your request. Please try again.");
            }
        }

        private string BuildAIPrompt()
        {
            var prompt = new StringBuilder();
            
            prompt.AppendLine("You are a medical AI assistant providing medicine recommendations. Please analyze the following patient information and provide helpful recommendations:");
            prompt.AppendLine();
            prompt.AppendLine($"Symptoms: {txtSymptoms.Text}");
            prompt.AppendLine($"Severity: {ddlSeverity.SelectedValue}");
            prompt.AppendLine($"Duration: {ddlDuration.SelectedValue}");
            prompt.AppendLine($"Age Group: {ddlAgeGroup.SelectedValue}");
            
            if (!string.IsNullOrWhiteSpace(txtAllergies.Text))
                prompt.AppendLine($"Known Allergies: {txtAllergies.Text}");
            
            if (!string.IsNullOrWhiteSpace(txtCurrentMedications.Text))
                prompt.AppendLine($"Current Medications: {txtCurrentMedications.Text}");
            
            if (!string.IsNullOrWhiteSpace(txtMedicalConditions.Text))
                prompt.AppendLine($"Medical Conditions: {txtMedicalConditions.Text}");
            
            prompt.AppendLine();
            prompt.AppendLine("Please provide:");
            prompt.AppendLine("1. Possible causes of these symptoms");
            prompt.AppendLine("2. Recommended over-the-counter medications");
            prompt.AppendLine("3. Home remedies and lifestyle recommendations");
            prompt.AppendLine("4. When to seek professional medical attention");
            prompt.AppendLine("5. Important warnings and precautions");
            prompt.AppendLine();
            prompt.AppendLine("Format your response in HTML with proper headings and bullet points.");
            prompt.AppendLine("Always emphasize that this is not a substitute for professional medical advice.");
            
            return prompt.ToString();
        }

        private string GetAIRecommendations(string prompt)
        {
            try
            {
                var aiResponse = AIHelper.GetChatbotResponseAsync(prompt).Result;
                return aiResponse;
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error calling AI service for recommendations");
                return GetFallbackRecommendations();
            }
        }

        private string GetFallbackRecommendations()
        {
            return @"
                <div class='alert alert-warning'>
                    <h6>AI Service Temporarily Unavailable</h6>
                    <p>We're unable to provide AI recommendations at this time. Here are some general guidelines:</p>
                    <ul>
                        <li>For mild symptoms, consider rest and hydration</li>
                        <li>For pain or fever, over-the-counter pain relievers may help</li>
                        <li>If symptoms persist or worsen, consult a healthcare professional</li>
                        <li>For severe symptoms, seek immediate medical attention</li>
                    </ul>
                    <p><strong>Important:</strong> This is general information only. Always consult with a healthcare professional for proper diagnosis and treatment.</p>
                </div>";
        }

        private void DisplayRecommendations(string aiResponse)
        {
            // Clean and format the AI response
            var formattedResponse = FormatAIResponse(aiResponse);
            
            litRecommendations.Text = formattedResponse;
            pnlRecommendations.Visible = true;
        }

        private string FormatAIResponse(string response)
        {
            // Add disclaimer at the top
            var disclaimer = @"
                <div class='alert alert-warning mb-3'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    <strong>Medical Disclaimer:</strong> This AI-generated information is for educational purposes only and should not replace professional medical advice, diagnosis, or treatment.
                </div>";
            
            // Ensure the response is properly formatted
            if (!response.Contains("<"))
            {
                // Convert plain text to HTML
                response = response.Replace("\n", "<br>");
            }
            
            return disclaimer + response;
        }

        private void LoadRecommendedMedicines()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Get medicines that might be relevant to the symptoms
                    var keywords = ExtractKeywords(txtSymptoms.Text);
                    
                    var medicines = db.Medicines
                        .Where(m => m.IsActive && m.StockQuantity > 0)
                        .Where(m => keywords.Any(k => 
                            m.Name.Contains(k) || 
                            m.Description.Contains(k) || 
                            m.Category.Contains(k) ||
                            m.GenericName.Contains(k)))
                        .OrderBy(m => m.Name)
                        .Take(5)
                        .ToList();
                    
                    if (medicines.Any())
                    {
                        rptRecommendedMedicines.DataSource = medicines;
                        rptRecommendedMedicines.DataBind();
                        pnlAvailableMedicines.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading recommended medicines");
            }
        }

        private string[] ExtractKeywords(string symptoms)
        {
            // Simple keyword extraction based on common symptoms
            var commonMedicineKeywords = new[]
            {
                "pain", "fever", "headache", "cold", "cough", "flu", "allergy",
                "stomach", "nausea", "diarrhea", "constipation", "acid", "heartburn",
                "muscle", "joint", "inflammation", "infection", "antibiotic",
                "vitamin", "supplement", "throat", "sinus", "congestion"
            };
            
            var symptomsLower = symptoms.ToLower();
            return commonMedicineKeywords.Where(k => symptomsLower.Contains(k)).ToArray();
        }

        private void LogRecommendationRequest(int userId, string symptoms, string recommendations)
        {
            try
            {
                ErrorLogger.LogUserActivity($"AI Recommendation Request - Symptoms: {symptoms.Substring(0, Math.Min(100, symptoms.Length))}...", userId);
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error logging recommendation request");
            }
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }
    }
}
