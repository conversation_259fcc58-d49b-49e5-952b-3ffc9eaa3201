using System;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;
using Prescription = MediEase.Models.Prescription;

namespace MediEase.Customer
{
    public partial class Profile : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null || currentUser.Role.ToLower() != "customer")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadUserProfile(currentUser);
                LoadUserStats(currentUser);
            }
        }

        private void LoadUserProfile(UserInfo currentUser)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var user = db.Users.FirstOrDefault(u => u.UserId == currentUser.UserId);
                    if (user != null)
                    {
                        // Basic Information
                        lblUserName.Text = user.FirstName + " " + user.LastName;
                        lblUserEmail.Text = user.Email;
                        lblMemberSince.Text = user.CreatedDate.ToString("MMM yyyy");

                        txtFirstName.Text = user.FirstName;
                        txtLastName.Text = user.LastName;
                        txtEmail.Text = user.Email;
                        txtPhoneNumber.Text = user.PhoneNumber;
                        
                        if (user.DateOfBirth.HasValue)
                            txtDateOfBirth.Text = user.DateOfBirth.Value.ToString("yyyy-MM-dd");
                        
                        if (!string.IsNullOrEmpty(user.Gender))
                            ddlGender.SelectedValue = user.Gender;

                        // Address Information
                        txtAddress.Text = user.Address;
                        txtCity.Text = user.City;
                        txtPostalCode.Text = user.PostalCode;
                        txtCountry.Text = user.Country;

                        // Health Information
                        if (!string.IsNullOrEmpty(user.BloodType))
                            ddlBloodType.SelectedValue = user.BloodType;
                        
                        txtEmergencyContact.Text = user.EmergencyContact;
                        txtAllergies.Text = user.Allergies;
                        txtCurrentMedications.Text = user.CurrentMedications;

                        // Preferences
                        chkEmailNotifications.Checked = user.EmailNotifications;
                        chkSMSNotifications.Checked = user.SMSNotifications;
                        chkNewsletterSubscription.Checked = user.NewsletterSubscription;
                        chkAutoRefill.Checked = user.AutoRefillReminders;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading user profile");
                ShowErrorMessage("Error loading profile information.");
            }
        }

        private void LoadUserStats(UserInfo currentUser)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Total Orders
                    var totalOrders = db.Orders.Count(o => o.CustomerId == currentUser.UserId);
                    lblTotalOrders.Text = totalOrders.ToString();

                    // Loyalty Points
                    var user = db.Users.FirstOrDefault(u => u.UserId == currentUser.UserId);
                    lblLoyaltyPoints.Text = user?.LoyaltyPoints.ToString() ?? "0";

                    // Total Prescriptions
                    var totalPrescriptions = db.Prescriptions.Count(p => p.PatientId == currentUser.UserId);
                    lblTotalPrescriptions.Text = totalPrescriptions.ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading user stats");
                // Don't show error for stats, just log it
            }
        }

        protected void btnSaveProfile_Click(object sender, EventArgs e)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    var user = db.Users.FirstOrDefault(u => u.UserId == currentUser.UserId);
                    if (user != null)
                    {
                        // Update basic information
                        user.FirstName = txtFirstName.Text.Trim();
                        user.LastName = txtLastName.Text.Trim();
                        user.PhoneNumber = txtPhoneNumber.Text.Trim();
                        
                        if (DateTime.TryParse(txtDateOfBirth.Text, out DateTime dob))
                            user.DateOfBirth = dob;
                        
                        user.Gender = ddlGender.SelectedValue;

                        // Update address information
                        user.Address = txtAddress.Text.Trim();
                        user.City = txtCity.Text.Trim();
                        user.PostalCode = txtPostalCode.Text.Trim();
                        user.Country = txtCountry.Text.Trim();

                        // Update health information
                        user.BloodType = ddlBloodType.SelectedValue;
                        user.EmergencyContact = txtEmergencyContact.Text.Trim();
                        user.Allergies = txtAllergies.Text.Trim();
                        user.CurrentMedications = txtCurrentMedications.Text.Trim();

                        // Update preferences
                        user.EmailNotifications = chkEmailNotifications.Checked;
                        user.SMSNotifications = chkSMSNotifications.Checked;
                        user.NewsletterSubscription = chkNewsletterSubscription.Checked;
                        user.AutoRefillReminders = chkAutoRefill.Checked;

                        user.ModifiedDate = DateTime.Now;

                        db.SaveChanges();

                        // Handle profile image upload if provided
                        if (fuProfileImage.HasFile)
                        {
                            SaveProfileImage(user.UserId);
                        }

                        ShowSuccessMessage("Profile updated successfully!");
                        
                        // Update session with new name if changed
                        SecurityHelper.UpdateUserSession(user);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error saving user profile");
                ShowErrorMessage("Error saving profile. Please try again.");
            }
        }

        private void SaveProfileImage(int userId)
        {
            try
            {
                if (fuProfileImage.HasFile)
                {
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                    var fileExtension = System.IO.Path.GetExtension(fuProfileImage.FileName).ToLower();
                    
                    if (allowedExtensions.Contains(fileExtension))
                    {
                        var fileName = $"profile_{userId}_{DateTime.Now.Ticks}{fileExtension}";
                        var uploadPath = Server.MapPath("~/Images/Profiles/");
                        
                        // Create directory if it doesn't exist
                        if (!System.IO.Directory.Exists(uploadPath))
                            System.IO.Directory.CreateDirectory(uploadPath);
                        
                        var filePath = System.IO.Path.Combine(uploadPath, fileName);
                        fuProfileImage.SaveAs(filePath);
                        
                        // Update user's profile image path in database
                        using (var db = new MediEaseContext())
                        {
                            var user = db.Users.FirstOrDefault(u => u.UserId == userId);
                            if (user != null)
                            {
                                user.ProfileImagePath = fileName;
                                db.SaveChanges();
                            }
                        }
                    }
                    else
                    {
                        ShowErrorMessage("Please upload a valid image file (JPG, PNG, GIF).");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error saving profile image");
                ShowErrorMessage("Error uploading profile image.");
            }
        }

        protected void btnChangePassword_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Customer/ChangePassword.aspx");
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }
    }
}
