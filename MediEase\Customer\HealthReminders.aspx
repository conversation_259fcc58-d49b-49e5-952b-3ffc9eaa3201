<%@ Page Title="Health Reminders" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="HealthReminders.aspx.cs" Inherits="MediEase.Customer.HealthReminders" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-bell me-2 text-info"></i>Health Reminders</h2>
                        <p class="text-muted">Manage your medication schedules and health appointments</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addReminderModal">
                            <i class="fas fa-plus me-2"></i>Add Reminder
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's Reminders -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Today's Reminders</h5>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlTodayReminders" runat="server">
                            <asp:Repeater ID="rptTodayReminders" runat="server" OnItemCommand="rptTodayReminders_ItemCommand">
                                <ItemTemplate>
                                    <div class="reminder-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded">
                                        <div class="d-flex align-items-center">
                                            <div class="reminder-icon me-3">
                                                <i class="fas fa-<%# GetReminderIcon(Eval("Type").ToString()) %> fa-2x text-<%# GetReminderColor(Eval("Type").ToString()) %>"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1"><%# Eval("Title") %></h6>
                                                <p class="mb-1 text-muted"><%# Eval("Description") %></p>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i><%# Convert.ToDateTime(Eval("ReminderTime")).ToString("hh:mm tt") %>
                                                </small>
                                            </div>
                                        </div>
                                        <div class="reminder-actions">
                                            <asp:LinkButton runat="server" CssClass="btn btn-success btn-sm me-2"
                                                CommandName="MarkTaken" CommandArgument='<%# Eval("ReminderId") %>'>
                                                <i class="fas fa-check me-1"></i>Taken
                                            </asp:LinkButton>
                                            <asp:LinkButton runat="server" CssClass="btn btn-warning btn-sm me-2"
                                                CommandName="Snooze" CommandArgument='<%# Eval("ReminderId") %>'>
                                                <i class="fas fa-clock me-1"></i>Snooze
                                            </asp:LinkButton>
                                            <asp:LinkButton runat="server" CssClass="btn btn-outline-danger btn-sm"
                                                CommandName="Skip" CommandArgument='<%# Eval("ReminderId") %>'>
                                                <i class="fas fa-times me-1"></i>Skip
                                            </asp:LinkButton>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </asp:Panel>

                        <asp:Panel ID="pnlNoTodayReminders" runat="server" Visible="false">
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h5>All caught up!</h5>
                                <p class="text-muted">No reminders for today. Great job staying on track!</p>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reminder Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-pills fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActiveReminders" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Reminders</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblComplianceRate" runat="server" Text="0"></asp:Label>%</h4>
                        <p class="mb-0">Compliance Rate</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-week fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblWeeklyDoses" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">This Week's Doses</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-trophy fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblStreak" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Day Streak</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Reminders -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>All Reminders</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <asp:Button ID="btnFilterMedication" runat="server" CssClass="btn btn-outline-primary active" Text="Medication" OnClick="btnFilterMedication_Click" />
                            <asp:Button ID="btnFilterAppointment" runat="server" CssClass="btn btn-outline-info" Text="Appointments" OnClick="btnFilterAppointment_Click" />
                            <asp:Button ID="btnFilterHealth" runat="server" CssClass="btn btn-outline-success" Text="Health Tasks" OnClick="btnFilterHealth_Click" />
                            <asp:Button ID="btnFilterAll" runat="server" CssClass="btn btn-outline-secondary" Text="All" OnClick="btnFilterAll_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <asp:Repeater ID="rptAllReminders" runat="server" OnItemCommand="rptAllReminders_ItemCommand">
                                <ItemTemplate>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="card reminder-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span class="badge bg-<%# GetReminderColor(Eval("Type").ToString()) %> fs-6">
                                                    <%# Eval("Type") %>
                                                </span>
                                                <div class="form-check form-switch">
                                                    <asp:CheckBox ID="chkActive" runat="server" CssClass="form-check-input"
                                                        Checked='<%# Convert.ToBoolean(Eval("IsActive")) %>'
                                                        AutoPostBack="true" OnCheckedChanged="chkActive_CheckedChanged" />
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex align-items-start">
                                                    <div class="reminder-icon me-3">
                                                        <i class="fas fa-<%# GetReminderIcon(Eval("Type").ToString()) %> fa-2x text-<%# GetReminderColor(Eval("Type").ToString()) %>"></i>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="text-primary"><%# Eval("Title") %></h6>
                                                        <p class="text-muted mb-2"><%# Eval("Description") %></p>

                                                        <div class="reminder-schedule mb-3">
                                                            <div class="row text-center">
                                                                <div class="col-6">
                                                                    <small class="text-muted">Time</small>
                                                                    <div class="fw-bold"><%# Convert.ToDateTime(Eval("ReminderTime")).ToString("hh:mm tt") %></div>
                                                                </div>
                                                                <div class="col-6">
                                                                    <small class="text-muted">Frequency</small>
                                                                    <div class="fw-bold"><%# GetFrequencyText(Eval("Frequency").ToString()) %></div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <%# Eval("Type").ToString() == "Medication" ?
                                                            "<div class='medication-info p-2 bg-light rounded'>" +
                                                            "<small class='text-muted'><strong>Medicine:</strong> " + Eval("MedicineName") + "</small><br>" +
                                                            "<small class='text-muted'><strong>Dosage:</strong> " + Eval("Dosage") + "</small>" +
                                                            "</div>" : "" %>

                                                        <%# !string.IsNullOrEmpty(Eval("Notes")?.ToString()) ?
                                                            "<div class='mt-2'><small class='text-muted'><strong>Notes:</strong> " + Eval("Notes") + "</small></div>" : "" %>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-flex justify-content-between">
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary edit-reminder" data-reminder-id='<%# Eval("ReminderId") %>'>
                                                            <i class="fas fa-edit me-1"></i>Edit
                                                        </button>
                                                        <button class="btn btn-outline-info view-history" data-reminder-id='<%# Eval("ReminderId") %>'>
                                                            <i class="fas fa-history me-1"></i>History
                                                        </button>
                                                    </div>
                                                    <asp:LinkButton runat="server" CssClass="btn btn-outline-danger btn-sm"
                                                        CommandName="Delete" CommandArgument='<%# Eval("ReminderId") %>'
                                                        OnClientClick="return confirm('Are you sure you want to delete this reminder?');">
                                                        <i class="fas fa-trash me-1"></i>Delete
                                                    </asp:LinkButton>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>

                        <!-- No Reminders Message -->
                        <asp:Panel ID="pnlNoReminders" runat="server" Visible="false">
                            <div class="text-center py-5">
                                <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                                <h4>No reminders set up yet</h4>
                                <p class="text-muted">Create your first health reminder to stay on track with your medications and appointments.</p>
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addReminderModal">
                                    <i class="fas fa-plus me-2"></i>Create Your First Reminder
                                </button>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Reminder Modal -->
    <div class="modal fade" id="addReminderModal" tabindex="-1" aria-labelledby="addReminderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addReminderModalLabel">Add Health Reminder</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="reminderForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Reminder Type *</label>
                                    <asp:DropDownList ID="ddlReminderType" runat="server" CssClass="form-select" required AutoPostBack="true" OnSelectedIndexChanged="ddlReminderType_SelectedIndexChanged">
                                        <asp:ListItem Value="">Select type...</asp:ListItem>
                                        <asp:ListItem Value="Medication">Medication</asp:ListItem>
                                        <asp:ListItem Value="Appointment">Doctor Appointment</asp:ListItem>
                                        <asp:ListItem Value="Health">Health Task</asp:ListItem>
                                        <asp:ListItem Value="Refill">Prescription Refill</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Title *</label>
                                    <asp:TextBox ID="txtTitle" runat="server" CssClass="form-control" placeholder="e.g., Take Blood Pressure Medication" required></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <!-- Medication-specific fields -->
                        <asp:Panel ID="pnlMedicationFields" runat="server" Visible="false">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Select Medicine</label>
                                        <asp:DropDownList ID="ddlMedicine" runat="server" CssClass="form-select">
                                            <asp:ListItem Value="">Choose from your medicines...</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Dosage</label>
                                        <asp:TextBox ID="txtDosage" runat="server" CssClass="form-control" placeholder="e.g., 1 tablet, 5ml"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2"
                                placeholder="Additional details about this reminder..."></asp:TextBox>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Reminder Time *</label>
                                    <asp:TextBox ID="txtReminderTime" runat="server" CssClass="form-control" TextMode="Time" required></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Frequency *</label>
                                    <asp:DropDownList ID="ddlFrequency" runat="server" CssClass="form-select" required>
                                        <asp:ListItem Value="">Select frequency...</asp:ListItem>
                                        <asp:ListItem Value="Daily">Daily</asp:ListItem>
                                        <asp:ListItem Value="Weekly">Weekly</asp:ListItem>
                                        <asp:ListItem Value="Monthly">Monthly</asp:ListItem>
                                        <asp:ListItem Value="AsNeeded">As Needed</asp:ListItem>
                                        <asp:ListItem Value="Custom">Custom Schedule</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Start Date *</label>
                                    <asp:TextBox ID="txtStartDate" runat="server" CssClass="form-control" TextMode="Date" required></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">End Date (Optional)</label>
                                    <asp:TextBox ID="txtEndDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Additional Notes</label>
                            <asp:TextBox ID="txtNotes" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2"
                                placeholder="Any special instructions or notes..."></asp:TextBox>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkEmailNotification" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkEmailNotification.ClientID %>">
                                    Send email notifications
                                </label>
                            </div>
                            <div class="form-check">
                                <asp:CheckBox ID="chkSMSNotification" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkSMSNotification.ClientID %>">
                                    Send SMS notifications (charges may apply)
                                </label>
                            </div>
                            <div class="form-check">
                                <asp:CheckBox ID="chkPushNotification" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkPushNotification.ClientID %>">
                                    Send push notifications
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSaveReminder" runat="server" CssClass="btn btn-info" Text="Save Reminder" OnClick="btnSaveReminder_Click" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .reminder-card:hover {
            transform: translateY(-3px);
            transition: all 0.3s ease;
        }

        .reminder-item {
            transition: all 0.3s ease;
        }

        .reminder-item:hover {
            background-color: #f8f9fa;
        }

        .reminder-icon {
            min-width: 60px;
            text-align: center;
        }

        .medication-info {
            border-left: 4px solid #17a2b8;
        }
    </style>

    <script>
        // Edit reminder
        $(document).on('click', '.edit-reminder', function(e) {
            e.preventDefault();
            const reminderId = $(this).data('reminder-id');
            loadEditReminderForm(reminderId);
        });

        // View reminder history
        $(document).on('click', '.view-history', function(e) {
            e.preventDefault();
            const reminderId = $(this).data('reminder-id');
            loadReminderHistory(reminderId);
        });

        function loadEditReminderForm(reminderId) {
            // Load reminder data and populate form
            $.ajax({
                type: 'POST',
                url: 'HealthReminders.aspx/GetReminderForEdit',
                data: JSON.stringify({ reminderId: reminderId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        // Populate form with reminder data
                        populateReminderForm(response.d.reminder);
                        $('#addReminderModalLabel').text('Edit Reminder');
                        $('#addReminderModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading reminder data.');
                }
            });
        }

        function loadReminderHistory(reminderId) {
            // Show reminder compliance history
            $.ajax({
                type: 'POST',
                url: 'HealthReminders.aspx/GetReminderHistory',
                data: JSON.stringify({ reminderId: reminderId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        // Show history in a modal or new page
                        showReminderHistoryModal(response.d.history);
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading reminder history.');
                }
            });
        }

        function populateReminderForm(reminder) {
            // Populate form fields with reminder data
            $('#<%= ddlReminderType.ClientID %>').val(reminder.Type);
            $('#<%= txtTitle.ClientID %>').val(reminder.Title);
            $('#<%= txtDescription.ClientID %>').val(reminder.Description);
            $('#<%= txtReminderTime.ClientID %>').val(reminder.ReminderTime);
            $('#<%= ddlFrequency.ClientID %>').val(reminder.Frequency);
            $('#<%= txtStartDate.ClientID %>').val(reminder.StartDate);
            $('#<%= txtEndDate.ClientID %>').val(reminder.EndDate);
            $('#<%= txtNotes.ClientID %>').val(reminder.Notes);
        }

        // Set minimum date for start date
        $(document).ready(function() {
            const today = new Date().toISOString().split('T')[0];
            $('#<%= txtStartDate.ClientID %>').attr('min', today);
        });
    </script>
</asp:Content>