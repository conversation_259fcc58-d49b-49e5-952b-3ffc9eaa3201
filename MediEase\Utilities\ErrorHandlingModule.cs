using System;
using System.Web;
using System.Web.Security;
using MediEase.DAL;
using MediEase.Models;

namespace MediEase.Utilities
{
    public class ErrorHandlingModule : IHttpModule
    {
        public void Init(HttpApplication context)
        {
            context.Error += OnError;
        }

        private void OnError(object sender, EventArgs e)
        {
            var context = HttpContext.Current;
            var exception = context.Server.GetLastError();

            if (exception != null)
            {
                LogError(exception, context);
                
                // Clear the error to prevent default error page
                context.Server.ClearError();
                
                // Redirect to appropriate error page
                RedirectToErrorPage(exception, context);
            }
        }

        private void LogError(Exception exception, HttpContext context)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var errorLog = new ErrorLog
                    {
                        ErrorMessage = exception.Message,
                        StackTrace = exception.StackTrace,
                        Source = exception.Source,
                        TargetSite = exception.TargetSite?.ToString(),
                        InnerException = exception.InnerException?.Message,
                        RequestUrl = context.Request.Url?.ToString(),
                        RequestMethod = context.Request.HttpMethod,
                        UserAgent = context.Request.UserAgent,
                        IPAddress = SecurityHelper.GetClientIPAddress(),
                        UserId = SecurityHelper.GetCurrentUser()?.UserId,
                        CreatedDate = DateTime.Now
                    };

                    db.ErrorLogs.Add(errorLog);
                    db.SaveChanges();
                }
            }
            catch
            {
                // If database logging fails, log to event log or file
                System.Diagnostics.EventLog.WriteEntry("MediEase", 
                    $"Error: {exception.Message}\nStack Trace: {exception.StackTrace}", 
                    System.Diagnostics.EventLogEntryType.Error);
            }
        }

        private void RedirectToErrorPage(Exception exception, HttpContext context)
        {
            var errorType = GetErrorType(exception);
            var errorPage = GetErrorPageUrl(errorType);
            
            // Store error details in session for error page to display
            context.Session["LastError"] = new ErrorDetails
            {
                Message = exception.Message,
                Type = errorType,
                Timestamp = DateTime.Now,
                RequestUrl = context.Request.Url?.ToString()
            };

            context.Response.Redirect(errorPage);
        }

        private ErrorType GetErrorType(Exception exception)
        {
            switch (exception)
            {
                case HttpException httpEx when httpEx.GetHttpCode() == 404:
                    return ErrorType.NotFound;
                case UnauthorizedAccessException _:
                    return ErrorType.Unauthorized;
                case ArgumentNullException _:
                    return ErrorType.BadRequest;
                case ArgumentException _:
                    return ErrorType.BadRequest;
                case TimeoutException _:
                    return ErrorType.Timeout;
                case System.Data.SqlClient.SqlException _:
                    return ErrorType.Database;
                case HttpRequestValidationException _:
                    return ErrorType.Validation;
                default:
                    return ErrorType.General;
            }
        }

        private string GetErrorPageUrl(ErrorType errorType)
        {
            switch (errorType)
            {
                case ErrorType.NotFound:
                    return "~/Error404.aspx";
                case ErrorType.Unauthorized:
                    return "~/Error401.aspx";
                case ErrorType.Database:
                    return "~/ErrorDatabase.aspx";
                default:
                    return "~/Error.aspx";
            }
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }

    public class ErrorDetails
    {
        public string Message { get; set; }
        public ErrorType Type { get; set; }
        public DateTime Timestamp { get; set; }
        public string RequestUrl { get; set; }
    }

    public enum ErrorType
    {
        General,
        NotFound,
        Unauthorized,
        BadRequest,
        Timeout,
        Database,
        Validation
    }



    public static class ErrorLogger
    {
        public static void LogError(Exception exception, string additionalInfo = null)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var context = HttpContext.Current;
                    var errorLog = new ErrorLog
                    {
                        ErrorMessage = exception.Message,
                        StackTrace = exception.StackTrace,
                        Source = exception.Source,
                        TargetSite = exception.TargetSite?.ToString(),
                        InnerException = exception.InnerException?.Message,
                        RequestUrl = context?.Request.Url?.ToString(),
                        RequestMethod = context?.Request.HttpMethod,
                        UserAgent = context?.Request.UserAgent,
                        IPAddress = SecurityHelper.GetClientIPAddress(),
                        UserId = SecurityHelper.GetCurrentUser()?.UserId,
                        CreatedDate = DateTime.Now
                    };

                    if (!string.IsNullOrEmpty(additionalInfo))
                    {
                        errorLog.ErrorMessage += $" | Additional Info: {additionalInfo}";
                    }

                    db.ErrorLogs.Add(errorLog);
                    db.SaveChanges();
                }
            }
            catch
            {
                // Fallback logging
                System.Diagnostics.Debug.WriteLine($"Error logging failed: {exception.Message}");
            }
        }

        public static void LogInfo(string message, string category = "Info")
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var context = HttpContext.Current;
                    var errorLog = new ErrorLog
                    {
                        ErrorMessage = $"[{category}] {message}",
                        Source = "MediEase Application",
                        RequestUrl = context?.Request.Url?.ToString(),
                        RequestMethod = context?.Request.HttpMethod,
                        UserAgent = context?.Request.UserAgent,
                        IPAddress = SecurityHelper.GetClientIPAddress(),
                        UserId = SecurityHelper.GetCurrentUser()?.UserId,
                        CreatedDate = DateTime.Now
                    };

                    db.ErrorLogs.Add(errorLog);
                    db.SaveChanges();
                }
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"Info logging failed: {message}");
            }
        }

        public static void LogUserActivity(string activity, int? userId = null)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                var userIdToLog = userId ?? currentUser?.UserId;

                if (userIdToLog.HasValue)
                {
                    LogInfo($"User Activity: {activity} (User ID: {userIdToLog})", "UserActivity");
                }
            }
            catch
            {
                // Silent fail for activity logging
            }
        }
    }
}
