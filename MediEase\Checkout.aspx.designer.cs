//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase
{
    public partial class Checkout
    {
        /// <summary>
        /// txtFirstName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtFirstName;

        /// <summary>
        /// txtLastName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtLastName;

        /// <summary>
        /// txtEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtEmail;

        /// <summary>
        /// txtPhoneNumber control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtPhoneNumber;

        /// <summary>
        /// txtAddress control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtAddress;

        /// <summary>
        /// txtCity control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtCity;

        /// <summary>
        /// ddlState control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlState;

        /// <summary>
        /// txtZipCode control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtZipCode;

        /// <summary>
        /// rblPaymentMethod control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RadioButtonList rblPaymentMethod;

        /// <summary>
        /// pnlCreditCard control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlCreditCard;

        /// <summary>
        /// txtCardNumber control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtCardNumber;

        /// <summary>
        /// txtExpiryDate control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtExpiryDate;

        /// <summary>
        /// txtCVV control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtCVV;

        /// <summary>
        /// txtCardholderName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtCardholderName;

        /// <summary>
        /// pnlPrescriptionUpload control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlPrescriptionUpload;

        /// <summary>
        /// fuPrescription control.
        /// </summary>
        protected global::System.Web.UI.WebControls.FileUpload fuPrescription;

        /// <summary>
        /// txtOrderNotes control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtOrderNotes;

        /// <summary>
        /// rptOrderItems control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptOrderItems;

        /// <summary>
        /// lblSubtotal control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblSubtotal;

        /// <summary>
        /// lblDiscount control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblDiscount;

        /// <summary>
        /// lblTax control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTax;

        /// <summary>
        /// lblShipping control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblShipping;

        /// <summary>
        /// lblTotal control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotal;

        /// <summary>
        /// btnPlaceOrder control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnPlaceOrder;
    }
}
