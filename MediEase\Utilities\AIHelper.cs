using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace MediEase.Utilities
{
    public class AIHelper
    {
        private static readonly string apiKey = ConfigurationManager.AppSettings["OpenRouterApiKey"] ?? "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";
        private static readonly string apiUrl = ConfigurationManager.AppSettings["OpenRouterApiUrl"] ?? "https://openrouter.ai/api/v1/chat/completions";
        private static readonly string model = ConfigurationManager.AppSettings["OpenRouterModel"] ?? "deepseek/deepseek-r1-0528-qwen3-8b:free";

        public static async Task<string> GetMedicineRecommendationsAsync(string symptoms, string patientAge = null, string patientGender = null, string allergies = null)
        {
            try
            {
                var prompt = BuildMedicineRecommendationPrompt(symptoms, patientAge, patientGender, allergies);
                return await CallOpenRouterAPIAsync(prompt);
            }
            catch (Exception ex)
            {
                LogError("GetMedicineRecommendationsAsync", ex);
                return "I apologize, but I'm unable to provide medicine recommendations at the moment. Please consult with our pharmacist or your healthcare provider.";
            }
        }

        public static async Task<string> GetChatbotResponseAsync(string userMessage, string conversationContext = null)
        {
            try
            {
                var prompt = BuildChatbotPrompt(userMessage, conversationContext);
                return await CallOpenRouterAPIAsync(prompt);
            }
            catch (Exception ex)
            {
                LogError("GetChatbotResponseAsync", ex);
                return "I apologize, but I'm experiencing technical difficulties. Please try again later or contact our support team.";
            }
        }

        public static async Task<string> AnalyzePrescriptionAsync(string prescriptionText)
        {
            try
            {
                var prompt = BuildPrescriptionAnalysisPrompt(prescriptionText);
                return await CallOpenRouterAPIAsync(prompt);
            }
            catch (Exception ex)
            {
                LogError("AnalyzePrescriptionAsync", ex);
                return "Unable to analyze prescription at the moment. Please have a pharmacist review it manually.";
            }
        }

        public static async Task<string> GetDrugInteractionWarningsAsync(List<string> medications)
        {
            try
            {
                var prompt = BuildDrugInteractionPrompt(medications);
                return await CallOpenRouterAPIAsync(prompt);
            }
            catch (Exception ex)
            {
                LogError("GetDrugInteractionWarningsAsync", ex);
                return "Unable to check drug interactions at the moment. Please consult with a pharmacist.";
            }
        }

        private static async Task<string> CallOpenRouterAPIAsync(string prompt)
        {
            try
            {
                var requestBody = new
                {
                    model = model,
                    messages = new[]
                    {
                        new { role = "system", content = GetSystemPrompt() },
                        new { role = "user", content = prompt }
                    },
                    max_tokens = 1000,
                    temperature = 0.7,
                    top_p = 0.9,
                    frequency_penalty = 0.1,
                    presence_penalty = 0.1
                };

                var json = JsonConvert.SerializeObject(requestBody);

                using (var webClient = new WebClient())
                {
                    webClient.Headers[HttpRequestHeader.Authorization] = $"Bearer {apiKey}";
                    webClient.Headers[HttpRequestHeader.ContentType] = "application/json";
                    webClient.Headers["HTTP-Referer"] = "https://mediease.com";
                    webClient.Headers["X-Title"] = "MediEase Pharmacy System";

                    try
                    {
                        var responseContent = await webClient.UploadStringTaskAsync(apiUrl, json);
                        var apiResponse = JsonConvert.DeserializeObject<OpenRouterResponse>(responseContent);
                        return apiResponse?.Choices?[0]?.Message?.Content ?? "No response received from AI service.";
                    }
                    catch (WebException webEx)
                    {
                        string errorResponse = "";
                        if (webEx.Response != null)
                        {
                            using (var reader = new System.IO.StreamReader(webEx.Response.GetResponseStream()))
                            {
                                errorResponse = reader.ReadToEnd();
                            }
                        }
                        LogError("API WebException", new Exception($"WebException: {webEx.Message}, Response: {errorResponse}"));
                        return "I apologize, but I'm experiencing technical difficulties. Please try again later.";
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("CallOpenRouterAPIAsync", ex);
                return "I apologize, but I'm experiencing technical difficulties. Please try again later.";
            }
        }

        private static string GetSystemPrompt()
        {
            return @"You are MediBot, an AI assistant for MediEase Pharmacy Management System. You are knowledgeable about medications, health conditions, and pharmacy operations. 

IMPORTANT GUIDELINES:
1. Always emphasize that your advice is for informational purposes only
2. Recommend consulting healthcare professionals for medical decisions
3. Never diagnose medical conditions
4. Be cautious about drug recommendations - suggest consulting a pharmacist
5. Provide helpful, accurate information about medications and health
6. Be empathetic and professional
7. If unsure about medical information, recommend professional consultation
8. Focus on general health education and medication information
9. Respect patient privacy and confidentiality
10. Encourage proper medication adherence and safety

You can help with:
- General medication information
- Common side effects and precautions
- Medication storage and handling
- General health education
- Pharmacy services information
- Navigation assistance for the MediEase system

Always maintain a helpful, professional, and caring tone.";
        }

        private static string BuildMedicineRecommendationPrompt(string symptoms, string patientAge, string patientGender, string allergies)
        {
            var prompt = new StringBuilder();
            prompt.AppendLine("Please provide general information about over-the-counter medications that might help with the following symptoms:");
            prompt.AppendLine($"Symptoms: {symptoms}");
            
            if (!string.IsNullOrEmpty(patientAge))
                prompt.AppendLine($"Patient Age: {patientAge}");
            
            if (!string.IsNullOrEmpty(patientGender))
                prompt.AppendLine($"Patient Gender: {patientGender}");
            
            if (!string.IsNullOrEmpty(allergies))
                prompt.AppendLine($"Known Allergies: {allergies}");

            prompt.AppendLine("\nPlease provide:");
            prompt.AppendLine("1. General medication categories that might help");
            prompt.AppendLine("2. Important precautions and warnings");
            prompt.AppendLine("3. When to seek professional medical advice");
            prompt.AppendLine("4. Remind that this is general information only");

            return prompt.ToString();
        }

        private static string BuildChatbotPrompt(string userMessage, string conversationContext)
        {
            var prompt = new StringBuilder();
            
            if (!string.IsNullOrEmpty(conversationContext))
            {
                prompt.AppendLine("Previous conversation context:");
                prompt.AppendLine(conversationContext);
                prompt.AppendLine();
            }

            prompt.AppendLine("User message:");
            prompt.AppendLine(userMessage);
            prompt.AppendLine();
            prompt.AppendLine("Please provide a helpful, informative response as MediBot, the MediEase pharmacy assistant.");

            return prompt.ToString();
        }

        private static string BuildPrescriptionAnalysisPrompt(string prescriptionText)
        {
            var prompt = new StringBuilder();
            prompt.AppendLine("Please analyze the following prescription text and provide general information:");
            prompt.AppendLine(prescriptionText);
            prompt.AppendLine();
            prompt.AppendLine("Please provide:");
            prompt.AppendLine("1. Identified medications (if clearly readable)");
            prompt.AppendLine("2. General information about these medications");
            prompt.AppendLine("3. Important safety considerations");
            prompt.AppendLine("4. Recommend pharmacist verification");
            prompt.AppendLine("5. Note that this is preliminary analysis only");

            return prompt.ToString();
        }

        private static string BuildDrugInteractionPrompt(List<string> medications)
        {
            var prompt = new StringBuilder();
            prompt.AppendLine("Please provide general information about potential interactions between these medications:");
            
            foreach (var medication in medications)
            {
                prompt.AppendLine($"- {medication}");
            }

            prompt.AppendLine();
            prompt.AppendLine("Please provide:");
            prompt.AppendLine("1. General interaction warnings (if any)");
            prompt.AppendLine("2. Important precautions");
            prompt.AppendLine("3. Recommend professional consultation");
            prompt.AppendLine("4. Note that this is general information only");

            return prompt.ToString();
        }

        private static void LogError(string method, Exception ex)
        {
            try
            {
                // Log error to system
                System.Diagnostics.Debug.WriteLine($"AIHelper.{method} Error: {ex.Message}");

                // Use ErrorLogger if available
                ErrorLogger.LogError(ex, $"AIHelper.{method}");
            }
            catch
            {
                // Fail silently if logging fails
            }
        }



        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                var testResponse = await CallOpenRouterAPIAsync("Hello, please respond with 'Connection successful'");
                return !string.IsNullOrEmpty(testResponse);
            }
            catch
            {
                return false;
            }
        }
    }

    public class OpenRouterResponse
    {
        [JsonProperty("choices")]
        public Choice[] Choices { get; set; }

        [JsonProperty("usage")]
        public Usage Usage { get; set; }
    }

    public class Choice
    {
        [JsonProperty("message")]
        public Message Message { get; set; }

        [JsonProperty("finish_reason")]
        public string FinishReason { get; set; }
    }

    public class Message
    {
        [JsonProperty("role")]
        public string Role { get; set; }

        [JsonProperty("content")]
        public string Content { get; set; }
    }

    public class Usage
    {
        [JsonProperty("prompt_tokens")]
        public int PromptTokens { get; set; }

        [JsonProperty("completion_tokens")]
        public int CompletionTokens { get; set; }

        [JsonProperty("total_tokens")]
        public int TotalTokens { get; set; }
    }
}
