//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase
{
    public partial class Contact
    {
        /// <summary>
        /// pnlSuccess control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlSuccess;

        /// <summary>
        /// pnlError control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlError;

        /// <summary>
        /// lblError control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblError;

        /// <summary>
        /// txtName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtName;

        /// <summary>
        /// rfvName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvName;

        /// <summary>
        /// txtEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtEmail;

        /// <summary>
        /// rfvEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvEmail;

        /// <summary>
        /// revEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RegularExpressionValidator revEmail;

        /// <summary>
        /// txtPhone control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtPhone;

        /// <summary>
        /// ddlSubject control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlSubject;

        /// <summary>
        /// rfvSubject control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvSubject;

        /// <summary>
        /// txtMessage control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtMessage;

        /// <summary>
        /// rfvMessage control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvMessage;

        /// <summary>
        /// chkNewsletter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.CheckBox chkNewsletter;

        /// <summary>
        /// btnSend control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnSend;
    }
}
