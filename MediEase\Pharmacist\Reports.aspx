<%@ Page Title="Reports & Analytics" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Reports.aspx.cs" Inherits="MediEase.Pharmacist.Reports" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-chart-line me-2 text-primary"></i>Reports & Analytics</h2>
                        <p class="text-muted">Comprehensive business intelligence and reporting</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" onclick="exportAllReports()">
                            <i class="fas fa-download me-2"></i>Export All
                        </button>
                        <button type="button" class="btn btn-info" onclick="scheduleReport()">
                            <i class="fas fa-clock me-2"></i>Schedule Report
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">Date Range</label>
                                <asp:DropDownList ID="ddlDateRange" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlDateRange_SelectedIndexChanged">
                                    <asp:ListItem Value="today">Today</asp:ListItem>
                                    <asp:ListItem Value="yesterday">Yesterday</asp:ListItem>
                                    <asp:ListItem Value="week">This Week</asp:ListItem>
                                    <asp:ListItem Value="month" Selected="true">This Month</asp:ListItem>
                                    <asp:ListItem Value="quarter">This Quarter</asp:ListItem>
                                    <asp:ListItem Value="year">This Year</asp:ListItem>
                                    <asp:ListItem Value="custom">Custom Range</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">From Date</label>
                                <asp:TextBox ID="txtFromDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">To Date</label>
                                <asp:TextBox ID="txtToDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                            </div>
                            <div class="col-md-3">
                                <asp:Button ID="btnApplyFilter" runat="server" CssClass="btn btn-primary w-100" 
                                    Text="Apply Filter" OnClick="btnApplyFilter_Click" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>$<asp:Label ID="lblTotalRevenue" runat="server" Text="0.00"></asp:Label></h4>
                        <p class="mb-0">Total Revenue</p>
                        <small class="opacity-75">
                            <asp:Label ID="lblRevenueChange" runat="server" Text=""></asp:Label>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalOrders" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Orders</p>
                        <small class="opacity-75">
                            <asp:Label ID="lblOrdersChange" runat="server" Text=""></asp:Label>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-pills fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblMedicinesSold" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Medicines Sold</p>
                        <small class="opacity-75">
                            <asp:Label ID="lblMedicinesChange" runat="server" Text=""></asp:Label>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h4>$<asp:Label ID="lblAvgOrderValue" runat="server" Text="0.00"></asp:Label></h4>
                        <p class="mb-0">Avg Order Value</p>
                        <small class="opacity-75">
                            <asp:Label ID="lblAOVChange" runat="server" Text=""></asp:Label>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow-sm h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Revenue Trend</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="changeChart('revenue', 'daily')">Daily</button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeChart('revenue', 'weekly')">Weekly</button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeChart('revenue', 'monthly')">Monthly</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Top Categories</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="categoriesChart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="reportsTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab">
                                    <i class="fas fa-chart-bar me-2"></i>Sales Report
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab">
                                    <i class="fas fa-boxes me-2"></i>Inventory Report
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                                    <i class="fas fa-shopping-cart me-2"></i>Orders Report
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="customers-tab" data-bs-toggle="tab" data-bs-target="#customers" type="button" role="tab">
                                    <i class="fas fa-users me-2"></i>Customers Report
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="reportsTabContent">
                            <!-- Sales Report -->
                            <div class="tab-pane fade show active" id="sales" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>Sales Performance</h6>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportReport('sales')">
                                        <i class="fas fa-download me-1"></i>Export
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <asp:GridView ID="gvSalesReport" runat="server" CssClass="table table-hover" 
                                        AutoGenerateColumns="false" EmptyDataText="No sales data found.">
                                        <Columns>
                                            <asp:BoundField DataField="Date" HeaderText="Date" DataFormatString="{0:MMM dd, yyyy}" />
                                            <asp:BoundField DataField="OrderCount" HeaderText="Orders" />
                                            <asp:BoundField DataField="ItemsSold" HeaderText="Items Sold" />
                                            <asp:BoundField DataField="Revenue" HeaderText="Revenue" DataFormatString="${0:F2}" />
                                            <asp:BoundField DataField="Profit" HeaderText="Profit" DataFormatString="${0:F2}" />
                                            <asp:BoundField DataField="ProfitMargin" HeaderText="Margin %" DataFormatString="{0:F1}%" />
                                        </Columns>
                                    </asp:GridView>
                                </div>
                            </div>

                            <!-- Inventory Report -->
                            <div class="tab-pane fade" id="inventory" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>Inventory Status</h6>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="showLowStock()">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Low Stock
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="exportReport('inventory')">
                                            <i class="fas fa-download me-1"></i>Export
                                        </button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <asp:GridView ID="gvInventoryReport" runat="server" CssClass="table table-hover" 
                                        AutoGenerateColumns="false" EmptyDataText="No inventory data found.">
                                        <Columns>
                                            <asp:BoundField DataField="MedicineName" HeaderText="Medicine" />
                                            <asp:BoundField DataField="Category" HeaderText="Category" />
                                            <asp:BoundField DataField="CurrentStock" HeaderText="Current Stock" />
                                            <asp:BoundField DataField="MinimumLevel" HeaderText="Min Level" />
                                            <asp:BoundField DataField="StockValue" HeaderText="Stock Value" DataFormatString="${0:F2}" />
                                            <asp:TemplateField HeaderText="Status">
                                                <ItemTemplate>
                                                    <span class="badge bg-<%# GetStockStatusColor(Convert.ToInt32(Eval("CurrentStock")), Convert.ToInt32(Eval("MinimumLevel"))) %>">
                                                        <%# GetStockStatus(Convert.ToInt32(Eval("CurrentStock")), Convert.ToInt32(Eval("MinimumLevel"))) %>
                                                    </span>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                        </Columns>
                                    </asp:GridView>
                                </div>
                            </div>

                            <!-- Orders Report -->
                            <div class="tab-pane fade" id="orders" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>Order Analysis</h6>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportReport('orders')">
                                        <i class="fas fa-download me-1"></i>Export
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <asp:GridView ID="gvOrdersReport" runat="server" CssClass="table table-hover" 
                                        AutoGenerateColumns="false" EmptyDataText="No orders data found.">
                                        <Columns>
                                            <asp:BoundField DataField="OrderNumber" HeaderText="Order #" />
                                            <asp:BoundField DataField="CustomerName" HeaderText="Customer" />
                                            <asp:BoundField DataField="OrderDate" HeaderText="Date" DataFormatString="{0:MMM dd, yyyy}" />
                                            <asp:BoundField DataField="ItemCount" HeaderText="Items" />
                                            <asp:BoundField DataField="TotalAmount" HeaderText="Amount" DataFormatString="${0:F2}" />
                                            <asp:TemplateField HeaderText="Status">
                                                <ItemTemplate>
                                                    <span class="badge bg-<%# GetOrderStatusColor(Eval("Status").ToString()) %>">
                                                        <%# Eval("Status") %>
                                                    </span>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                        </Columns>
                                    </asp:GridView>
                                </div>
                            </div>

                            <!-- Customers Report -->
                            <div class="tab-pane fade" id="customers" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>Customer Analytics</h6>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportReport('customers')">
                                        <i class="fas fa-download me-1"></i>Export
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <asp:GridView ID="gvCustomersReport" runat="server" CssClass="table table-hover" 
                                        AutoGenerateColumns="false" EmptyDataText="No customer data found.">
                                        <Columns>
                                            <asp:BoundField DataField="CustomerName" HeaderText="Customer" />
                                            <asp:BoundField DataField="Email" HeaderText="Email" />
                                            <asp:BoundField DataField="TotalOrders" HeaderText="Orders" />
                                            <asp:BoundField DataField="TotalSpent" HeaderText="Total Spent" DataFormatString="${0:F2}" />
                                            <asp:BoundField DataField="AvgOrderValue" HeaderText="Avg Order" DataFormatString="${0:F2}" />
                                            <asp:BoundField DataField="LastOrderDate" HeaderText="Last Order" DataFormatString="{0:MMM dd, yyyy}" />
                                            <asp:TemplateField HeaderText="Segment">
                                                <ItemTemplate>
                                                    <span class="badge bg-<%# GetCustomerSegmentColor(Convert.ToDecimal(Eval("TotalSpent"))) %>">
                                                        <%# GetCustomerSegment(Convert.ToDecimal(Eval("TotalSpent"))) %>
                                                    </span>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                        </Columns>
                                    </asp:GridView>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Report Modal -->
    <div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduleReportModalLabel">Schedule Report</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="scheduleForm">
                        <div class="mb-3">
                            <label class="form-label">Report Type</label>
                            <select class="form-select" id="reportType">
                                <option value="sales">Sales Report</option>
                                <option value="inventory">Inventory Report</option>
                                <option value="orders">Orders Report</option>
                                <option value="customers">Customers Report</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Frequency</label>
                            <select class="form-select" id="frequency">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Email Recipients</label>
                            <textarea class="form-control" id="recipients" rows="3" placeholder="Enter email addresses separated by commas"></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeCharts">
                                <label class="form-check-label" for="includeCharts">
                                    Include charts and graphs
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-info" onclick="saveScheduledReport()">Schedule Report</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .bg-gradient-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        
        .bg-gradient-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }
        
        .bg-gradient-info {
            background: linear-gradient(45deg, #17a2b8, #117a8b);
        }
        
        .bg-gradient-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
        }
        
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
        }
        
        .nav-tabs .nav-link.active {
            background-color: transparent;
            border-bottom: 2px solid #007bff;
            color: #007bff;
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let revenueChart, categoriesChart;

        $(document).ready(function() {
            initializeCharts();
            loadChartData();
        });

        function initializeCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Revenue',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // Categories Chart
            const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
            categoriesChart = new Chart(categoriesCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8',
                            '#6f42c1', '#fd7e14', '#20c997', '#6c757d', '#343a40'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function loadChartData() {
            $.ajax({
                type: 'POST',
                url: 'Reports.aspx/GetChartData',
                data: JSON.stringify({}),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        updateCharts(response.d.data);
                    }
                },
                error: function() {
                    console.error('Error loading chart data');
                }
            });
        }

        function updateCharts(data) {
            // Update revenue chart
            revenueChart.data.labels = data.revenueLabels;
            revenueChart.data.datasets[0].data = data.revenueData;
            revenueChart.update();

            // Update categories chart
            categoriesChart.data.labels = data.categoryLabels;
            categoriesChart.data.datasets[0].data = data.categoryData;
            categoriesChart.update();
        }

        function changeChart(chartType, period) {
            // Update chart based on period selection
            $.ajax({
                type: 'POST',
                url: 'Reports.aspx/GetChartData',
                data: JSON.stringify({ chartType: chartType, period: period }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        updateCharts(response.d.data);
                    }
                }
            });
        }

        function exportReport(reportType) {
            window.open(`ExportReport.aspx?type=${reportType}`, '_blank');
        }

        function exportAllReports() {
            window.open('ExportAllReports.aspx', '_blank');
        }

        function scheduleReport() {
            $('#scheduleReportModal').modal('show');
        }

        function saveScheduledReport() {
            const data = {
                reportType: $('#reportType').val(),
                frequency: $('#frequency').val(),
                recipients: $('#recipients').val(),
                includeCharts: $('#includeCharts').is(':checked')
            };

            $.ajax({
                type: 'POST',
                url: 'Reports.aspx/ScheduleReport',
                data: JSON.stringify(data),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Report scheduled successfully!');
                        $('#scheduleReportModal').modal('hide');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error scheduling report.');
                }
            });
        }

        function refreshDashboard() {
            location.reload();
        }

        function showLowStock() {
            // Filter inventory report to show only low stock items
            // This would be implemented based on your specific requirements
        }
    </script>
</asp:Content>
