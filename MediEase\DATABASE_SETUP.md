# 🗄️ MediEase Database Setup Guide

## 📋 Overview

MediEase uses SQL Server LocalDB for development and can be configured for SQL Server for production. The database will be automatically created in the `App_Data` folder as `MediEase.mdf`.

## 🚀 Quick Setup (Automatic)

### Option 1: Automatic Database Creation (Recommended)
The application will automatically create the database when you first run it:

1. **Build and Run** the application
2. **Entity Framework** will automatically create the database structure
3. **Database seeder** will populate initial data
4. **Ready to use!**

### Option 2: Manual Database Creation
If you prefer manual setup:

1. Open **SQL Server Management Studio** or **Visual Studio**
2. Connect to **(LocalDB)\MSSQLLocalDB**
3. Run the script: `Scripts/CreateLocalDatabase.sql`

## 📊 Database Schema Overview

### 🔑 Core Tables

#### **Users Table**
- **Purpose**: User authentication and profile management
- **Key Fields**: UserId, Email, PasswordHash, Role, LoyaltyPoints
- **Roles**: <PERSON><PERSON>, Pharmacist, Customer

#### **Medicines Table**
- **Purpose**: Product catalog and inventory
- **Key Fields**: MedicineId, Name, Price, StockQuantity, PrescriptionRequired
- **Features**: Categories, brands, ratings, discounts

#### **Orders Table**
- **Purpose**: Order management and tracking
- **Key Fields**: OrderId, OrderNumber, Status, TotalAmount, PaymentStatus
- **Workflow**: Pending → Processing → Shipped → Delivered

#### **Prescriptions Table**
- **Purpose**: Prescription upload and verification
- **Key Fields**: PrescriptionId, UserId, Status, FilePath
- **AI Integration**: Automatic text extraction and processing

### 🔗 Relationship Overview

```
Users (1) ←→ (Many) Orders
Users (1) ←→ (Many) Prescriptions
Users (1) ←→ (Many) ShoppingCart
Orders (1) ←→ (Many) OrderItems
Medicines (1) ←→ (Many) OrderItems
Categories (1) ←→ (Many) Medicines
Brands (1) ←→ (Many) Medicines
```

## 📁 Complete Table Structure

### **Authentication & Users**
- `Users` - User accounts and profiles
- `UserSessions` - Active user sessions
- `UserPreferences` - User settings and preferences

### **Product Catalog**
- `Categories` - Medicine categories
- `Brands` - Pharmaceutical brands
- `Medicines` - Product catalog
- `MedicineImages` - Product images
- `MedicineInteractions` - Drug interactions

### **Inventory Management**
- `StockMovements` - Inventory tracking
- `DailySalesSummary` - Sales analytics

### **Orders & Transactions**
- `Orders` - Customer orders
- `OrderItems` - Order line items
- `ShoppingCart` - Shopping cart items

### **Medical Features**
- `Prescriptions` - Uploaded prescriptions
- `PrescriptionItems` - Prescription medications
- `FamilyProfiles` - Family member profiles
- `HealthReminders` - Medication reminders

### **Reviews & Ratings**
- `MedicineReviews` - Product reviews
- `ReviewHelpfulness` - Review voting

### **Loyalty & Marketing**
- `LoyaltyTransactions` - Points tracking
- `Offers` - Coupons and discounts
- `UserOfferUsage` - Coupon usage tracking

### **Communications**
- `Notifications` - In-app notifications
- `EmailQueue` - Email delivery queue
- `SMSQueue` - SMS delivery queue

### **AI & Analytics**
- `ChatSessions` - AI chatbot sessions
- `ChatMessages` - Chat conversation history
- `AIRecommendations` - AI-generated suggestions

### **System Management**
- `SystemSettings` - Application configuration
- `AuditLogs` - User activity tracking
- `ErrorLogs` - Error and exception logging
- `FileUploads` - File upload tracking
- `UserActivityLogs` - User behavior analytics

## 🔧 Configuration

### **Connection String**
The application uses this connection string in `Web.config`:

```xml
<connectionStrings>
  <add name="MediEaseConnection" 
       connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MediEase.mdf;Integrated Security=True;Connect Timeout=30" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### **Entity Framework Configuration**
The application uses Entity Framework 6 with Code First approach:
- **Context**: `MediEaseContext.cs`
- **Models**: Located in `Models/` folder
- **Migrations**: Automatic database creation

## 📈 Performance Optimization

### **Indexes Created**
- User email and role indexes
- Medicine category and status indexes
- Order customer and date indexes
- Prescription user and status indexes

### **Views for Reporting**
- `vw_OrderSummary` - Order details with customer info
- `vw_MedicineInventory` - Stock status and pricing
- `vw_CustomerOrderHistory` - Customer analytics
- `vw_DailySalesReport` - Daily sales metrics

### **Stored Procedures**
- `sp_UpdateMedicineStock` - Inventory management
- `sp_ProcessOrder` - Order status updates

## 🔒 Security Features

### **Data Protection**
- Password hashing with BCrypt
- SQL injection prevention
- Input validation and sanitization
- Secure file upload handling

### **Access Control**
- Role-based permissions
- Session management
- Audit logging
- Error tracking

## 🚀 Production Deployment

### **SQL Server Setup**
For production, update the connection string:

```xml
<connectionStrings>
  <add name="MediEaseConnection" 
       connectionString="Data Source=your-server;Initial Catalog=MediEase;User ID=your-user;Password=your-password;Encrypt=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### **Database Migration**
1. Create production SQL Server database
2. Run `Scripts/CreateDatabase.sql` for full schema
3. Update connection string
4. Test connectivity

### **Backup Strategy**
- Daily automated backups
- Transaction log backups every 15 minutes
- Monthly full backups with retention
- Test restore procedures regularly

## 🛠️ Maintenance

### **Regular Tasks**
- Monitor database size and growth
- Review and archive old logs
- Update statistics and rebuild indexes
- Monitor slow queries and optimize

### **Health Checks**
- Database connectivity
- Disk space monitoring
- Performance metrics
- Error log review

## 📞 Troubleshooting

### **Common Issues**

#### **Database Connection Failed**
- Verify LocalDB is installed
- Check connection string
- Ensure App_Data folder exists and is writable

#### **Tables Not Created**
- Run the application once to trigger Entity Framework
- Check for migration errors in logs
- Manually run CreateLocalDatabase.sql if needed

#### **Performance Issues**
- Check database size and available disk space
- Review slow query logs
- Consider index optimization

### **Diagnostic Queries**

```sql
-- Check database size
SELECT 
    DB_NAME() AS DatabaseName,
    (SELECT SUM(size) * 8 / 1024 FROM sys.database_files WHERE type = 0) AS DataSizeMB,
    (SELECT SUM(size) * 8 / 1024 FROM sys.database_files WHERE type = 1) AS LogSizeMB;

-- Check table row counts
SELECT 
    t.name AS TableName,
    p.rows AS RowCount
FROM sys.tables t
INNER JOIN sys.partitions p ON t.object_id = p.object_id
WHERE p.index_id IN (0,1)
ORDER BY p.rows DESC;

-- Check recent orders
SELECT TOP 10 * FROM Orders ORDER BY OrderDate DESC;

-- Check user counts by role
SELECT Role, COUNT(*) as UserCount FROM Users GROUP BY Role;
```

---

**🎯 The database is now ready to support all MediEase features including AI integration, e-commerce functionality, and comprehensive pharmacy management!**
