using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Customer
{
    public partial class LoyaltyProgram : Page
    {
        private int currentUserPoints = 0;
        private string currentTier = "Bronze";

        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsCustomer() && !SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadLoyaltyData();
                LoadRewards();
                LoadPointsHistory();
            }
        }

        private void LoadLoyaltyData()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find(currentUser.UserId);
                    if (user != null)
                    {
                        currentUserPoints = user.LoyaltyPoints;
                        lblCurrentPoints.Text = currentUserPoints.ToString("N0");
                        lblPointsValue.Text = (currentUserPoints * 0.01m).ToString("F2"); // 1 point = $0.01

                        // Determine tier
                        currentTier = GetUserTier(currentUserPoints);
                        lblTierName.Text = currentTier;

                        // Calculate points to next reward
                        var nextRewardPoints = GetNextRewardThreshold(currentUserPoints);
                        lblPointsToNextReward.Text = (nextRewardPoints - currentUserPoints).ToString("N0");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading loyalty data");
            }
        }

        private void LoadRewards()
        {
            try
            {
                // Sample rewards data - in a real application, this would come from a database
                var rewards = new List<object>
                {
                    new { RewardId = 1, Title = "$5 Off Next Order", Description = "Get $5 discount on your next purchase", Type = "discount", PointsRequired = 500 },
                    new { RewardId = 2, Title = "Free Shipping", Description = "Free shipping on your next order", Type = "shipping", PointsRequired = 200 },
                    new { RewardId = 3, Title = "$10 Off Next Order", Description = "Get $10 discount on your next purchase", Type = "discount", PointsRequired = 1000 },
                    new { RewardId = 4, Title = "Free Health Consultation", Description = "30-minute consultation with our pharmacist", Type = "service", PointsRequired = 1500 },
                    new { RewardId = 5, Title = "$25 Off Next Order", Description = "Get $25 discount on your next purchase", Type = "discount", PointsRequired = 2500 },
                    new { RewardId = 6, Title = "Premium Membership", Description = "1 month of premium membership benefits", Type = "membership", PointsRequired = 3000 }
                };

                rptRewards.DataSource = rewards;
                rptRewards.DataBind();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading rewards");
            }
        }

        private void LoadPointsHistory()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                // Sample points history - in a real application, this would come from a database
                var history = new List<object>
                {
                    new { Date = DateTime.Now.AddDays(-1), Description = "Purchase Order #12345", Type = "purchase", Points = 150, Balance = currentUserPoints, OrderNumber = "12345" },
                    new { Date = DateTime.Now.AddDays(-5), Description = "Product Review", Type = "review", Points = 50, Balance = currentUserPoints - 150, OrderNumber = "" },
                    new { Date = DateTime.Now.AddDays(-10), Description = "Redeemed $5 Off Coupon", Type = "redemption", Points = -500, Balance = currentUserPoints - 200, OrderNumber = "" },
                    new { Date = DateTime.Now.AddDays(-15), Description = "Purchase Order #12340", Type = "purchase", Points = 200, Balance = currentUserPoints + 300, OrderNumber = "12340" },
                    new { Date = DateTime.Now.AddDays(-20), Description = "Birthday Bonus", Type = "bonus", Points = 200, Balance = currentUserPoints + 100, OrderNumber = "" }
                };

                gvPointsHistory.DataSource = history;
                gvPointsHistory.DataBind();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading points history");
            }
        }

        protected void btnViewRewards_Click(object sender, EventArgs e)
        {
            // Scroll to rewards section
            ClientScript.RegisterStartupScript(this.GetType(), "scrollToRewards", 
                "document.querySelector('.card:nth-child(3)').scrollIntoView({ behavior: 'smooth' });", true);
        }

        [WebMethod]
        public static object RedeemReward(int rewardId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null)
                {
                    return new { success = false, message = "User not authenticated" };
                }

                using (var db = new MediEaseContext())
                {
                    var user = db.Users.Find(currentUser.UserId);
                    if (user == null)
                    {
                        return new { success = false, message = "User not found" };
                    }

                    // Get reward details (in a real app, this would be from database)
                    var rewardPoints = GetRewardPoints(rewardId);
                    if (rewardPoints == 0)
                    {
                        return new { success = false, message = "Invalid reward" };
                    }

                    if (user.LoyaltyPoints < rewardPoints)
                    {
                        return new { success = false, message = "Insufficient points" };
                    }

                    // Deduct points
                    user.LoyaltyPoints -= rewardPoints;
                    db.SaveChanges();

                    // Log the redemption
                    ErrorLogger.LogUserActivity($"Redeemed reward {rewardId} for {rewardPoints} points", user.UserId);

                    return new { success = true, message = "Reward redeemed successfully!" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error redeeming reward");
                return new { success = false, message = "An error occurred while redeeming the reward" };
            }
        }

        // Helper methods
        private string GetUserTier(int points)
        {
            if (points >= 5000) return "Platinum";
            if (points >= 3000) return "Gold";
            if (points >= 1000) return "Silver";
            return "Bronze";
        }

        private int GetNextRewardThreshold(int currentPoints)
        {
            if (currentPoints < 1000) return 1000;
            if (currentPoints < 3000) return 3000;
            if (currentPoints < 5000) return 5000;
            return 10000; // Next major milestone
        }

        protected string GetTierClass(string tier)
        {
            return tier == currentTier ? "active" : "";
        }

        protected int GetProgressPercentage()
        {
            var nextThreshold = GetNextRewardThreshold(currentUserPoints);
            var previousThreshold = GetPreviousThreshold(currentUserPoints);
            var progress = (double)(currentUserPoints - previousThreshold) / (nextThreshold - previousThreshold) * 100;
            return Math.Min(100, Math.Max(0, (int)progress));
        }

        protected int GetTierProgress()
        {
            return GetProgressPercentage();
        }

        private int GetPreviousThreshold(int currentPoints)
        {
            if (currentPoints >= 5000) return 5000;
            if (currentPoints >= 3000) return 3000;
            if (currentPoints >= 1000) return 1000;
            return 0;
        }

        protected int GetCurrentUserPoints()
        {
            return currentUserPoints;
        }

        protected string GetRewardIcon(string type)
        {
            switch (type.ToLower())
            {
                case "discount": return "fas fa-percent";
                case "shipping": return "fas fa-shipping-fast";
                case "service": return "fas fa-user-md";
                case "membership": return "fas fa-crown";
                case "product": return "fas fa-gift";
                default: return "fas fa-star";
            }
        }

        protected string GetActivityIcon(string type)
        {
            switch (type.ToLower())
            {
                case "purchase": return "fas fa-shopping-cart text-success";
                case "review": return "fas fa-star text-warning";
                case "redemption": return "fas fa-gift text-danger";
                case "bonus": return "fas fa-birthday-cake text-primary";
                case "referral": return "fas fa-user-friends text-info";
                default: return "fas fa-circle text-muted";
            }
        }

        private static int GetRewardPoints(int rewardId)
        {
            // In a real application, this would query the database
            var rewardPoints = new Dictionary<int, int>
            {
                { 1, 500 },   // $5 Off
                { 2, 200 },   // Free Shipping
                { 3, 1000 },  // $10 Off
                { 4, 1500 },  // Health Consultation
                { 5, 2500 },  // $25 Off
                { 6, 3000 }   // Premium Membership
            };

            return rewardPoints.ContainsKey(rewardId) ? rewardPoints[rewardId] : 0;
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Loyalty Program - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Earn points with every purchase and unlock exclusive rewards in the MediEase loyalty program.");
                master.AddMetaKeywords("loyalty program, rewards, points, discounts, pharmacy benefits");
            }
        }
    }
}
