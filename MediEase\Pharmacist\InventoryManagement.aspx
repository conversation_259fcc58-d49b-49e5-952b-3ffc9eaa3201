<%@ Page Title="Inventory Management" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="InventoryManagement.aspx.cs" Inherits="MediEase.Pharmacist.InventoryManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-boxes me-2"></i>Inventory Management</h2>
                        <p class="text-muted">Manage medicine stock, batches, and inventory levels</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addMedicineModal">
                            <i class="fas fa-plus me-2"></i>Add Medicine
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
                            <i class="fas fa-upload me-2"></i>Bulk Update
                        </button>
                        <button type="button" class="btn btn-warning" onclick="generateLowStockReport()">
                            <i class="fas fa-exclamation-triangle me-2"></i>Low Stock Alert
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-pills fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalMedicines" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Medicines</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblInStock" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">In Stock</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblLowStock" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Low Stock</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblOutOfStock" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Out of Stock</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expiry Alerts -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-calendar-times me-2"></i>Expiry Alerts</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-danger"><asp:Label ID="lblExpiredMedicines" runat="server" Text="0"></asp:Label></h4>
                                    <small>Expired Medicines</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-warning"><asp:Label ID="lblExpiringThisMonth" runat="server" Text="0"></asp:Label></h4>
                                    <small>Expiring This Month</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-info"><asp:Label ID="lblExpiringNext3Months" runat="server" Text="0"></asp:Label></h4>
                                    <small>Expiring in 3 Months</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">Category</label>
                                <asp:DropDownList ID="ddlCategoryFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlCategoryFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Categories</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Stock Status</label>
                                <asp:DropDownList ID="ddlStockFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStockFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Stock</asp:ListItem>
                                    <asp:ListItem Value="InStock">In Stock</asp:ListItem>
                                    <asp:ListItem Value="LowStock">Low Stock</asp:ListItem>
                                    <asp:ListItem Value="OutOfStock">Out of Stock</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Expiry Status</label>
                                <asp:DropDownList ID="ddlExpiryFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlExpiryFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All</asp:ListItem>
                                    <asp:ListItem Value="Expired">Expired</asp:ListItem>
                                    <asp:ListItem Value="ExpiringThisMonth">Expiring This Month</asp:ListItem>
                                    <asp:ListItem Value="Expiring3Months">Expiring in 3 Months</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search by name, batch, or barcode..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <asp:Button ID="btnRefresh" runat="server" CssClass="btn btn-outline-secondary" Text="Refresh" OnClick="btnRefresh_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory List -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Medicine Inventory</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="toggleView('table')">
                                <i class="fas fa-table"></i> Table View
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="toggleView('grid')">
                                <i class="fas fa-th"></i> Grid View
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Table View -->
                        <div id="tableView">
                            <div class="table-responsive">
                                <asp:GridView ID="gvInventory" runat="server" CssClass="table table-hover" 
                                    AutoGenerateColumns="false" EmptyDataText="No medicines found." OnRowCommand="gvInventory_RowCommand">
                                    <Columns>
                                        <asp:TemplateField HeaderText="Medicine Info">
                                            <ItemTemplate>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' 
                                                             alt="Medicine" class="rounded" width="50" height="50" />
                                                    </div>
                                                    <div>
                                                        <strong><%# Eval("Name") %></strong><br>
                                                        <small class="text-muted"><%# Eval("GenericName") %></small><br>
                                                        <small class="text-muted">SKU: <%# Eval("SKU") %></small>
                                                    </div>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Category & Brand">
                                            <ItemTemplate>
                                                <div>
                                                    <span class="badge bg-info"><%# Eval("Category") %></span><br>
                                                    <small class="text-muted"><%# Eval("Brand") %></small><br>
                                                    <small class="text-muted"><%# Eval("Strength") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Stock Level">
                                            <ItemTemplate>
                                                <div class="text-center">
                                                    <h5 class="mb-1 text-<%# GetStockColor(Convert.ToInt32(Eval("StockQuantity")), Convert.ToInt32(Eval("MinimumStock"))) %>">
                                                        <%# Eval("StockQuantity") %>
                                                    </h5>
                                                    <small class="text-muted">Min: <%# Eval("MinimumStock") %></small><br>
                                                    <span class="badge bg-<%# GetStockStatusColor(Convert.ToInt32(Eval("StockQuantity")), Convert.ToInt32(Eval("MinimumStock"))) %>">
                                                        <%# GetStockStatus(Convert.ToInt32(Eval("StockQuantity")), Convert.ToInt32(Eval("MinimumStock"))) %>
                                                    </span>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Pricing">
                                            <ItemTemplate>
                                                <div>
                                                    <strong>$<%# String.Format("{0:F2}", Eval("Price")) %></strong><br>
                                                    <small class="text-muted">Cost: $<%# String.Format("{0:F2}", Eval("CostPrice")) %></small><br>
                                                    <small class="text-success">Margin: <%# String.Format("{0:F1}%", GetProfitMargin(Convert.ToDecimal(Eval("Price")), Convert.ToDecimal(Eval("CostPrice")))) %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Batch & Expiry">
                                            <ItemTemplate>
                                                <div>
                                                    <strong>Batch: <%# Eval("BatchNumber") %></strong><br>
                                                    <small class="text-muted">Mfg: <%# Convert.ToDateTime(Eval("ManufactureDate")).ToString("MM/yyyy") %></small><br>
                                                    <small class="text-<%# GetExpiryColor(Convert.ToDateTime(Eval("ExpiryDate"))) %>">
                                                        Exp: <%# Convert.ToDateTime(Eval("ExpiryDate")).ToString("MM/yyyy") %>
                                                    </small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Status">
                                            <ItemTemplate>
                                                <div>
                                                    <%# Convert.ToBoolean(Eval("IsActive")) ? 
                                                        "<span class=\"badge bg-success\">Active</span>" : 
                                                        "<span class=\"badge bg-danger\">Inactive</span>" %>
                                                    <%# Convert.ToBoolean(Eval("RequiresPrescription")) ? 
                                                        "<br><span class=\"badge bg-warning mt-1\">Rx Required</span>" : "" %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Actions">
                                            <ItemTemplate>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info btn-sm view-medicine" 
                                                        data-medicine-id='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm edit-medicine mt-1" 
                                                        data-medicine-id='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-edit me-1"></i>Edit
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm update-stock mt-1" 
                                                        data-medicine-id='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-plus me-1"></i>Stock
                                                    </button>
                                                    <asp:LinkButton runat="server" CssClass="btn btn-outline-warning btn-sm mt-1" 
                                                        CommandName="SetReorderAlert" CommandArgument='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-bell me-1"></i>Alert
                                                    </asp:LinkButton>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>

                        <!-- Grid View (Hidden by default) -->
                        <div id="gridView" class="row g-4" style="display: none;">
                            <asp:Repeater ID="rptInventoryGrid" runat="server">
                                <ItemTemplate>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card medicine-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span class="badge bg-info"><%# Eval("Category") %></span>
                                                <span class="badge bg-<%# GetStockStatusColor(Convert.ToInt32(Eval("StockQuantity")), Convert.ToInt32(Eval("MinimumStock"))) %>">
                                                    <%# GetStockStatus(Convert.ToInt32(Eval("StockQuantity")), Convert.ToInt32(Eval("MinimumStock"))) %>
                                                </span>
                                            </div>
                                            <div class="card-body text-center">
                                                <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' 
                                                     alt="Medicine" class="rounded mb-3" width="80" height="80" />
                                                <h6 class="card-title"><%# Eval("Name") %></h6>
                                                <p class="card-text small text-muted"><%# Eval("GenericName") %></p>
                                                <div class="row text-center mb-3">
                                                    <div class="col-4">
                                                        <h6 class="text-<%# GetStockColor(Convert.ToInt32(Eval("StockQuantity")), Convert.ToInt32(Eval("MinimumStock"))) %>">
                                                            <%# Eval("StockQuantity") %>
                                                        </h6>
                                                        <small class="text-muted">Stock</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <h6 class="text-primary">$<%# String.Format("{0:F2}", Eval("Price")) %></h6>
                                                        <small class="text-muted">Price</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <h6 class="text-<%# GetExpiryColor(Convert.ToDateTime(Eval("ExpiryDate"))) %>">
                                                            <%# Convert.ToDateTime(Eval("ExpiryDate")).ToString("MM/yy") %>
                                                        </h6>
                                                        <small class="text-muted">Expiry</small>
                                                    </div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-muted">Batch: <%# Eval("BatchNumber") %></small><br>
                                                    <small class="text-muted">Brand: <%# Eval("Brand") %></small>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-grid gap-2">
                                                    <div class="btn-group">
                                                        <button class="btn btn-outline-info btn-sm view-medicine" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                            <i class="fas fa-eye me-1"></i>View
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm edit-medicine" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                            <i class="fas fa-edit me-1"></i>Edit
                                                        </button>
                                                    </div>
                                                    <button class="btn btn-outline-success btn-sm update-stock" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-plus me-1"></i>Update Stock
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Medicines Message -->
        <asp:Panel ID="pnlNoMedicines" runat="server" Visible="false">
            <div class="text-center py-5">
                <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                <h4>No medicines found</h4>
                <p class="text-muted">No medicines match your current filter criteria.</p>
            </div>
        </asp:Panel>
    </div>

    <!-- Add Medicine Modal -->
    <div class="modal fade" id="addMedicineModal" tabindex="-1" aria-labelledby="addMedicineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addMedicineModalLabel">Add New Medicine</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="addMedicineForm">
                        <!-- Add medicine form will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Medicine Details Modal -->
    <div class="modal fade" id="medicineModal" tabindex="-1" aria-labelledby="medicineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="medicineModalLabel">Medicine Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="medicineDetails">
                        <!-- Medicine details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="medicineActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Update Modal -->
    <div class="modal fade" id="stockUpdateModal" tabindex="-1" aria-labelledby="stockUpdateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="stockUpdateModalLabel">Update Stock</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="stockUpdateForm">
                        <!-- Stock update form will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .medicine-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .medicine-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
    </style>

    <script>
        function toggleView(viewType) {
            const tableView = document.getElementById('tableView');
            const gridView = document.getElementById('gridView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'table') {
                tableView.style.display = 'block';
                gridView.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                tableView.style.display = 'none';
                gridView.style.display = 'flex';
                buttons[1].classList.add('active');
            }
        }

        // View medicine details
        $(document).on('click', '.view-medicine', function() {
            const medicineId = $(this).data('medicine-id');
            loadMedicineDetails(medicineId);
        });

        // Edit medicine
        $(document).on('click', '.edit-medicine', function() {
            const medicineId = $(this).data('medicine-id');
            loadEditMedicineForm(medicineId);
        });

        // Update stock
        $(document).on('click', '.update-stock', function() {
            const medicineId = $(this).data('medicine-id');
            loadStockUpdateForm(medicineId);
        });

        function loadMedicineDetails(medicineId) {
            $.ajax({
                type: 'POST',
                url: 'InventoryManagement.aspx/GetMedicineDetails',
                data: JSON.stringify({ medicineId: medicineId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#medicineDetails').html(response.d.html);
                        $('#medicineActions').html(response.d.actions);
                        $('#medicineModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading medicine details.');
                }
            });
        }

        function loadStockUpdateForm(medicineId) {
            $.ajax({
                type: 'POST',
                url: 'InventoryManagement.aspx/GetStockUpdateForm',
                data: JSON.stringify({ medicineId: medicineId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#stockUpdateForm').html(response.d.html);
                        $('#stockUpdateModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading stock update form.');
                }
            });
        }

        function generateLowStockReport() {
            window.open('Reports/LowStockReport.aspx', '_blank');
        }
    </script>
</asp:Content>
