using System;
using System.Web;
using System.Web.Routing;
using MediEase.Utilities;

namespace MediEase
{
    public class Global : HttpApplication
    {
        protected void Application_Start(object sender, EventArgs e)
        {
            // Initialize application
            InitializeApplication();
        }

        protected void Application_End(object sender, EventArgs e)
        {
            // Cleanup when application ends
            ErrorLogger.LogInfo("Application ended", "System");
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            // Global error handling
            var exception = Server.GetLastError();
            if (exception != null)
            {
                ErrorLogger.LogError(exception, "Unhandled application error");
            }
        }

        protected void Session_Start(object sender, EventArgs e)
        {
            // Initialize session
            Session["SessionStartTime"] = DateTime.Now;
            
            // Log session start
            try
            {
                var userInfo = SecurityHelper.GetCurrentUser();
                if (userInfo != null)
                {
                    ErrorLogger.LogUserActivity("Session started", userInfo.UserId);
                }
            }
            catch
            {
                // Silent fail for session logging
            }
        }

        protected void Session_End(object sender, EventArgs e)
        {
            // Cleanup session
            try
            {
                var userInfo = SecurityHelper.GetCurrentUser();
                if (userInfo != null)
                {
                    ErrorLogger.LogUserActivity("Session ended", userInfo.UserId);
                }
            }
            catch
            {
                // Silent fail for session logging
            }
        }

        private void InitializeApplication()
        {
            try
            {
                // Log application start
                ErrorLogger.LogInfo("MediEase application started", "System");

                // Register routes if needed
                RegisterRoutes(RouteTable.Routes);

                // Initialize any required services
                InitializeServices();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error during application initialization");
            }
        }

        private void RegisterRoutes(RouteCollection routes)
        {
            // Add custom routes here if needed
            // For now, using default Web Forms routing
        }

        private void InitializeServices()
        {
            // Initialize database with schema and sample data
            try
            {
                DatabaseInitializer.InitializeDatabase();
                ErrorLogger.LogInfo("Database initialized successfully", "InitializeServices");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Database initialization failed");
                // Continue startup even if database initialization fails
            }

            // Initialize any background services or caches
            // Test AI connection on startup
            try
            {
                var aiConnectionTest = AIHelper.TestConnectionAsync();
                // Don't wait for the result, just start the test
            }
            catch
            {
                // Silent fail for AI connection test
            }
        }
    }
}
