<%@ Page Title="Internal Messaging" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="InternalMessaging.aspx.cs" Inherits="MediEase.Shared.InternalMessaging" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-comments me-2 text-primary"></i>Internal Messaging</h2>
                        <p class="text-muted">Communicate with team members and manage internal communications</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#composeModal">
                            <i class="fas fa-plus me-2"></i>New Message
                        </button>
                        <button type="button" class="btn btn-info" onclick="refreshMessages()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <!-- Message Stats -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Message Stats</h6>
                            <i class="fas fa-chart-pie text-primary"></i>
                        </div>
                        <div class="stats-item d-flex justify-content-between mb-2">
                            <span>Unread</span>
                            <span class="badge bg-danger"><asp:Label ID="lblUnreadCount" runat="server" Text="0"></asp:Label></span>
                        </div>
                        <div class="stats-item d-flex justify-content-between mb-2">
                            <span>Sent</span>
                            <span class="badge bg-primary"><asp:Label ID="lblSentCount" runat="server" Text="0"></asp:Label></span>
                        </div>
                        <div class="stats-item d-flex justify-content-between mb-2">
                            <span>Drafts</span>
                            <span class="badge bg-secondary"><asp:Label ID="lblDraftsCount" runat="server" Text="0"></asp:Label></span>
                        </div>
                        <div class="stats-item d-flex justify-content-between">
                            <span>Archived</span>
                            <span class="badge bg-info"><asp:Label ID="lblArchivedCount" runat="server" Text="0"></asp:Label></span>
                        </div>
                    </div>
                </div>

                <!-- Folders -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Folders</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <asp:LinkButton ID="lnkInbox" runat="server" CssClass="list-group-item list-group-item-action active" OnClick="lnkInbox_Click">
                            <i class="fas fa-inbox me-2"></i>Inbox
                            <span class="badge bg-danger float-end"><asp:Label ID="lblInboxCount" runat="server" Text="0"></asp:Label></span>
                        </asp:LinkButton>
                        <asp:LinkButton ID="lnkSent" runat="server" CssClass="list-group-item list-group-item-action" OnClick="lnkSent_Click">
                            <i class="fas fa-paper-plane me-2"></i>Sent
                        </asp:LinkButton>
                        <asp:LinkButton ID="lnkDrafts" runat="server" CssClass="list-group-item list-group-item-action" OnClick="lnkDrafts_Click">
                            <i class="fas fa-edit me-2"></i>Drafts
                        </asp:LinkButton>
                        <asp:LinkButton ID="lnkArchived" runat="server" CssClass="list-group-item list-group-item-action" OnClick="lnkArchived_Click">
                            <i class="fas fa-archive me-2"></i>Archived
                        </asp:LinkButton>
                        <asp:LinkButton ID="lnkImportant" runat="server" CssClass="list-group-item list-group-item-action" OnClick="lnkImportant_Click">
                            <i class="fas fa-star me-2"></i>Important
                        </asp:LinkButton>
                    </div>
                </div>

                <!-- Quick Contacts -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h6 class="mb-0">Quick Contacts</h6>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptQuickContacts" runat="server">
                            <ItemTemplate>
                                <div class="contact-item d-flex align-items-center mb-2 p-2 rounded hover-bg">
                                    <div class="avatar me-2">
                                        <img src='<%# GetUserAvatar(Eval("UserId")) %>' alt="Avatar" class="rounded-circle" width="30" height="30" />
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold small"><%# Eval("FirstName") %> <%# Eval("LastName") %></div>
                                        <div class="text-muted small"><%# Eval("Role") %></div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary quick-message" 
                                        data-user-id='<%# Eval("UserId") %>' data-user-name='<%# Eval("FirstName") %> <%# Eval("LastName") %>'>
                                        <i class="fas fa-comment"></i>
                                    </button>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Search and Filters -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search messages..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-outline-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <asp:DropDownList ID="ddlPriorityFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlPriorityFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Priorities</asp:ListItem>
                                    <asp:ListItem Value="High">High Priority</asp:ListItem>
                                    <asp:ListItem Value="Medium">Medium Priority</asp:ListItem>
                                    <asp:ListItem Value="Low">Low Priority</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Messages</asp:ListItem>
                                    <asp:ListItem Value="Unread">Unread</asp:ListItem>
                                    <asp:ListItem Value="Read">Read</asp:ListItem>
                                    <asp:ListItem Value="Replied">Replied</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messages List -->
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            <asp:Label ID="lblFolderName" runat="server" Text="Inbox"></asp:Label>
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="selectAllMessages()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="markAsRead()">
                                <i class="fas fa-eye"></i> Mark Read
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="archiveSelected()">
                                <i class="fas fa-archive"></i> Archive
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteSelected()">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="messages-container">
                            <asp:Repeater ID="rptMessages" runat="server" OnItemCommand="rptMessages_ItemCommand">
                                <ItemTemplate>
                                    <div class="message-item d-flex align-items-center p-3 border-bottom hover-bg <%# !Convert.ToBoolean(Eval("IsRead")) ? "unread-message" : "" %>" 
                                         data-message-id='<%# Eval("MessageId") %>'>
                                        <div class="form-check me-3">
                                            <input class="form-check-input message-checkbox" type="checkbox" value='<%# Eval("MessageId") %>'>
                                        </div>
                                        <div class="message-priority me-3">
                                            <%# GetPriorityIcon(Eval("Priority").ToString()) %>
                                        </div>
                                        <div class="message-avatar me-3">
                                            <img src='<%# GetUserAvatar(Eval("SenderId")) %>' alt="Avatar" class="rounded-circle" width="40" height="40" />
                                        </div>
                                        <div class="message-content flex-grow-1 cursor-pointer" onclick="openMessage(<%# Eval("MessageId") %>)">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="message-header">
                                                    <div class="sender-name fw-bold"><%# Eval("SenderName") %></div>
                                                    <div class="message-subject"><%# Eval("Subject") %></div>
                                                    <div class="message-preview text-muted small"><%# TruncateText(Eval("Message").ToString(), 100) %></div>
                                                </div>
                                                <div class="message-meta text-end">
                                                    <div class="message-time small text-muted"><%# GetRelativeTime(Convert.ToDateTime(Eval("CreatedDate"))) %></div>
                                                    <div class="message-actions mt-1">
                                                        <%# Convert.ToBoolean(Eval("IsImportant")) ? "<i class='fas fa-star text-warning'></i>" : "" %>
                                                        <%# Convert.ToBoolean(Eval("HasAttachment")) ? "<i class='fas fa-paperclip text-info ms-1'></i>" : "" %>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="message-actions-dropdown">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="openMessage(<%# Eval("MessageId") %>)">
                                                        <i class="fas fa-eye me-2"></i>Open</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="replyMessage(<%# Eval("MessageId") %>)">
                                                        <i class="fas fa-reply me-2"></i>Reply</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="forwardMessage(<%# Eval("MessageId") %>)">
                                                        <i class="fas fa-share me-2"></i>Forward</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><asp:LinkButton runat="server" CssClass="dropdown-item" 
                                                        CommandName="ToggleImportant" CommandArgument='<%# Eval("MessageId") %>'>
                                                        <i class="fas fa-star me-2"></i><%# Convert.ToBoolean(Eval("IsImportant")) ? "Remove Star" : "Add Star" %>
                                                    </asp:LinkButton></li>
                                                    <li><asp:LinkButton runat="server" CssClass="dropdown-item" 
                                                        CommandName="Archive" CommandArgument='<%# Eval("MessageId") %>'>
                                                        <i class="fas fa-archive me-2"></i>Archive
                                                    </asp:LinkButton></li>
                                                    <li><asp:LinkButton runat="server" CssClass="dropdown-item text-danger" 
                                                        CommandName="Delete" CommandArgument='<%# Eval("MessageId") %>'
                                                        OnClientClick="return confirm('Are you sure you want to delete this message?');">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </asp:LinkButton></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>

                        <!-- No Messages -->
                        <asp:Panel ID="pnlNoMessages" runat="server" Visible="false">
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5>No messages found</h5>
                                <p class="text-muted">Your inbox is empty or no messages match your current filter.</p>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compose Message Modal -->
    <div class="modal fade" id="composeModal" tabindex="-1" aria-labelledby="composeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="composeModalLabel">Compose Message</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="composeForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">To *</label>
                                    <asp:DropDownList ID="ddlRecipients" runat="server" CssClass="form-select" multiple required>
                                    </asp:DropDownList>
                                    <small class="text-muted">Hold Ctrl to select multiple recipients</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <asp:DropDownList ID="ddlPriority" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="Low">Low</asp:ListItem>
                                        <asp:ListItem Value="Medium" Selected="true">Medium</asp:ListItem>
                                        <asp:ListItem Value="High">High</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Subject *</label>
                            <asp:TextBox ID="txtSubject" runat="server" CssClass="form-control" placeholder="Enter message subject" required></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Message *</label>
                            <asp:TextBox ID="txtMessage" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="8" 
                                placeholder="Type your message here..." required></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Attachments</label>
                            <asp:FileUpload ID="fuAttachments" runat="server" CssClass="form-control" AllowMultiple="true" />
                            <small class="text-muted">Maximum 5 files, 10MB each</small>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkImportant" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkImportant.ClientID %>">
                                    Mark as important
                                </label>
                            </div>
                            <div class="form-check">
                                <asp:CheckBox ID="chkRequestReadReceipt" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkRequestReadReceipt.ClientID %>">
                                    Request read receipt
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="saveDraft()">
                        <i class="fas fa-save me-1"></i>Save Draft
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSendMessage" runat="server" CssClass="btn btn-success" Text="Send Message" OnClick="btnSendMessage_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Message Details Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalLabel">Message Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="messageDetails">
                        <!-- Message details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="messageActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .unread-message {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        .message-item:hover {
            background-color: #e9ecef;
        }
        
        .hover-bg:hover {
            background-color: #f8f9fa;
        }
        
        .cursor-pointer {
            cursor: pointer;
        }
        
        .message-preview {
            max-width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .contact-item:hover {
            background-color: #f8f9fa;
        }
        
        .avatar img {
            object-fit: cover;
        }
    </style>

    <script>
        // Open message details
        function openMessage(messageId) {
            $.ajax({
                type: 'POST',
                url: 'InternalMessaging.aspx/GetMessageDetails',
                data: JSON.stringify({ messageId: messageId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#messageDetails').html(response.d.html);
                        $('#messageActions').html(response.d.actions);
                        $('#messageModal').modal('show');
                        
                        // Mark as read
                        markMessageAsRead(messageId);
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading message details.');
                }
            });
        }

        // Quick message
        $(document).on('click', '.quick-message', function() {
            const userId = $(this).data('user-id');
            const userName = $(this).data('user-name');
            
            $('#<%= ddlRecipients.ClientID %>').val(userId);
            $('#<%= txtSubject.ClientID %>').val('Quick message to ' + userName);
            $('#composeModal').modal('show');
        });

        // Reply to message
        function replyMessage(messageId) {
            $.ajax({
                type: 'POST',
                url: 'InternalMessaging.aspx/GetReplyData',
                data: JSON.stringify({ messageId: messageId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        populateReplyForm(response.d.data);
                        $('#composeModal').modal('show');
                    }
                }
            });
        }

        // Forward message
        function forwardMessage(messageId) {
            $.ajax({
                type: 'POST',
                url: 'InternalMessaging.aspx/GetForwardData',
                data: JSON.stringify({ messageId: messageId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        populateForwardForm(response.d.data);
                        $('#composeModal').modal('show');
                    }
                }
            });
        }

        // Bulk actions
        function selectAllMessages() {
            $('.message-checkbox').prop('checked', true);
        }

        function markAsRead() {
            const selectedIds = getSelectedMessageIds();
            if (selectedIds.length === 0) {
                alert('Please select messages to mark as read.');
                return;
            }
            
            bulkAction('MarkAsRead', selectedIds);
        }

        function archiveSelected() {
            const selectedIds = getSelectedMessageIds();
            if (selectedIds.length === 0) {
                alert('Please select messages to archive.');
                return;
            }
            
            bulkAction('Archive', selectedIds);
        }

        function deleteSelected() {
            const selectedIds = getSelectedMessageIds();
            if (selectedIds.length === 0) {
                alert('Please select messages to delete.');
                return;
            }
            
            if (confirm('Are you sure you want to delete the selected messages?')) {
                bulkAction('Delete', selectedIds);
            }
        }

        function getSelectedMessageIds() {
            const ids = [];
            $('.message-checkbox:checked').each(function() {
                ids.push($(this).val());
            });
            return ids;
        }

        function bulkAction(action, messageIds) {
            $.ajax({
                type: 'POST',
                url: 'InternalMessaging.aspx/BulkAction',
                data: JSON.stringify({ action: action, messageIds: messageIds }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Action completed successfully!');
                        location.reload();
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error performing bulk action.');
                }
            });
        }

        function markMessageAsRead(messageId) {
            $.ajax({
                type: 'POST',
                url: 'InternalMessaging.aspx/MarkAsRead',
                data: JSON.stringify({ messageId: messageId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        // Update UI to show message as read
                        $(`.message-item[data-message-id="${messageId}"]`).removeClass('unread-message');
                    }
                }
            });
        }

        function saveDraft() {
            // Save current compose form as draft
            const formData = {
                recipients: $('#<%= ddlRecipients.ClientID %>').val(),
                subject: $('#<%= txtSubject.ClientID %>').val(),
                message: $('#<%= txtMessage.ClientID %>').val(),
                priority: $('#<%= ddlPriority.ClientID %>').val(),
                isImportant: $('#<%= chkImportant.ClientID %>').is(':checked')
            };

            $.ajax({
                type: 'POST',
                url: 'InternalMessaging.aspx/SaveDraft',
                data: JSON.stringify(formData),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showSuccessMessage('Draft saved successfully!');
                        $('#composeModal').modal('hide');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error saving draft.');
                }
            });
        }

        function refreshMessages() {
            location.reload();
        }

        function populateReplyForm(data) {
            $('#<%= ddlRecipients.ClientID %>').val(data.senderId);
            $('#<%= txtSubject.ClientID %>').val('Re: ' + data.subject);
            $('#<%= txtMessage.ClientID %>').val('\n\n--- Original Message ---\n' + data.originalMessage);
        }

        function populateForwardForm(data) {
            $('#<%= txtSubject.ClientID %>').val('Fwd: ' + data.subject);
            $('#<%= txtMessage.ClientID %>').val('\n\n--- Forwarded Message ---\nFrom: ' + data.senderName + '\nSubject: ' + data.subject + '\n\n' + data.originalMessage);
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            // Check for new messages without full page reload
            checkForNewMessages();
        }, 30000);

        function checkForNewMessages() {
            $.ajax({
                type: 'POST',
                url: 'InternalMessaging.aspx/CheckNewMessages',
                data: JSON.stringify({}),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.hasNewMessages) {
                        // Update unread count
                        $('#<%= lblUnreadCount.ClientID %>').text(response.d.unreadCount);
                        $('#<%= lblInboxCount.ClientID %>').text(response.d.unreadCount);
                        
                        // Show notification
                        showInfoMessage('You have new messages!');
                    }
                }
            });
        }
    </script>
</asp:Content>
