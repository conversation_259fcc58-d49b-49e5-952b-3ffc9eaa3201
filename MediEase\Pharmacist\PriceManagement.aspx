<%@ Page Title="Price & Offer Management" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="PriceManagement.aspx.cs" Inherits="MediEase.Pharmacist.PriceManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-tags me-2 text-primary"></i>Price & Offer Management</h2>
                        <p class="text-muted">Manage medicine prices, discounts, and promotional offers</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#bulkPriceUpdateModal">
                            <i class="fas fa-edit me-2"></i>Bulk Price Update
                        </button>
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#createOfferModal">
                            <i class="fas fa-percentage me-2"></i>Create Offer
                        </button>
                        <button type="button" class="btn btn-info" onclick="exportPriceList()">
                            <i class="fas fa-download me-2"></i>Export Prices
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-pills fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalMedicines" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Medicines</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-percentage fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActiveOffers" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Offers</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblExpiringOffers" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Expiring Soon</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>$<asp:Label ID="lblAvgPrice" runat="server" Text="0.00"></asp:Label></h4>
                        <p class="mb-0">Average Price</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Category Filter</label>
                                <asp:DropDownList ID="ddlCategoryFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlCategoryFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Categories</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Price Range</label>
                                <asp:DropDownList ID="ddlPriceRange" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlPriceRange_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Prices</asp:ListItem>
                                    <asp:ListItem Value="0-10">$0 - $10</asp:ListItem>
                                    <asp:ListItem Value="10-25">$10 - $25</asp:ListItem>
                                    <asp:ListItem Value="25-50">$25 - $50</asp:ListItem>
                                    <asp:ListItem Value="50-100">$50 - $100</asp:ListItem>
                                    <asp:ListItem Value="100+">$100+</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Offer Status</label>
                                <asp:DropDownList ID="ddlOfferFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlOfferFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Items</asp:ListItem>
                                    <asp:ListItem Value="with-offer">With Active Offers</asp:ListItem>
                                    <asp:ListItem Value="without-offer">Without Offers</asp:ListItem>
                                    <asp:ListItem Value="expired-offer">Expired Offers</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search medicines..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Price Management Grid -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Medicine Prices</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="selectAllItems()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="bulkPriceUpdate()">
                                <i class="fas fa-edit"></i> Bulk Update
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="bulkOfferCreate()">
                                <i class="fas fa-percentage"></i> Bulk Offer
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <asp:GridView ID="gvPrices" runat="server" CssClass="table table-hover" 
                                AutoGenerateColumns="false" EmptyDataText="No medicines found." OnRowCommand="gvPrices_RowCommand"
                                AllowPaging="true" PageSize="20" OnPageIndexChanging="gvPrices_PageIndexChanging">
                                <Columns>
                                    <asp:TemplateField HeaderText="Select">
                                        <ItemTemplate>
                                            <div class="form-check">
                                                <input class="form-check-input medicine-checkbox" type="checkbox" value='<%# Eval("MedicineId") %>'>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Medicine">
                                        <ItemTemplate>
                                            <div class="d-flex align-items-center">
                                                <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' alt="Medicine" class="rounded me-3" width="50" height="50" />
                                                <div>
                                                    <strong><%# Eval("Name") %></strong><br>
                                                    <small class="text-muted"><%# Eval("GenericName") %></small><br>
                                                    <span class="badge bg-info"><%# Eval("Category") %></span>
                                                </div>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Current Price">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <h5 class="text-success mb-1">$<%# String.Format("{0:F2}", Eval("Price")) %></h5>
                                                <small class="text-muted">Cost: $<%# String.Format("{0:F2}", Eval("CostPrice")) %></small><br>
                                                <small class="text-muted">Margin: <%# String.Format("{0:F1}%", GetProfitMargin(Convert.ToDecimal(Eval("Price")), Convert.ToDecimal(Eval("CostPrice")))) %></small>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Offers">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ? 
                                                    $"<span class='badge bg-warning text-dark fs-6'>{Eval("DiscountPercentage")}% OFF</span><br>" +
                                                    $"<small class='text-muted'>Sale: ${Convert.ToDecimal(Eval("Price")) * (1 - Convert.ToDecimal(Eval("DiscountPercentage")) / 100):F2}</small>" :
                                                    "<span class='text-muted'>No active offers</span>" %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Stock & Sales">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <strong>Stock: <%# Eval("StockQuantity") %></strong><br>
                                                <small class="text-muted">Sold: <%# GetSalesCount(Convert.ToInt32(Eval("MedicineId"))) %></small><br>
                                                <small class="text-muted">Revenue: $<%# GetRevenue(Convert.ToInt32(Eval("MedicineId"))):F2 %></small>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Actions">
                                        <ItemTemplate>
                                            <div class="btn-group-vertical btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary btn-sm update-price" 
                                                    data-medicine-id='<%# Eval("MedicineId") %>' data-current-price='<%# Eval("Price") %>'>
                                                    <i class="fas fa-edit me-1"></i>Update Price
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-sm create-offer mt-1" 
                                                    data-medicine-id='<%# Eval("MedicineId") %>' data-medicine-name='<%# Eval("Name") %>'>
                                                    <i class="fas fa-percentage me-1"></i>Create Offer
                                                </button>
                                                <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ? 
                                                    $"<button type='button' class='btn btn-outline-danger btn-sm mt-1 remove-offer' data-medicine-id='{Eval("MedicineId")}'>" +
                                                    "<i class='fas fa-times me-1'></i>Remove Offer</button>" : "" %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                                <PagerStyle CssClass="pagination-ys" />
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Price Modal -->
    <div class="modal fade" id="updatePriceModal" tabindex="-1" aria-labelledby="updatePriceModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updatePriceModalLabel">Update Medicine Price</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="priceUpdateForm">
                        <input type="hidden" id="hiddenMedicineId" />
                        <div class="mb-3">
                            <label class="form-label">Medicine Name</label>
                            <input type="text" class="form-control" id="medicineName" readonly />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Current Price</label>
                            <input type="text" class="form-control" id="currentPrice" readonly />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">New Price *</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <asp:TextBox ID="txtNewPrice" runat="server" CssClass="form-control" placeholder="0.00" required></asp:TextBox>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Cost Price</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <asp:TextBox ID="txtCostPrice" runat="server" CssClass="form-control" placeholder="0.00"></asp:TextBox>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Reason for Change</label>
                            <asp:TextBox ID="txtPriceChangeReason" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                placeholder="Enter reason for price change..."></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkNotifyCustomers" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkNotifyCustomers.ClientID %>">
                                    Notify customers about price change
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnUpdatePrice" runat="server" CssClass="btn btn-primary" Text="Update Price" OnClick="btnUpdatePrice_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Create Offer Modal -->
    <div class="modal fade" id="createOfferModal" tabindex="-1" aria-labelledby="createOfferModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createOfferModalLabel">Create Promotional Offer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="offerForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Offer Title *</label>
                                    <asp:TextBox ID="txtOfferTitle" runat="server" CssClass="form-control" placeholder="e.g., Summer Sale" required></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Offer Type *</label>
                                    <asp:DropDownList ID="ddlOfferType" runat="server" CssClass="form-select" required>
                                        <asp:ListItem Value="">Select Offer Type</asp:ListItem>
                                        <asp:ListItem Value="percentage">Percentage Discount</asp:ListItem>
                                        <asp:ListItem Value="fixed">Fixed Amount Discount</asp:ListItem>
                                        <asp:ListItem Value="bogo">Buy One Get One</asp:ListItem>
                                        <asp:ListItem Value="bulk">Bulk Discount</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Discount Value *</label>
                                    <div class="input-group">
                                        <asp:TextBox ID="txtDiscountValue" runat="server" CssClass="form-control" placeholder="0" required></asp:TextBox>
                                        <span class="input-group-text" id="discountUnit">%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Minimum Quantity</label>
                                    <asp:TextBox ID="txtMinQuantity" runat="server" CssClass="form-control" placeholder="1" TextMode="Number"></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Start Date *</label>
                                    <asp:TextBox ID="txtStartDate" runat="server" CssClass="form-control" TextMode="DateTimeLocal" required></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">End Date *</label>
                                    <asp:TextBox ID="txtEndDate" runat="server" CssClass="form-control" TextMode="DateTimeLocal" required></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <asp:TextBox ID="txtOfferDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                placeholder="Describe the offer details..."></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkOfferActive" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkOfferActive.ClientID %>">
                                    Activate offer immediately
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnCreateOffer" runat="server" CssClass="btn btn-warning" Text="Create Offer" OnClick="btnCreateOffer_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Price Update Modal -->
    <div class="modal fade" id="bulkPriceUpdateModal" tabindex="-1" aria-labelledby="bulkPriceUpdateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkPriceUpdateModalLabel">Bulk Price Update</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="bulkUpdateForm">
                        <div class="mb-3">
                            <label class="form-label">Update Type</label>
                            <asp:DropDownList ID="ddlBulkUpdateType" runat="server" CssClass="form-select">
                                <asp:ListItem Value="percentage">Percentage Increase/Decrease</asp:ListItem>
                                <asp:ListItem Value="fixed">Fixed Amount Increase/Decrease</asp:ListItem>
                                <asp:ListItem Value="set">Set Specific Price</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Value</label>
                            <div class="input-group">
                                <asp:TextBox ID="txtBulkValue" runat="server" CssClass="form-control" placeholder="0" required></asp:TextBox>
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Apply To</label>
                            <asp:DropDownList ID="ddlBulkApplyTo" runat="server" CssClass="form-select">
                                <asp:ListItem Value="selected">Selected Items Only</asp:ListItem>
                                <asp:ListItem Value="category">Entire Category</asp:ListItem>
                                <asp:ListItem Value="all">All Medicines</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Reason</label>
                            <asp:TextBox ID="txtBulkReason" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                placeholder="Enter reason for bulk price update..."></asp:TextBox>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnBulkUpdate" runat="server" CssClass="btn btn-success" Text="Update Prices" OnClick="btnBulkUpdate_Click" />
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update price modal
        $(document).on('click', '.update-price', function() {
            const medicineId = $(this).data('medicine-id');
            const currentPrice = $(this).data('current-price');
            const medicineName = $(this).closest('tr').find('strong').text();
            
            $('#hiddenMedicineId').val(medicineId);
            $('#medicineName').val(medicineName);
            $('#currentPrice').val('$' + parseFloat(currentPrice).toFixed(2));
            $('#<%= txtNewPrice.ClientID %>').val(currentPrice);
            
            $('#updatePriceModal').modal('show');
        });

        // Create offer modal
        $(document).on('click', '.create-offer', function() {
            const medicineId = $(this).data('medicine-id');
            const medicineName = $(this).data('medicine-name');
            
            $('#<%= txtOfferTitle.ClientID %>').val(medicineName + ' Special Offer');
            $('#createOfferModal').modal('show');
        });

        // Offer type change
        $('#<%= ddlOfferType.ClientID %>').change(function() {
            const offerType = $(this).val();
            const unit = $('#discountUnit');
            
            switch(offerType) {
                case 'percentage':
                    unit.text('%');
                    break;
                case 'fixed':
                    unit.text('$');
                    break;
                default:
                    unit.text('');
            }
        });

        // Bulk update type change
        $('#<%= ddlBulkUpdateType.ClientID %>').change(function() {
            const updateType = $(this).val();
            const unit = $('#bulkPriceUpdateModal .input-group-text');
            
            switch(updateType) {
                case 'percentage':
                    unit.text('%');
                    break;
                case 'fixed':
                case 'set':
                    unit.text('$');
                    break;
            }
        });

        function selectAllItems() {
            $('.medicine-checkbox').prop('checked', true);
        }

        function bulkPriceUpdate() {
            const selected = $('.medicine-checkbox:checked').length;
            if (selected === 0) {
                alert('Please select medicines to update.');
                return;
            }
            $('#bulkPriceUpdateModal').modal('show');
        }

        function bulkOfferCreate() {
            const selected = $('.medicine-checkbox:checked').length;
            if (selected === 0) {
                alert('Please select medicines to create offers for.');
                return;
            }
            $('#createOfferModal').modal('show');
        }

        function exportPriceList() {
            window.open('ExportPrices.aspx', '_blank');
        }

        // Remove offer
        $(document).on('click', '.remove-offer', function() {
            const medicineId = $(this).data('medicine-id');
            if (confirm('Are you sure you want to remove this offer?')) {
                // AJAX call to remove offer
                $.post('PriceManagement.aspx/RemoveOffer', 
                    { medicineId: medicineId }, 
                    function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error removing offer: ' + response.message);
                        }
                    });
            }
        });
    </script>
</asp:Content>
