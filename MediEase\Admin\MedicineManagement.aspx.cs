using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.Services;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;
using System.IO;

namespace MediEase.Admin
{
    public partial class MedicineManagement : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is admin
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || currentUser.Role != "Admin")
                {
                    Response.Redirect("~/Login.aspx");
                    return;
                }

                LoadStatistics();
                LoadFilters();
                LoadMedicines();
            }
        }

        private void LoadStatistics()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    lblTotalMedicines.Text = db.Medicines.Count().ToString();
                    lblActiveMedicines.Text = db.Medicines.Count(m => m.IsActive).ToString();
                    lblTotalCategories.Text = db.Categories.Count().ToString();
                    lblTotalBrands.Text = db.Brands.Count().ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "LoadStatistics");
            }
        }

        private void LoadFilters()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    // Load categories
                    var categories = db.Categories.Where(c => c.IsActive).OrderBy(c => c.Name).ToList();
                    ddlCategoryFilter.DataSource = categories;
                    ddlCategoryFilter.DataTextField = "Name";
                    ddlCategoryFilter.DataValueField = "CategoryId";
                    ddlCategoryFilter.DataBind();
                    ddlCategoryFilter.Items.Insert(0, new ListItem("All Categories", ""));

                    // Load brands
                    var brands = db.Brands.Where(b => b.IsActive).OrderBy(b => b.Name).ToList();
                    ddlBrandFilter.DataSource = brands;
                    ddlBrandFilter.DataTextField = "Name";
                    ddlBrandFilter.DataValueField = "BrandId";
                    ddlBrandFilter.DataBind();
                    ddlBrandFilter.Items.Insert(0, new ListItem("All Brands", ""));
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "LoadFilters");
            }
        }

        private void LoadMedicines()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var query = db.Medicines.AsQueryable();

                    // Apply filters
                    if (!string.IsNullOrEmpty(ddlCategoryFilter.SelectedValue))
                    {
                        int categoryId = int.Parse(ddlCategoryFilter.SelectedValue);
                        query = query.Where(m => m.CategoryId == categoryId);
                    }

                    if (!string.IsNullOrEmpty(ddlBrandFilter.SelectedValue))
                    {
                        int brandId = int.Parse(ddlBrandFilter.SelectedValue);
                        query = query.Where(m => m.BrandId == brandId);
                    }

                    if (!string.IsNullOrEmpty(ddlStatusFilter.SelectedValue))
                    {
                        bool isActive = bool.Parse(ddlStatusFilter.SelectedValue);
                        query = query.Where(m => m.IsActive == isActive);
                    }

                    if (!string.IsNullOrEmpty(txtSearch.Text))
                    {
                        string searchTerm = txtSearch.Text.ToLower();
                        query = query.Where(m => m.Name.ToLower().Contains(searchTerm) || 
                                               m.GenericName.ToLower().Contains(searchTerm));
                    }

                    var medicines = query.OrderBy(m => m.Name).ToList();
                    
                    gvMedicines.DataSource = medicines;
                    gvMedicines.DataBind();

                    rptMedicinesGrid.DataSource = medicines;
                    rptMedicinesGrid.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "LoadMedicines");
                ShowErrorMessage("Error loading medicines: " + ex.Message);
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadMedicines();
        }

        protected void ddlCategoryFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadMedicines();
        }

        protected void ddlBrandFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadMedicines();
        }

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadMedicines();
        }

        protected void gvMedicines_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvMedicines.PageIndex = e.NewPageIndex;
            LoadMedicines();
        }

        protected void gvMedicines_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            int medicineId = Convert.ToInt32(e.CommandArgument);

            switch (e.CommandName)
            {
                case "ToggleStatus":
                    ToggleMedicineStatus(medicineId);
                    break;
                case "Delete":
                    DeleteMedicine(medicineId);
                    break;
            }
        }

        protected void rptMedicinesGrid_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            int medicineId = Convert.ToInt32(e.CommandArgument);

            switch (e.CommandName)
            {
                case "ToggleStatus":
                    ToggleMedicineStatus(medicineId);
                    break;
                case "Delete":
                    DeleteMedicine(medicineId);
                    break;
            }
        }

        private void ToggleMedicineStatus(int medicineId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var medicine = db.Medicines.Find(medicineId);
                    if (medicine != null)
                    {
                        medicine.IsActive = !medicine.IsActive;
                        medicine.ModifiedDate = DateTime.Now;
                        medicine.ModifiedBy = SecurityHelper.GetCurrentUser()?.UserId;
                        
                        db.SaveChanges();
                        
                        LoadMedicines();
                        LoadStatistics();
                        
                        ShowSuccessMessage($"Medicine {(medicine.IsActive ? "activated" : "deactivated")} successfully.");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "ToggleMedicineStatus");
                ShowErrorMessage("Error updating medicine status.");
            }
        }

        private void DeleteMedicine(int medicineId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var medicine = db.Medicines.Find(medicineId);
                    if (medicine != null)
                    {
                        // Check if medicine is used in any orders
                        bool hasOrders = db.OrderItems.Any(oi => oi.MedicineId == medicineId);
                        if (hasOrders)
                        {
                            ShowErrorMessage("Cannot delete medicine as it has been used in orders. You can deactivate it instead.");
                            return;
                        }

                        db.Medicines.Remove(medicine);
                        db.SaveChanges();
                        
                        LoadMedicines();
                        LoadStatistics();
                        
                        ShowSuccessMessage("Medicine deleted successfully.");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "DeleteMedicine");
                ShowErrorMessage("Error deleting medicine.");
            }
        }

        protected void btnSaveCategory_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtCategoryName.Text))
                {
                    ShowErrorMessage("Category name is required.");
                    return;
                }

                using (var db = new MediEaseContext())
                {
                    var category = new Category
                    {
                        Name = SecurityHelper.SanitizeInput(txtCategoryName.Text.Trim()),
                        Description = SecurityHelper.SanitizeInput(txtCategoryDescription.Text.Trim()),
                        IsActive = chkCategoryActive.Checked,
                        CreatedDate = DateTime.Now,
                        CreatedBy = SecurityHelper.GetCurrentUser()?.UserId
                    };

                    db.Categories.Add(category);
                    db.SaveChanges();

                    // Clear form
                    txtCategoryName.Text = "";
                    txtCategoryDescription.Text = "";
                    chkCategoryActive.Checked = true;

                    LoadFilters();
                    LoadStatistics();
                    
                    ShowSuccessMessage("Category added successfully.");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "btnSaveCategory_Click");
                ShowErrorMessage("Error saving category.");
            }
        }

        protected void btnSaveBrand_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtBrandName.Text))
                {
                    ShowErrorMessage("Brand name is required.");
                    return;
                }

                using (var db = new MediEaseContext())
                {
                    var brand = new Brand
                    {
                        Name = SecurityHelper.SanitizeInput(txtBrandName.Text.Trim()),
                        Manufacturer = SecurityHelper.SanitizeInput(txtManufacturer.Text.Trim()),
                        Country = SecurityHelper.SanitizeInput(txtCountry.Text.Trim()),
                        Description = SecurityHelper.SanitizeInput(txtBrandDescription.Text.Trim()),
                        IsActive = chkBrandActive.Checked,
                        CreatedDate = DateTime.Now,
                        CreatedBy = SecurityHelper.GetCurrentUser()?.UserId
                    };

                    db.Brands.Add(brand);
                    db.SaveChanges();

                    // Clear form
                    txtBrandName.Text = "";
                    txtManufacturer.Text = "";
                    txtCountry.Text = "";
                    txtBrandDescription.Text = "";
                    chkBrandActive.Checked = true;

                    LoadFilters();
                    LoadStatistics();
                    
                    ShowSuccessMessage("Brand added successfully.");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "btnSaveBrand_Click");
                ShowErrorMessage("Error saving brand.");
            }
        }

        protected string GetMedicineImage(object imagePath)
        {
            if (imagePath == null || string.IsNullOrEmpty(imagePath.ToString()))
            {
                return "~/Images/medicine-placeholder.png";
            }
            return "~/Uploads/Medicines/" + imagePath.ToString();
        }

        private void ShowSuccessMessage(string message)
        {
            ScriptManager.RegisterStartupScript(this, GetType(), "success",
                $"showSuccessMessage('{message.Replace("'", "\\'")}');", true);
        }

        private void ShowErrorMessage(string message)
        {
            ScriptManager.RegisterStartupScript(this, GetType(), "error",
                $"showErrorMessage('{message.Replace("'", "\\'")}');", true);
        }
    }
}
