<%@ Page Title="Register" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Register.aspx.cs" Inherits="MediEase.Register" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card shadow-lg border-0 mt-4">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>Create Your MediEase Account
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">Join thousands of satisfied customers</p>
                    </div>
                    <div class="card-body p-5">
                        <!-- Registration Form -->
                        <div class="needs-validation" novalidate>
                            <div class="row">
                                <!-- Personal Information -->
                                <div class="col-md-6">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-user me-2"></i>Personal Information
                                    </h5>
                                    
                                    <!-- First Name -->
                                    <div class="mb-3">
                                        <label for="txtFirstName" class="form-label">First Name *</label>
                                        <asp:TextBox ID="txtFirstName" runat="server" 
                                            CssClass="form-control" 
                                            placeholder="Enter your first name"
                                            MaxLength="50"
                                            required="true" />
                                        <asp:RequiredFieldValidator ID="rfvFirstName" runat="server" 
                                            ControlToValidate="txtFirstName" 
                                            ErrorMessage="First name is required" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                    </div>

                                    <!-- Last Name -->
                                    <div class="mb-3">
                                        <label for="txtLastName" class="form-label">Last Name *</label>
                                        <asp:TextBox ID="txtLastName" runat="server" 
                                            CssClass="form-control" 
                                            placeholder="Enter your last name"
                                            MaxLength="50"
                                            required="true" />
                                        <asp:RequiredFieldValidator ID="rfvLastName" runat="server" 
                                            ControlToValidate="txtLastName" 
                                            ErrorMessage="Last name is required" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                    </div>

                                    <!-- Email -->
                                    <div class="mb-3">
                                        <label for="txtEmail" class="form-label">Email Address *</label>
                                        <asp:TextBox ID="txtEmail" runat="server" 
                                            CssClass="form-control email-input" 
                                            TextMode="Email" 
                                            placeholder="Enter your email address"
                                            MaxLength="100"
                                            required="true" />
                                        <asp:RequiredFieldValidator ID="rfvEmail" runat="server" 
                                            ControlToValidate="txtEmail" 
                                            ErrorMessage="Email is required" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                        <asp:RegularExpressionValidator ID="revEmail" runat="server" 
                                            ControlToValidate="txtEmail" 
                                            ErrorMessage="Please enter a valid email address" 
                                            ValidationExpression="^[^\s@]+@[^\s@]+\.[^\s@]+$" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <!-- Phone Number -->
                                    <div class="mb-3">
                                        <label for="txtPhoneNumber" class="form-label">Phone Number *</label>
                                        <asp:TextBox ID="txtPhoneNumber" runat="server" 
                                            CssClass="form-control phone-input" 
                                            placeholder="(*************"
                                            MaxLength="15"
                                            required="true" />
                                        <asp:RequiredFieldValidator ID="rfvPhoneNumber" runat="server" 
                                            ControlToValidate="txtPhoneNumber" 
                                            ErrorMessage="Phone number is required" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <!-- Date of Birth -->
                                    <div class="mb-3">
                                        <label for="txtDateOfBirth" class="form-label">Date of Birth</label>
                                        <asp:TextBox ID="txtDateOfBirth" runat="server" 
                                            CssClass="form-control" 
                                            TextMode="Date" />
                                    </div>

                                    <!-- Gender -->
                                    <div class="mb-3">
                                        <label for="ddlGender" class="form-label">Gender</label>
                                        <asp:DropDownList ID="ddlGender" runat="server" CssClass="form-select">
                                            <asp:ListItem Text="Select Gender" Value="" />
                                            <asp:ListItem Text="Male" Value="Male" />
                                            <asp:ListItem Text="Female" Value="Female" />
                                            <asp:ListItem Text="Other" Value="Other" />
                                        </asp:DropDownList>
                                    </div>
                                </div>

                                <!-- Account & Address Information -->
                                <div class="col-md-6">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-lock me-2"></i>Account Security
                                    </h5>
                                    
                                    <!-- Password -->
                                    <div class="mb-3">
                                        <label for="txtPassword" class="form-label">Password *</label>
                                        <div class="input-group">
                                            <asp:TextBox ID="txtPassword" runat="server" 
                                                CssClass="form-control" 
                                                TextMode="Password" 
                                                placeholder="Create a strong password"
                                                required="true" />
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <asp:RequiredFieldValidator ID="rfvPassword" runat="server" 
                                            ControlToValidate="txtPassword" 
                                            ErrorMessage="Password is required" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                        <div id="password-strength" class="mt-2"></div>
                                    </div>

                                    <!-- Confirm Password -->
                                    <div class="mb-3">
                                        <label for="txtConfirmPassword" class="form-label">Confirm Password *</label>
                                        <asp:TextBox ID="txtConfirmPassword" runat="server" 
                                            CssClass="form-control" 
                                            TextMode="Password" 
                                            placeholder="Confirm your password"
                                            required="true" />
                                        <asp:RequiredFieldValidator ID="rfvConfirmPassword" runat="server" 
                                            ControlToValidate="txtConfirmPassword" 
                                            ErrorMessage="Please confirm your password" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                        <asp:CompareValidator ID="cvPassword" runat="server" 
                                            ControlToValidate="txtConfirmPassword" 
                                            ControlToCompare="txtPassword" 
                                            ErrorMessage="Passwords do not match" 
                                            CssClass="invalid-feedback" 
                                            Display="Dynamic" />
                                    </div>

                                    <h5 class="text-primary mb-3 mt-4">
                                        <i class="fas fa-map-marker-alt me-2"></i>Address Information
                                    </h5>

                                    <!-- Address -->
                                    <div class="mb-3">
                                        <label for="txtAddress" class="form-label">Street Address</label>
                                        <asp:TextBox ID="txtAddress" runat="server" 
                                            CssClass="form-control" 
                                            TextMode="MultiLine"
                                            Rows="2"
                                            placeholder="Enter your street address"
                                            MaxLength="500" />
                                    </div>

                                    <!-- City -->
                                    <div class="mb-3">
                                        <label for="txtCity" class="form-label">City</label>
                                        <asp:TextBox ID="txtCity" runat="server" 
                                            CssClass="form-control" 
                                            placeholder="Enter your city"
                                            MaxLength="100" />
                                    </div>

                                    <!-- Postal Code & Country -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="txtPostalCode" class="form-label">Postal Code</label>
                                                <asp:TextBox ID="txtPostalCode" runat="server" 
                                                    CssClass="form-control" 
                                                    placeholder="12345"
                                                    MaxLength="20" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="txtCountry" class="form-label">Country</label>
                                                <asp:TextBox ID="txtCountry" runat="server" 
                                                    CssClass="form-control" 
                                                    placeholder="United States"
                                                    MaxLength="100"
                                                    Text="United States" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="form-check">
                                        <asp:CheckBox ID="chkTerms" runat="server" CssClass="form-check-input" />
                                        <label class="form-check-label" for="<%= chkTerms.ClientID %>">
                                            I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                            <a href="#" target="_blank">Privacy Policy</a> *
                                        </label>
                                        <asp:CustomValidator ID="cvTerms" runat="server" 
                                            ErrorMessage="You must agree to the terms and conditions" 
                                            CssClass="invalid-feedback d-block" 
                                            Display="Dynamic"
                                            ClientValidationFunction="validateTerms" 
                                            OnServerValidate="cvTerms_ServerValidate" />
                                    </div>
                                </div>
                            </div>

                            <!-- Newsletter Subscription -->
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div class="form-check">
                                        <asp:CheckBox ID="chkNewsletter" runat="server" CssClass="form-check-input" Checked="true" />
                                        <label class="form-check-label" for="<%= chkNewsletter.ClientID %>">
                                            Subscribe to our newsletter for health tips and special offers
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Register Button -->
                            <div class="d-grid mt-4">
                                <asp:Button ID="btnRegister" runat="server" 
                                    CssClass="btn btn-primary btn-lg" 
                                    Text="Create Account" 
                                    OnClick="btnRegister_Click" />
                            </div>

                            <!-- Error/Success Messages -->
                            <asp:Panel ID="pnlError" runat="server" Visible="false" CssClass="alert alert-danger mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <asp:Literal ID="litErrorMessage" runat="server" />
                            </asp:Panel>

                            <asp:Panel ID="pnlSuccess" runat="server" Visible="false" CssClass="alert alert-success mt-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <asp:Literal ID="litSuccessMessage" runat="server" />
                            </asp:Panel>
                        </div>

                        <!-- Login Link -->
                        <div class="text-center mt-4">
                            <hr class="my-3">
                            <span class="text-muted bg-white px-3">Already have an account?</span>
                            <div class="mt-3">
                                <a href="~/Login.aspx" runat="server" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login Here
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>

<asp:Content ID="ScriptContent" ContentPlaceHolderID="ScriptContent" runat="server">
    <script>
        $(document).ready(function() {
            initializeRegistrationPage();
        });

        function initializeRegistrationPage() {
            // Password toggle functionality
            $('#togglePassword').click(function() {
                const passwordField = $('#<%= txtPassword.ClientID %>');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Password strength checking
            $('#<%= txtPassword.ClientID %>').on('input', function() {
                checkPasswordStrength($(this).val(), 'password-strength');
            });

            // Form validation
            setupFormValidation();
            
            // Auto-hide alerts
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 8000);
        }

        function setupFormValidation() {
            const form = $('.needs-validation')[0];
            
            $('#<%= btnRegister.ClientID %>').click(function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(form).addClass('was-validated');
            });

            // Real-time validation
            $('#<%= txtEmail.ClientID %>').on('blur', function() {
                validateEmailField($(this));
            });

            $('#<%= txtPhoneNumber.ClientID %>').on('input', function() {
                formatPhoneNumber($(this));
            });

            $('#<%= txtConfirmPassword.ClientID %>').on('input', function() {
                validatePasswordMatch();
            });
        }

        function validatePasswordMatch() {
            const password = $('#<%= txtPassword.ClientID %>').val();
            const confirmPassword = $('#<%= txtConfirmPassword.ClientID %>').val();
            const confirmField = $('#<%= txtConfirmPassword.ClientID %>');
            
            if (confirmPassword && password !== confirmPassword) {
                confirmField.addClass('is-invalid');
            } else if (confirmPassword) {
                confirmField.removeClass('is-invalid').addClass('is-valid');
            }
        }

        function validateTerms(sender, args) {
            args.IsValid = $('#<%= chkTerms.ClientID %>').is(':checked');
        }
    </script>
</asp:Content>
