<%@ Page Title="Family Profiles" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="FamilyProfiles.aspx.cs" Inherits="MediEase.Customer.FamilyProfiles" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-users me-2"></i>Family Profiles</h2>
                        <p class="text-muted">Manage health profiles for your family members</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFamilyMemberModal">
                            <i class="fas fa-plus me-2"></i>Add Family Member
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Family Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalMembers" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Family Members</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-prescription-bottle-alt fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActivePrescriptions" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Prescriptions</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblAllergies" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Known Allergies</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblUpcomingRefills" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Upcoming Refills</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Family Members List -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Family Members</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <asp:Repeater ID="rptFamilyMembers" runat="server" OnItemCommand="rptFamilyMembers_ItemCommand">
                                <ItemTemplate>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card family-member-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span class="badge bg-<%# GetRelationshipColor(Eval("Relationship").ToString()) %>">
                                                    <%# Eval("Relationship") %>
                                                </span>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item view-profile" href="#" data-member-id='<%# Eval("FamilyMemberId") %>'>
                                                            <i class="fas fa-eye me-2"></i>View Profile</a></li>
                                                        <li><a class="dropdown-item edit-profile" href="#" data-member-id='<%# Eval("FamilyMemberId") %>'>
                                                            <i class="fas fa-edit me-2"></i>Edit Profile</a></li>
                                                        <li><a class="dropdown-item view-prescriptions" href="#" data-member-id='<%# Eval("FamilyMemberId") %>'>
                                                            <i class="fas fa-prescription me-2"></i>Prescriptions</a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><asp:LinkButton runat="server" CssClass="dropdown-item text-danger" 
                                                            CommandName="Delete" CommandArgument='<%# Eval("FamilyMemberId") %>'
                                                            OnClientClick="return confirm('Are you sure you want to remove this family member?');">
                                                            <i class="fas fa-trash me-2"></i>Remove
                                                        </asp:LinkButton></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="card-body text-center">
                                                <div class="profile-avatar mb-3">
                                                    <img src='<%# GetProfileImage(Eval("ProfileImage")) %>' 
                                                         alt="Profile" class="rounded-circle" width="80" height="80" />
                                                </div>
                                                <h6 class="card-title"><%# Eval("FirstName") %> <%# Eval("LastName") %></h6>
                                                <p class="card-text small text-muted">
                                                    <%# GetAge(Convert.ToDateTime(Eval("DateOfBirth"))) %> years old
                                                </p>
                                                <div class="health-info mb-3">
                                                    <div class="row text-center">
                                                        <div class="col-4">
                                                            <small class="text-muted">Blood Type</small>
                                                            <div class="fw-bold"><%# Eval("BloodType") ?? "Unknown" %></div>
                                                        </div>
                                                        <div class="col-4">
                                                            <small class="text-muted">Allergies</small>
                                                            <div class="fw-bold text-<%# HasAllergies(Eval("Allergies")) ? "warning" : "success" %>">
                                                                <%# HasAllergies(Eval("Allergies")) ? "Yes" : "None" %>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <small class="text-muted">Medications</small>
                                                            <div class="fw-bold text-<%# HasMedications(Eval("CurrentMedications")) ? "info" : "muted" %>">
                                                                <%# HasMedications(Eval("CurrentMedications")) ? "Active" : "None" %>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="emergency-contact">
                                                    <small class="text-muted">Emergency Contact</small>
                                                    <div class="small"><%# Eval("EmergencyContactName") ?? "Not set" %></div>
                                                    <div class="small text-muted"><%# Eval("EmergencyContactPhone") ?? "" %></div>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-grid gap-2">
                                                    <button class="btn btn-outline-primary btn-sm view-profile" data-member-id='<%# Eval("FamilyMemberId") %>'>
                                                        <i class="fas fa-eye me-1"></i>View Full Profile
                                                    </button>
                                                    <div class="btn-group">
                                                        <button class="btn btn-outline-success btn-sm upload-prescription" data-member-id='<%# Eval("FamilyMemberId") %>'>
                                                            <i class="fas fa-upload me-1"></i>Upload Prescription
                                                        </button>
                                                        <button class="btn btn-outline-info btn-sm view-orders" data-member-id='<%# Eval("FamilyMemberId") %>'>
                                                            <i class="fas fa-shopping-cart me-1"></i>Orders
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>

                        <!-- No Family Members Message -->
                        <asp:Panel ID="pnlNoMembers" runat="server" Visible="false">
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h4>No family members added yet</h4>
                                <p class="text-muted">Add family members to manage their health profiles and prescriptions.</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFamilyMemberModal">
                                    <i class="fas fa-plus me-2"></i>Add Your First Family Member
                                </button>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Family Member Modal -->
    <div class="modal fade" id="addFamilyMemberModal" tabindex="-1" aria-labelledby="addFamilyMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addFamilyMemberModalLabel">Add Family Member</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addFamilyMemberForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">First Name *</label>
                                    <asp:TextBox ID="txtFirstName" runat="server" CssClass="form-control" required></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Last Name *</label>
                                    <asp:TextBox ID="txtLastName" runat="server" CssClass="form-control" required></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Date of Birth *</label>
                                    <asp:TextBox ID="txtDateOfBirth" runat="server" CssClass="form-control" TextMode="Date" required></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Gender</label>
                                    <asp:DropDownList ID="ddlGender" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="">Select Gender</asp:ListItem>
                                        <asp:ListItem Value="Male">Male</asp:ListItem>
                                        <asp:ListItem Value="Female">Female</asp:ListItem>
                                        <asp:ListItem Value="Other">Other</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Relationship *</label>
                                    <asp:DropDownList ID="ddlRelationship" runat="server" CssClass="form-select" required>
                                        <asp:ListItem Value="">Select Relationship</asp:ListItem>
                                        <asp:ListItem Value="Spouse">Spouse</asp:ListItem>
                                        <asp:ListItem Value="Child">Child</asp:ListItem>
                                        <asp:ListItem Value="Parent">Parent</asp:ListItem>
                                        <asp:ListItem Value="Sibling">Sibling</asp:ListItem>
                                        <asp:ListItem Value="Grandparent">Grandparent</asp:ListItem>
                                        <asp:ListItem Value="Grandchild">Grandchild</asp:ListItem>
                                        <asp:ListItem Value="Other">Other</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Blood Type</label>
                                    <asp:DropDownList ID="ddlBloodType" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="">Select Blood Type</asp:ListItem>
                                        <asp:ListItem Value="A+">A+</asp:ListItem>
                                        <asp:ListItem Value="A-">A-</asp:ListItem>
                                        <asp:ListItem Value="B+">B+</asp:ListItem>
                                        <asp:ListItem Value="B-">B-</asp:ListItem>
                                        <asp:ListItem Value="AB+">AB+</asp:ListItem>
                                        <asp:ListItem Value="AB-">AB-</asp:ListItem>
                                        <asp:ListItem Value="O+">O+</asp:ListItem>
                                        <asp:ListItem Value="O-">O-</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Known Allergies</label>
                            <asp:TextBox ID="txtAllergies" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" 
                                placeholder="List any known allergies (medications, food, environmental)"></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Current Medications</label>
                            <asp:TextBox ID="txtCurrentMedications" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" 
                                placeholder="List current medications and dosages"></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Medical Conditions</label>
                            <asp:TextBox ID="txtMedicalConditions" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" 
                                placeholder="List any chronic conditions or medical history"></asp:TextBox>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Emergency Contact Name</label>
                                    <asp:TextBox ID="txtEmergencyContactName" runat="server" CssClass="form-control"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Emergency Contact Phone</label>
                                    <asp:TextBox ID="txtEmergencyContactPhone" runat="server" CssClass="form-control" TextMode="Phone"></asp:TextBox>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Additional Notes</label>
                            <asp:TextBox ID="txtNotes" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" 
                                placeholder="Any additional health information or notes"></asp:TextBox>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSaveFamilyMember" runat="server" CssClass="btn btn-primary" Text="Add Family Member" OnClick="btnSaveFamilyMember_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Family Member Profile Modal -->
    <div class="modal fade" id="familyMemberModal" tabindex="-1" aria-labelledby="familyMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="familyMemberModalLabel">Family Member Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="familyMemberDetails">
                        <!-- Family member details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="familyMemberActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .family-member-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .family-member-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .profile-avatar img {
            border: 3px solid #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .health-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
        }
    </style>

    <script>
        // View family member profile
        $(document).on('click', '.view-profile', function(e) {
            e.preventDefault();
            const memberId = $(this).data('member-id');
            loadFamilyMemberProfile(memberId);
        });

        // Edit family member profile
        $(document).on('click', '.edit-profile', function(e) {
            e.preventDefault();
            const memberId = $(this).data('member-id');
            loadEditFamilyMemberForm(memberId);
        });

        // Upload prescription for family member
        $(document).on('click', '.upload-prescription', function(e) {
            e.preventDefault();
            const memberId = $(this).data('member-id');
            window.location.href = 'UploadPrescription.aspx?familyMemberId=' + memberId;
        });

        // View orders for family member
        $(document).on('click', '.view-orders', function(e) {
            e.preventDefault();
            const memberId = $(this).data('member-id');
            window.location.href = 'OrderTracking.aspx?familyMemberId=' + memberId;
        });

        function loadFamilyMemberProfile(memberId) {
            $.ajax({
                type: 'POST',
                url: 'FamilyProfiles.aspx/GetFamilyMemberProfile',
                data: JSON.stringify({ familyMemberId: memberId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#familyMemberDetails').html(response.d.html);
                        $('#familyMemberActions').html(response.d.actions);
                        $('#familyMemberModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading family member profile.');
                }
            });
        }

        function loadEditFamilyMemberForm(memberId) {
            $.ajax({
                type: 'POST',
                url: 'FamilyProfiles.aspx/GetEditFamilyMemberForm',
                data: JSON.stringify({ familyMemberId: memberId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#familyMemberDetails').html(response.d.html);
                        $('#familyMemberModalLabel').text('Edit Family Member');
                        $('#familyMemberModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading edit form.');
                }
            });
        }
    </script>
</asp:Content>
