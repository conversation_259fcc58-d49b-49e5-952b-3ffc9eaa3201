<%@ Page Title="Invoice Generation" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="InvoiceGeneration.aspx.cs" Inherits="MediEase.Pharmacist.InvoiceGeneration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-file-invoice-dollar me-2 text-primary"></i>Invoice Generation</h2>
                        <p class="text-muted">Generate and manage invoices for orders and prescriptions</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createInvoiceModal">
                            <i class="fas fa-plus me-2"></i>Create New Invoice
                        </button>
                        <button type="button" class="btn btn-info" onclick="generateBulkInvoices()">
                            <i class="fas fa-layer-group me-2"></i>Bulk Generate
                        </button>
                        <button type="button" class="btn btn-warning" onclick="exportInvoices()">
                            <i class="fas fa-download me-2"></i>Export All
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalInvoices" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblPaidInvoices" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Paid Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblPendingInvoices" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Pending Payment</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>$<asp:Label ID="lblTotalRevenue" runat="server" Text="0.00"></asp:Label></h4>
                        <p class="mb-0">Total Revenue</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">Status Filter</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Status</asp:ListItem>
                                    <asp:ListItem Value="Draft">Draft</asp:ListItem>
                                    <asp:ListItem Value="Sent">Sent</asp:ListItem>
                                    <asp:ListItem Value="Paid">Paid</asp:ListItem>
                                    <asp:ListItem Value="Overdue">Overdue</asp:ListItem>
                                    <asp:ListItem Value="Cancelled">Cancelled</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Date Range</label>
                                <asp:DropDownList ID="ddlDateRange" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlDateRange_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Dates</asp:ListItem>
                                    <asp:ListItem Value="today">Today</asp:ListItem>
                                    <asp:ListItem Value="week">This Week</asp:ListItem>
                                    <asp:ListItem Value="month">This Month</asp:ListItem>
                                    <asp:ListItem Value="quarter">This Quarter</asp:ListItem>
                                    <asp:ListItem Value="year">This Year</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Customer Type</label>
                                <asp:DropDownList ID="ddlCustomerType" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlCustomerType_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Customers</asp:ListItem>
                                    <asp:ListItem Value="Individual">Individual</asp:ListItem>
                                    <asp:ListItem Value="Insurance">Insurance</asp:ListItem>
                                    <asp:ListItem Value="Corporate">Corporate</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search by invoice number, customer name..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <asp:Button ID="btnRefresh" runat="server" CssClass="btn btn-outline-secondary" Text="Refresh" OnClick="btnRefresh_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoices List -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Invoices</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="toggleView('table')">
                                <i class="fas fa-table"></i> Table View
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="toggleView('card')">
                                <i class="fas fa-th"></i> Card View
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Table View -->
                        <div id="tableView">
                            <div class="table-responsive">
                                <asp:GridView ID="gvInvoices" runat="server" CssClass="table table-hover" 
                                    AutoGenerateColumns="false" EmptyDataText="No invoices found." OnRowCommand="gvInvoices_RowCommand">
                                    <Columns>
                                        <asp:TemplateField HeaderText="Invoice Info">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("InvoiceNumber") %></strong><br>
                                                    <small class="text-muted">Created: <%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd, yyyy") %></small><br>
                                                    <small class="text-muted">Due: <%# Convert.ToDateTime(Eval("DueDate")).ToString("MMM dd, yyyy") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Customer">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("CustomerName") %></strong><br>
                                                    <small class="text-muted"><%# Eval("CustomerEmail") %></small><br>
                                                    <small class="text-muted"><%# Eval("CustomerPhone") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Order Details">
                                            <ItemTemplate>
                                                <div>
                                                    <strong>Order #<%# Eval("OrderNumber") %></strong><br>
                                                    <small class="text-muted">Items: <%# Eval("ItemCount") %></small><br>
                                                    <small class="text-muted">Type: <%# Eval("OrderType") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Amount">
                                            <ItemTemplate>
                                                <div class="text-end">
                                                    <h6 class="mb-1">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></h6>
                                                    <small class="text-muted">Subtotal: $<%# String.Format("{0:F2}", Eval("SubtotalAmount")) %></small><br>
                                                    <small class="text-muted">Tax: $<%# String.Format("{0:F2}", Eval("TaxAmount")) %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Status">
                                            <ItemTemplate>
                                                <div class="text-center">
                                                    <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %> fs-6">
                                                        <%# Eval("Status") %>
                                                    </span>
                                                    <%# IsOverdue(Convert.ToDateTime(Eval("DueDate")), Eval("Status").ToString()) ? 
                                                        "<br><small class='text-danger'><i class='fas fa-exclamation-triangle'></i> Overdue</small>" : "" %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Actions">
                                            <ItemTemplate>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info btn-sm view-invoice" 
                                                        data-invoice-id='<%# Eval("InvoiceId") %>'>
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm edit-invoice mt-1" 
                                                        data-invoice-id='<%# Eval("InvoiceId") %>'>
                                                        <i class="fas fa-edit me-1"></i>Edit
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm download-pdf mt-1" 
                                                        data-invoice-id='<%# Eval("InvoiceId") %>'>
                                                        <i class="fas fa-download me-1"></i>PDF
                                                    </button>
                                                    <asp:LinkButton runat="server" CssClass="btn btn-outline-warning btn-sm mt-1" 
                                                        CommandName="SendEmail" CommandArgument='<%# Eval("InvoiceId") %>'>
                                                        <i class="fas fa-envelope me-1"></i>Email
                                                    </asp:LinkButton>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>

                        <!-- Card View (Hidden by default) -->
                        <div id="cardView" class="row g-4" style="display: none;">
                            <asp:Repeater ID="rptInvoicesCard" runat="server">
                                <ItemTemplate>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card invoice-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <strong><%# Eval("InvoiceNumber") %></strong>
                                                <span class="badge bg-<%# GetStatusColor(Eval("Status").ToString()) %>">
                                                    <%# Eval("Status") %>
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <div class="customer-info mb-3">
                                                    <h6 class="text-primary"><%# Eval("CustomerName") %></h6>
                                                    <small class="text-muted"><%# Eval("CustomerEmail") %></small>
                                                </div>
                                                
                                                <div class="order-info mb-3">
                                                    <div class="row text-center">
                                                        <div class="col-6">
                                                            <small class="text-muted">Order</small>
                                                            <div class="fw-bold">#<%# Eval("OrderNumber") %></div>
                                                        </div>
                                                        <div class="col-6">
                                                            <small class="text-muted">Items</small>
                                                            <div class="fw-bold"><%# Eval("ItemCount") %></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="amount-info mb-3 text-center">
                                                    <h4 class="text-success">$<%# String.Format("{0:F2}", Eval("TotalAmount")) %></h4>
                                                    <small class="text-muted">
                                                        Subtotal: $<%# String.Format("{0:F2}", Eval("SubtotalAmount")) %> + 
                                                        Tax: $<%# String.Format("{0:F2}", Eval("TaxAmount")) %>
                                                    </small>
                                                </div>
                                                
                                                <div class="dates-info">
                                                    <small class="text-muted">
                                                        Created: <%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd, yyyy") %><br>
                                                        Due: <%# Convert.ToDateTime(Eval("DueDate")).ToString("MMM dd, yyyy") %>
                                                        <%# IsOverdue(Convert.ToDateTime(Eval("DueDate")), Eval("Status").ToString()) ? 
                                                            "<span class='text-danger'> (Overdue)</span>" : "" %>
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-grid gap-2">
                                                    <div class="btn-group">
                                                        <button class="btn btn-outline-info btn-sm view-invoice" data-invoice-id='<%# Eval("InvoiceId") %>'>
                                                            <i class="fas fa-eye me-1"></i>View
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm edit-invoice" data-invoice-id='<%# Eval("InvoiceId") %>'>
                                                            <i class="fas fa-edit me-1"></i>Edit
                                                        </button>
                                                    </div>
                                                    <div class="btn-group">
                                                        <button class="btn btn-outline-success btn-sm download-pdf" data-invoice-id='<%# Eval("InvoiceId") %>'>
                                                            <i class="fas fa-download me-1"></i>PDF
                                                        </button>
                                                        <button class="btn btn-outline-warning btn-sm send-email" data-invoice-id='<%# Eval("InvoiceId") %>'>
                                                            <i class="fas fa-envelope me-1"></i>Email
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Invoices Message -->
        <asp:Panel ID="pnlNoInvoices" runat="server" Visible="false">
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h4>No invoices found</h4>
                <p class="text-muted">No invoices match your current filter criteria.</p>
            </div>
        </asp:Panel>
    </div>

    <!-- Create Invoice Modal -->
    <div class="modal fade" id="createInvoiceModal" tabindex="-1" aria-labelledby="createInvoiceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createInvoiceModalLabel">Create New Invoice</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="createInvoiceForm">
                        <!-- Create invoice form will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Details Modal -->
    <div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="invoiceModalLabel">Invoice Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="invoiceDetails">
                        <!-- Invoice details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="invoiceActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .invoice-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .invoice-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
    </style>

    <script>
        function toggleView(viewType) {
            const tableView = document.getElementById('tableView');
            const cardView = document.getElementById('cardView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'table') {
                tableView.style.display = 'block';
                cardView.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                tableView.style.display = 'none';
                cardView.style.display = 'flex';
                buttons[1].classList.add('active');
            }
        }

        // View invoice details
        $(document).on('click', '.view-invoice', function() {
            const invoiceId = $(this).data('invoice-id');
            loadInvoiceDetails(invoiceId);
        });

        // Edit invoice
        $(document).on('click', '.edit-invoice', function() {
            const invoiceId = $(this).data('invoice-id');
            loadEditInvoiceForm(invoiceId);
        });

        // Download PDF
        $(document).on('click', '.download-pdf', function() {
            const invoiceId = $(this).data('invoice-id');
            downloadInvoicePDF(invoiceId);
        });

        // Send email
        $(document).on('click', '.send-email', function() {
            const invoiceId = $(this).data('invoice-id');
            sendInvoiceEmail(invoiceId);
        });

        function loadInvoiceDetails(invoiceId) {
            $.ajax({
                type: 'POST',
                url: 'InvoiceGeneration.aspx/GetInvoiceDetails',
                data: JSON.stringify({ invoiceId: invoiceId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#invoiceDetails').html(response.d.html);
                        $('#invoiceActions').html(response.d.actions);
                        $('#invoiceModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading invoice details.');
                }
            });
        }

        function downloadInvoicePDF(invoiceId) {
            window.open('InvoicePDF.aspx?id=' + invoiceId, '_blank');
        }

        function sendInvoiceEmail(invoiceId) {
            if (confirm('Send invoice via email to customer?')) {
                $.ajax({
                    type: 'POST',
                    url: 'InvoiceGeneration.aspx/SendInvoiceEmail',
                    data: JSON.stringify({ invoiceId: invoiceId }),
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    success: function(response) {
                        if (response.d.success) {
                            showSuccessMessage('Invoice sent successfully!');
                        } else {
                            showErrorMessage(response.d.message);
                        }
                    },
                    error: function() {
                        showErrorMessage('Error sending invoice email.');
                    }
                });
            }
        }

        function generateBulkInvoices() {
            if (confirm('Generate invoices for all pending orders?')) {
                $.ajax({
                    type: 'POST',
                    url: 'InvoiceGeneration.aspx/GenerateBulkInvoices',
                    data: JSON.stringify({}),
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    success: function(response) {
                        if (response.d.success) {
                            showSuccessMessage(response.d.message);
                            location.reload();
                        } else {
                            showErrorMessage(response.d.message);
                        }
                    },
                    error: function() {
                        showErrorMessage('Error generating bulk invoices.');
                    }
                });
            }
        }

        function exportInvoices() {
            window.open('ExportInvoices.aspx', '_blank');
        }
    </script>
</asp:Content>
