using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;
using Prescription = MediEase.Models.Prescription;

namespace MediEase.Customer
{
    public partial class FamilyProfiles : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsCustomer())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadFamilyMembers();
                UpdateFamilyStats();
            }
        }

        private void LoadFamilyMembers()
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var familyMembers = db.FamilyMembers
                        .Where(fm => fm.PrimaryUserId == currentUser.UserId && fm.IsActive)
                        .OrderBy(fm => fm.Relationship)
                        .ThenBy(fm => fm.FirstName)
                        .ToList();

                    if (familyMembers.Any())
                    {
                        rptFamilyMembers.DataSource = familyMembers;
                        rptFamilyMembers.DataBind();
                        pnlNoMembers.Visible = false;
                    }
                    else
                    {
                        rptFamilyMembers.DataSource = null;
                        rptFamilyMembers.DataBind();
                        pnlNoMembers.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading family members");
                ShowErrorMessage("Error loading family members. Please try again.");
            }
        }

        private void UpdateFamilyStats()
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var totalMembers = db.FamilyMembers.Count(fm => fm.PrimaryUserId == currentUser.UserId && fm.IsActive);
                    
                    var activePrescriptions = db.Prescriptions.Count(p => 
                        (p.PatientId == currentUser.UserId || 
                         db.FamilyMembers.Any(fm => fm.FamilyMemberId == p.PatientId && fm.PrimaryUserId == currentUser.UserId)) &&
                        p.Status == "Verified" && p.IsValid);

                    var allergiesCount = db.FamilyMembers
                        .Where(fm => fm.PrimaryUserId == currentUser.UserId && fm.IsActive && !string.IsNullOrEmpty(fm.Allergies))
                        .Count();

                    // Add user's own allergies if they exist
                    var user = db.Users.Find(currentUser.UserId);
                    if (user != null && !string.IsNullOrEmpty(user.Allergies))
                    {
                        allergiesCount++;
                    }

                    var upcomingRefills = 0; // This would be calculated based on prescription refill dates

                    lblTotalMembers.Text = totalMembers.ToString();
                    lblActivePrescriptions.Text = activePrescriptions.ToString();
                    lblAllergies.Text = allergiesCount.ToString();
                    lblUpcomingRefills.Text = upcomingRefills.ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating family stats");
            }
        }

        protected void btnSaveFamilyMember_Click(object sender, EventArgs e)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                // Validate required fields
                if (string.IsNullOrWhiteSpace(txtFirstName.Text) ||
                    string.IsNullOrWhiteSpace(txtLastName.Text) ||
                    string.IsNullOrWhiteSpace(ddlRelationship.SelectedValue) ||
                    string.IsNullOrWhiteSpace(txtDateOfBirth.Text))
                {
                    ShowErrorMessage("Please fill in all required fields.");
                    return;
                }

                if (!DateTime.TryParse(txtDateOfBirth.Text, out DateTime dateOfBirth))
                {
                    ShowErrorMessage("Please enter a valid date of birth.");
                    return;
                }

                // Check if date of birth is not in the future
                if (dateOfBirth > DateTime.Today)
                {
                    ShowErrorMessage("Date of birth cannot be in the future.");
                    return;
                }

                using (var db = new MediEaseContext())
                {
                    var familyMember = new FamilyMember
                    {
                        PrimaryUserId = currentUser.UserId,
                        FirstName = txtFirstName.Text.Trim(),
                        LastName = txtLastName.Text.Trim(),
                        DateOfBirth = dateOfBirth,
                        Gender = ddlGender.SelectedValue,
                        Relationship = ddlRelationship.SelectedValue,
                        BloodType = ddlBloodType.SelectedValue,
                        Allergies = txtAllergies.Text.Trim(),
                        CurrentMedications = txtCurrentMedications.Text.Trim(),
                        MedicalConditions = txtMedicalConditions.Text.Trim(),
                        EmergencyContactName = txtEmergencyContactName.Text.Trim(),
                        EmergencyContactPhone = txtEmergencyContactPhone.Text.Trim(),
                        Notes = txtNotes.Text.Trim(),
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    };

                    db.FamilyMembers.Add(familyMember);
                    db.SaveChanges();

                    ErrorLogger.LogUserActivity($"Added family member: {familyMember.FullName}", currentUser.UserId);
                    ShowSuccessMessage($"Family member {familyMember.FullName} added successfully!");

                    // Clear form
                    ClearForm();

                    // Reload data
                    LoadFamilyMembers();
                    UpdateFamilyStats();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error saving family member");
                ShowErrorMessage("Error saving family member. Please try again.");
            }
        }

        protected void rptFamilyMembers_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            if (e.CommandName == "Delete")
            {
                if (int.TryParse(e.CommandArgument.ToString(), out int familyMemberId))
                {
                    DeleteFamilyMember(familyMemberId);
                }
            }
        }

        private void DeleteFamilyMember(int familyMemberId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var familyMember = db.FamilyMembers.FirstOrDefault(fm => 
                        fm.FamilyMemberId == familyMemberId && 
                        fm.PrimaryUserId == currentUser.UserId);

                    if (familyMember != null)
                    {
                        // Soft delete - set IsActive to false
                        familyMember.IsActive = false;
                        familyMember.ModifiedDate = DateTime.Now;

                        db.SaveChanges();

                        ErrorLogger.LogUserActivity($"Removed family member: {familyMember.FullName}", currentUser.UserId);
                        ShowSuccessMessage($"Family member {familyMember.FullName} removed successfully!");

                        LoadFamilyMembers();
                        UpdateFamilyStats();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error deleting family member");
                ShowErrorMessage("Error removing family member. Please try again.");
            }
        }

        [WebMethod]
        public static object GetFamilyMemberProfile(int familyMemberId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || !SecurityHelper.IsCustomer())
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var familyMember = db.FamilyMembers.FirstOrDefault(fm => 
                        fm.FamilyMemberId == familyMemberId && 
                        fm.PrimaryUserId == currentUser.UserId && 
                        fm.IsActive);

                    if (familyMember == null)
                    {
                        return new { success = false, message = "Family member not found" };
                    }

                    var html = GenerateFamilyMemberProfileHtml(familyMember, db);
                    var actions = GenerateFamilyMemberActionsHtml(familyMember);
                    return new { success = true, html = html, actions = actions };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting family member profile");
                return new { success = false, message = "Error loading family member profile" };
            }
        }

        [WebMethod]
        public static object GetEditFamilyMemberForm(int familyMemberId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || !SecurityHelper.IsCustomer())
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var familyMember = db.FamilyMembers.FirstOrDefault(fm => 
                        fm.FamilyMemberId == familyMemberId && 
                        fm.PrimaryUserId == currentUser.UserId && 
                        fm.IsActive);

                    if (familyMember == null)
                    {
                        return new { success = false, message = "Family member not found" };
                    }

                    var html = GenerateEditFamilyMemberFormHtml(familyMember);
                    return new { success = true, html = html };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting edit family member form");
                return new { success = false, message = "Error loading edit form" };
            }
        }

        private static string GenerateFamilyMemberProfileHtml(FamilyMember member, MediEaseContext db)
        {
            // Get family member statistics
            var prescriptionCount = db.Prescriptions.Count(p => p.PatientId == member.FamilyMemberId);
            var orderCount = db.Orders.Count(o => o.CustomerId == member.FamilyMemberId);

            return $@"
                <div class='row'>
                    <div class='col-md-4 text-center'>
                        <img src='{GetProfileImageStatic(member.ProfileImage)}' alt='Profile' class='rounded-circle mb-3' width='120' height='120' />
                        <h4>{member.FullName}</h4>
                        <span class='badge bg-{GetRelationshipColorStatic(member.Relationship)} fs-6'>{member.Relationship}</span>
                        <p class='text-muted mt-2'>{member.Age} years old</p>
                    </div>
                    <div class='col-md-8'>
                        <div class='row'>
                            <div class='col-md-6'>
                                <h6>Personal Information</h6>
                                <p><strong>Full Name:</strong> {member.FullName}</p>
                                <p><strong>Date of Birth:</strong> {member.DateOfBirth:MM/dd/yyyy}</p>
                                <p><strong>Age:</strong> {member.Age} years</p>
                                <p><strong>Gender:</strong> {member.Gender ?? "Not specified"}</p>
                                <p><strong>Blood Type:</strong> {member.BloodType ?? "Unknown"}</p>
                                <p><strong>Relationship:</strong> {member.Relationship}</p>
                            </div>
                            <div class='col-md-6'>
                                <h6>Emergency Contact</h6>
                                <p><strong>Name:</strong> {member.EmergencyContactName ?? "Not provided"}</p>
                                <p><strong>Phone:</strong> {member.EmergencyContactPhone ?? "Not provided"}</p>
                                
                                <h6 class='mt-3'>Statistics</h6>
                                <p><strong>Prescriptions:</strong> {prescriptionCount}</p>
                                <p><strong>Orders:</strong> {orderCount}</p>
                            </div>
                        </div>
                        <div class='row mt-3'>
                            <div class='col-12'>
                                <h6>Health Information</h6>
                                <div class='mb-3'>
                                    <strong>Allergies:</strong>
                                    <div class='mt-1 p-2 bg-light rounded'>
                                        {(string.IsNullOrEmpty(member.Allergies) ? "No known allergies" : member.Allergies)}
                                    </div>
                                </div>
                                <div class='mb-3'>
                                    <strong>Current Medications:</strong>
                                    <div class='mt-1 p-2 bg-light rounded'>
                                        {(string.IsNullOrEmpty(member.CurrentMedications) ? "No current medications" : member.CurrentMedications)}
                                    </div>
                                </div>
                                <div class='mb-3'>
                                    <strong>Medical Conditions:</strong>
                                    <div class='mt-1 p-2 bg-light rounded'>
                                        {(string.IsNullOrEmpty(member.MedicalConditions) ? "No known medical conditions" : member.MedicalConditions)}
                                    </div>
                                </div>
                                {(!string.IsNullOrEmpty(member.Notes) ? $@"
                                <div class='mb-3'>
                                    <strong>Additional Notes:</strong>
                                    <div class='mt-1 p-2 bg-light rounded'>
                                        {member.Notes}
                                    </div>
                                </div>" : "")}
                            </div>
                        </div>
                    </div>
                </div>";
        }

        private static string GenerateFamilyMemberActionsHtml(FamilyMember member)
        {
            var actions = new List<string>();

            actions.Add($"<button class='btn btn-primary' onclick='editFamilyMember({member.FamilyMemberId})'>Edit Profile</button>");
            actions.Add($"<button class='btn btn-success' onclick='uploadPrescription({member.FamilyMemberId})'>Upload Prescription</button>");
            actions.Add($"<button class='btn btn-info' onclick='viewOrders({member.FamilyMemberId})'>View Orders</button>");

            return string.Join(" ", actions);
        }

        private static string GenerateEditFamilyMemberFormHtml(FamilyMember member)
        {
            return $@"
                <form id='editFamilyMemberForm'>
                    <input type='hidden' id='editFamilyMemberId' value='{member.FamilyMemberId}' />
                    <div class='row'>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>First Name</label>
                                <input type='text' class='form-control' id='editFirstName' value='{member.FirstName}' required />
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Last Name</label>
                                <input type='text' class='form-control' id='editLastName' value='{member.LastName}' required />
                            </div>
                        </div>
                    </div>
                    <div class='row'>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Date of Birth</label>
                                <input type='date' class='form-control' id='editDateOfBirth' value='{member.DateOfBirth:yyyy-MM-dd}' required />
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mb-3'>
                                <label class='form-label'>Gender</label>
                                <select class='form-select' id='editGender'>
                                    <option value=''>Select Gender</option>
                                    <option value='Male' {(member.Gender == "Male" ? "selected" : "")}>Male</option>
                                    <option value='Female' {(member.Gender == "Female" ? "selected" : "")}>Female</option>
                                    <option value='Other' {(member.Gender == "Other" ? "selected" : "")}>Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class='mb-3'>
                        <label class='form-label'>Allergies</label>
                        <textarea class='form-control' id='editAllergies' rows='2'>{member.Allergies}</textarea>
                    </div>
                    <div class='mb-3'>
                        <label class='form-label'>Current Medications</label>
                        <textarea class='form-control' id='editCurrentMedications' rows='2'>{member.CurrentMedications}</textarea>
                    </div>
                    <div class='d-grid'>
                        <button type='button' class='btn btn-primary' onclick='saveFamilyMemberChanges()'>Save Changes</button>
                    </div>
                </form>";
        }

        // Helper methods for data binding
        protected string GetRelationshipColor(string relationship)
        {
            return GetRelationshipColorStatic(relationship);
        }

        private static string GetRelationshipColorStatic(string relationship)
        {
            switch (relationship?.ToLower())
            {
                case "spouse": return "primary";
                case "child": return "success";
                case "parent": return "info";
                case "sibling": return "warning";
                case "grandparent": return "secondary";
                case "grandchild": return "light";
                default: return "dark";
            }
        }

        protected string GetProfileImage(object profileImage)
        {
            return GetProfileImageStatic(profileImage?.ToString());
        }

        private static string GetProfileImageStatic(string profileImage)
        {
            return string.IsNullOrEmpty(profileImage) ? "/Images/default-avatar.png" : profileImage;
        }

        protected int GetAge(DateTime dateOfBirth)
        {
            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Year;
            if (dateOfBirth.Date > today.AddYears(-age)) age--;
            return age;
        }

        protected bool HasAllergies(object allergies)
        {
            return !string.IsNullOrEmpty(allergies?.ToString());
        }

        protected bool HasMedications(object medications)
        {
            return !string.IsNullOrEmpty(medications?.ToString());
        }

        private void ClearForm()
        {
            txtFirstName.Text = "";
            txtLastName.Text = "";
            txtDateOfBirth.Text = "";
            ddlGender.SelectedValue = "";
            ddlRelationship.SelectedValue = "";
            ddlBloodType.SelectedValue = "";
            txtAllergies.Text = "";
            txtCurrentMedications.Text = "";
            txtMedicalConditions.Text = "";
            txtEmergencyContactName.Text = "";
            txtEmergencyContactPhone.Text = "";
            txtNotes.Text = "";
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Family Profiles - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Manage health profiles for your family members with MediEase family management system.");
                master.AddMetaKeywords("family profiles, health management, family health, medical records, prescription management");
            }
        }
    }
}
