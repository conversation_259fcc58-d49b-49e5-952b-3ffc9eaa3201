<%@ Page Title="Medicines" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Medicines.aspx.cs" Inherits="MediEase.Medicines" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-pills me-2 text-primary"></i>Medicines Catalog
                        </h2>
                        <p class="text-muted">Browse our comprehensive collection of medicines and health products</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                            <i class="fas fa-filter me-1"></i>Filters
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-sort me-1"></i>Sort By
                            </button>
                            <ul class="dropdown-menu">
                                <li><asp:LinkButton ID="lnkSortName" runat="server" CssClass="dropdown-item" OnClick="lnkSort_Click" CommandArgument="name">Name A-Z</asp:LinkButton></li>
                                <li><asp:LinkButton ID="lnkSortPrice" runat="server" CssClass="dropdown-item" OnClick="lnkSort_Click" CommandArgument="price">Price Low-High</asp:LinkButton></li>
                                <li><asp:LinkButton ID="lnkSortRating" runat="server" CssClass="dropdown-item" OnClick="lnkSort_Click" CommandArgument="rating">Highest Rated</asp:LinkButton></li>
                                <li><asp:LinkButton ID="lnkSortPopular" runat="server" CssClass="dropdown-item" OnClick="lnkSort_Click" CommandArgument="popular">Most Popular</asp:LinkButton></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter Bar -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <asp:TextBox ID="txtSearchMedicine" runat="server" 
                                        CssClass="form-control" 
                                        placeholder="Search medicines by name, generic name, or symptoms..." />
                                    <asp:Button ID="btnSearchMedicine" runat="server" 
                                        CssClass="btn btn-primary" 
                                        Text="Search" 
                                        OnClick="btnSearchMedicine_Click" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <asp:DropDownList ID="ddlCategory" runat="server" 
                                    CssClass="form-select" 
                                    AutoPostBack="true" 
                                    OnSelectedIndexChanged="ddlCategory_SelectedIndexChanged">
                                    <asp:ListItem Text="All Categories" Value="" />
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <asp:DropDownList ID="ddlBrand" runat="server" 
                                    CssClass="form-select" 
                                    AutoPostBack="true" 
                                    OnSelectedIndexChanged="ddlBrand_SelectedIndexChanged">
                                    <asp:ListItem Text="All Brands" Value="" />
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <asp:Label ID="lblResultsCount" runat="server" CssClass="text-muted" />
                    </div>
                    <div>
                        <asp:Label ID="lblCurrentSort" runat="server" CssClass="small text-muted" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Medicines Grid -->
        <div class="row g-4 mb-4">
            <asp:Repeater ID="rptMedicines" runat="server" OnItemCommand="rptMedicines_ItemCommand">
                <ItemTemplate>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="card medicine-card h-100 border-0 shadow-sm">
                            <div class="position-relative">
                                <img src='<%# ResolveUrl("~/Images/Products/" + (string.IsNullOrEmpty(Eval("ImagePath").ToString()) ? "no-image.jpg" : Eval("ImagePath"))) %>' 
                                     alt='<%# Eval("Name") %>' 
                                     class="card-img-top medicine-image" 
                                     onerror="this.src='<%# ResolveUrl("~/Images/no-image.jpg") %>'" />
                                
                                <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                    "<span class=\"discount-badge\">" + Eval("DiscountPercentage") + "% OFF</span>" : "" %>
                                
                                <%# Convert.ToBoolean(Eval("PrescriptionRequired")) ? 
                                    "<span class=\"badge bg-warning position-absolute top-0 start-0 m-2\"><i class=\"fas fa-prescription me-1\"></i>Rx</span>" : "" %>
                            </div>
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title mb-1"><%# Eval("Name") %></h6>
                                <p class="card-text text-muted small mb-1"><%# Eval("GenericName") %></p>
                                <p class="card-text small mb-2">
                                    <strong><%# Eval("Strength") %></strong> - <%# Eval("PackSize") %> <%# Eval("Unit") %>
                                </p>
                                <p class="card-text small text-muted mb-2"><%# Eval("Brand") %></p>
                                
                                <!-- Rating -->
                                <div class="mb-2">
                                    <%# GenerateStarRating(Convert.ToDecimal(Eval("AverageRating"))) %>
                                    <small class="text-muted ms-1">(<%# Eval("ReviewCount") %>)</small>
                                </div>
                                
                                <!-- Price and Stock -->
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="price-tag">$<%# String.Format("{0:F2}", Eval("FinalPrice")) %></span>
                                            <%# Convert.ToDecimal(Eval("DiscountAmount")) > 0 || Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                                "<small class=\"original-price ms-1\">$" + String.Format("{0:F2}", Eval("Price")) + "</small>" : "" %>
                                        </div>
                                        <span class="stock-status stock-<%# GetStockStatusClass(Convert.ToInt32(Eval("StockQuantity"))) %>">
                                            <%# GetStockStatusText(Convert.ToInt32(Eval("StockQuantity"))) %>
                                        </span>
                                    </div>
                                    
                                    <!-- Action Buttons -->
                                    <div class="d-grid gap-2">
                                        <%# Convert.ToInt32(Eval("StockQuantity")) > 0 ?
                                            "<button class=\"btn btn-primary btn-sm add-to-cart\" data-medicine-id=\"" + Eval("MedicineId") + "\"><i class=\"fas fa-cart-plus me-1\"></i>Add to Cart</button>" :
                                            "<button class=\"btn btn-secondary btn-sm\" disabled>Out of Stock</button>" %>
                                        
                                        <asp:LinkButton ID="lnkViewDetails" runat="server" 
                                            CssClass="btn btn-outline-primary btn-sm" 
                                            CommandName="ViewDetails" 
                                            CommandArgument='<%# Eval("MedicineId") %>'>
                                            <i class="fas fa-info-circle me-1"></i>View Details
                                        </asp:LinkButton>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ItemTemplate>
            </asp:Repeater>
        </div>

        <!-- No Results Message -->
        <asp:Panel ID="pnlNoResults" runat="server" Visible="false" CssClass="text-center py-5">
            <div class="card border-0">
                <div class="card-body">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No medicines found</h4>
                    <p class="text-muted">Try adjusting your search criteria or browse all categories.</p>
                    <asp:Button ID="btnClearFilters" runat="server" 
                        CssClass="btn btn-primary" 
                        Text="Clear All Filters" 
                        OnClick="btnClearFilters_Click" />
                </div>
            </div>
        </asp:Panel>

        <!-- Pagination -->
        <div class="row">
            <div class="col-12">
                <nav aria-label="Medicine pagination">
                    <ul class="pagination justify-content-center">
                        <asp:Repeater ID="rptPagination" runat="server" OnItemCommand="rptPagination_ItemCommand">
                            <ItemTemplate>
                                <li class="page-item <%# Eval("CssClass") %>">
                                    <asp:LinkButton ID="lnkPage" runat="server" 
                                        CssClass="page-link" 
                                        CommandName="Page" 
                                        CommandArgument='<%# Eval("PageNumber") %>'
                                        Text='<%# Eval("Text") %>' />
                                </li>
                            </ItemTemplate>
                        </asp:Repeater>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Filter Modal -->
    <div class="modal fade" id="filterModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Advanced Filters</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Price Range</label>
                            <div class="row">
                                <div class="col-6">
                                    <asp:TextBox ID="txtMinPrice" runat="server" 
                                        CssClass="form-control" 
                                        placeholder="Min Price" 
                                        TextMode="Number" />
                                </div>
                                <div class="col-6">
                                    <asp:TextBox ID="txtMaxPrice" runat="server" 
                                        CssClass="form-control" 
                                        placeholder="Max Price" 
                                        TextMode="Number" />
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <asp:CheckBox ID="chkInStockOnly" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkInStockOnly.ClientID %>">
                                    In Stock Only
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <asp:CheckBox ID="chkPrescriptionRequired" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkPrescriptionRequired.ClientID %>">
                                    Prescription Required
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <asp:CheckBox ID="chkOnSaleOnly" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkOnSaleOnly.ClientID %>">
                                    On Sale Only
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnApplyFilters" runat="server" 
                        CssClass="btn btn-primary" 
                        Text="Apply Filters" 
                        OnClick="btnApplyFilters_Click" />
                </div>
            </div>
        </div>
    </div>
</asp:Content>

<asp:Content ID="ScriptContent" ContentPlaceHolderID="ScriptContent" runat="server">
    <script>
        $(document).ready(function() {
            // Initialize medicines page
            initializeMedicinesPage();
        });

        function initializeMedicinesPage() {
            // Add to cart functionality
            $('.add-to-cart').on('click', function(e) {
                e.preventDefault();
                const medicineId = $(this).data('medicine-id');
                addToCart(medicineId, 1);
            });

            // Medicine card hover effects
            $('.medicine-card').hover(
                function() { $(this).addClass('shadow-lg'); },
                function() { $(this).removeClass('shadow-lg'); }
            );

            // Search on Enter key
            $('#<%= txtSearchMedicine.ClientID %>').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#<%= btnSearchMedicine.ClientID %>').click();
                }
            });

            // Auto-dismiss filter modal after applying filters
            $('#<%= btnApplyFilters.ClientID %>').on('click', function() {
                setTimeout(function() {
                    $('#filterModal').modal('hide');
                }, 500);
            });
        }

        function addToCart(medicineId, quantity) {
            $.ajax({
                type: 'POST',
                url: 'Medicines.aspx/AddToCart',
                data: JSON.stringify({ medicineId: medicineId, quantity: quantity }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        showNotification('Medicine added to cart successfully!', 'success');
                        updateCartCount();
                    } else {
                        showNotification(response.d.message || 'Error adding to cart', 'error');
                    }
                },
                error: function() {
                    showNotification('Error adding to cart. Please try again.', 'error');
                }
            });
        }

        function updateCartCount() {
            $.ajax({
                type: 'POST',
                url: 'Medicines.aspx/GetCartCount',
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    $('#cartCount').text(response.d);
                }
            });
        }

        function showNotification(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const notification = `<div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                style="top: 80px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>`;

            $('body').append(notification);

            setTimeout(function() {
                $('.alert').fadeOut();
            }, 3000);
        }
    </script>
</asp:Content>
