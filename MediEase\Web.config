﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	</configSections>
	<connectionStrings>
		<add name="MediEaseConnectionString"
			 connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MediEase.mdf;Integrated Security=True;Connect Timeout=30"
			 providerName="System.Data.SqlClient" />
	</connectionStrings>
	<appSettings>
		<add key="OpenRouterApiKey" value="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" />
		<add key="OpenRouterApiUrl" value="https://openrouter.ai/api/v1/chat/completions" />
		<add key="OpenRouterModel" value="deepseek/deepseek-r1-0528-qwen3-8b:free" />
		<add key="ApplicationName" value="MediEase - Smart Pharmacy Management System" />
		<add key="SessionTimeout" value="30" />
		<add key="MaxFileUploadSize" value="5242880" />
		<add key="PrescriptionUploadPath" value="~/Uploads/Prescriptions/" />
		<add key="ProductImagePath" value="~/Images/Products/" />
		<add key="vs:EnableBrowserLink" value="false" />
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
	</appSettings>
	<system.web>
		<compilation debug="true" targetFramework="4.8" />
		<httpRuntime targetFramework="4.8" maxRequestLength="5120" executionTimeout="300" />
		<authentication mode="Forms">
			<forms loginUrl="~/Login.aspx" defaultUrl="~/Default.aspx" timeout="30" slidingExpiration="true" cookieless="UseCookies" requireSSL="false" />
		</authentication>
		<authorization>
			<allow users="*" />
		</authorization>
		<sessionState mode="InProc" timeout="30" cookieless="UseCookies" regenerateExpiredSessionId="true" />
		<customErrors mode="RemoteOnly" defaultRedirect="~/Error.aspx">
			<error statusCode="404" redirect="~/Error404.aspx" />
			<error statusCode="500" redirect="~/Error500.aspx" />
		</customErrors>
		<pages enableViewState="true" enableSessionState="true" validateRequest="true" enableEventValidation="true" viewStateEncryptionMode="Auto" />
		<httpModules>
			<add name="ErrorHandlingModule" type="MediEase.Utilities.ErrorHandlingModule" />
		</httpModules>
	</system.web>
	<location path="Admin">
		<system.web>
			<authorization>
				<allow roles="Admin" />
				<deny users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="Pharmacist">
		<system.web>
			<authorization>
				<allow roles="Admin,Pharmacist" />
				<deny users="*" />
			</authorization>
		</system.web>
	</location>
	<location path="Customer">
		<system.web>
			<authorization>
				<allow roles="Admin,Customer" />
				<deny users="*" />
			</authorization>
		</system.web>
	</location>
	<system.webServer>
		<defaultDocument>
			<files>
				<clear />
				<add value="Default.aspx" />
			</files>
		</defaultDocument>
		<httpProtocol>
			<customHeaders>
				<add name="X-Content-Type-Options" value="nosniff" />
				<add name="X-Frame-Options" value="DENY" />
				<add name="X-XSS-Protection" value="1; mode=block" />
				<add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
			</customHeaders>
		</httpProtocol>
		<validation validateIntegratedModeConfiguration="false" />
		<modules>
			<add name="ErrorHandlingModule" type="MediEase.Utilities.ErrorHandlingModule" />
		</modules>
	</system.webServer>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
			<parameters>
				<parameter value="mssqllocaldb" />
			</parameters>
		</defaultConnectionFactory>
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
		</providers>
	</entityFramework>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>