using System;
using System.IO;
using System.Web;

namespace MediEase.Utilities
{
    public static class ErrorLogger
    {
        private static readonly string LogDirectory = HttpContext.Current?.Server.MapPath("~/App_Data/Logs") ?? 
                                                     Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "App_Data", "Logs");

        static ErrorLogger()
        {
            // Ensure log directory exists
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }

        public static void LogError(Exception ex, string additionalInfo = "")
        {
            try
            {
                var logFileName = $"Error_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(LogDirectory, logFileName);

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {additionalInfo}\n" +
                              $"Exception: {ex.Message}\n" +
                              $"Stack Trace: {ex.StackTrace}\n" +
                              $"Inner Exception: {ex.InnerException?.Message}\n" +
                              new string('-', 80) + "\n";

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Fail silently if logging fails
            }
        }

        public static void LogInfo(string message, string category = "INFO")
        {
            try
            {
                var logFileName = $"Info_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(LogDirectory, logFileName);

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {category}: {message}\n";

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Fail silently if logging fails
            }
        }

        public static void LogWarning(string message, string additionalInfo = "")
        {
            try
            {
                var logFileName = $"Warning_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(LogDirectory, logFileName);

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] WARNING: {message}\n" +
                              $"Additional Info: {additionalInfo}\n" +
                              new string('-', 80) + "\n";

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Fail silently if logging fails
            }
        }

        public static void LogDebug(string message, string category = "DEBUG")
        {
            try
            {
                var logFileName = $"Debug_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(LogDirectory, logFileName);

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {category}: {message}\n";

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Fail silently if logging fails
            }
        }

        public static string GetLogContents(string logType = "Error", DateTime? date = null)
        {
            try
            {
                var logDate = date ?? DateTime.Now;
                var logFileName = $"{logType}_{logDate:yyyyMMdd}.log";
                var logFilePath = Path.Combine(LogDirectory, logFileName);

                if (File.Exists(logFilePath))
                {
                    return File.ReadAllText(logFilePath);
                }

                return "No log entries found for the specified date.";
            }
            catch (Exception ex)
            {
                return $"Error reading log file: {ex.Message}";
            }
        }

        public static void LogUserActivity(string activity, int? userId = null)
        {
            try
            {
                var logFileName = $"UserActivity_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(LogDirectory, logFileName);

                var userInfo = userId.HasValue ? $"User ID: {userId}" : "Anonymous User";
                var ipAddress = SecurityHelper.GetClientIPAddress();
                var userAgent = SecurityHelper.GetUserAgent();

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ACTIVITY: {activity}\n" +
                              $"{userInfo}\n" +
                              $"IP Address: {ipAddress}\n" +
                              $"User Agent: {userAgent}\n" +
                              new string('-', 80) + "\n";

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Fail silently if logging fails
            }
        }

        public static void ClearLogs(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var logFiles = Directory.GetFiles(LogDirectory, "*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch
            {
                // Fail silently if cleanup fails
            }
        }
    }
}
