using System;
using System.Configuration;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase.Admin
{
    public partial class SystemConfiguration : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is admin
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null || currentUser.Role != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadSystemSettings();
            }
        }

        private void LoadSystemSettings()
        {
            try
            {
                // Load current configuration values
                LoadGeneralSettings();
                LoadSecuritySettings();
                LoadIntegrationSettings();
                LoadNotificationSettings();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading system configuration");
                ShowErrorMessage("Error loading system settings. Please try again.");
            }
        }

        private void LoadGeneralSettings()
        {
            // Load general system settings from web.config or database
            // Implementation would load actual configuration values
        }

        private void LoadSecuritySettings()
        {
            // Load security configuration
            // Implementation would load security settings
        }

        private void LoadIntegrationSettings()
        {
            // Load API and integration settings
            // Implementation would load integration configurations
        }

        private void LoadNotificationSettings()
        {
            // Load notification preferences
            // Implementation would load notification settings
        }

        protected void btnSaveAll_Click(object sender, EventArgs e)
        {
            try
            {
                SaveAllSettings();
                ShowSuccessMessage("System configuration saved successfully.");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error saving system configuration");
                ShowErrorMessage("Error saving configuration. Please try again.");
            }
        }

        protected void btnResetDefaults_Click(object sender, EventArgs e)
        {
            try
            {
                ResetToDefaults();
                LoadSystemSettings();
                ShowSuccessMessage("System configuration reset to defaults.");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error resetting system configuration");
                ShowErrorMessage("Error resetting configuration. Please try again.");
            }
        }

        private void SaveAllSettings()
        {
            // Save all configuration changes
            // Implementation would save to web.config or database
        }

        private void ResetToDefaults()
        {
            // Reset all settings to default values
            // Implementation would restore default configurations
        }

        [WebMethod]
        public static object TestDatabaseConnection()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var count = db.Users.ToList().Count;
                    return new { success = true, message = $"Database connection successful. {count} users found." };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Database connection test failed");
                return new { success = false, message = ex.Message };
            }
        }

        [WebMethod]
        public static object TestAiConnection()
        {
            try
            {
                var response = AIHelper.GetChatbotResponseAsync("Test connection").Result;
                var success = !string.IsNullOrEmpty(response);
                return new { success = success, message = success ? "AI connection successful" : "AI connection failed" };
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "AI connection test failed");
                return new { success = false, message = ex.Message };
            }
        }

        [WebMethod]
        public static object TestEmailService()
        {
            try
            {
                // Test email service configuration
                // Implementation would test SMTP settings
                return new { success = true, message = "Email service configuration is valid" };
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Email service test failed");
                return new { success = false, message = ex.Message };
            }
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }
    }
}
