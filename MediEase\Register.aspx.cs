using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.Models;

namespace MediEase
{
    public partial class Register : Page
    {
        // Database connection string
        private string ConnectionString
        {
            get
            {
                string connStr = ConfigurationManager.ConnectionStrings["MediEaseConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(connStr))
                {
                    throw new InvalidOperationException("Database connection string 'MediEaseConnectionString' is not configured in Web.config.");
                }
                return connStr;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (GetCurrentUser() != null)
                {
                    Response.Redirect("~/Default.aspx");
                    return;
                }

                SetPageMetadata();
            }
        }

        private void SetPageMetadata()
        {
            Page.Title = "Register - MediEase Pharmacy";

            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Create your MediEase account to access pharmacy services, order medicines online, and manage your health records securely.");
                master.AddMetaKeywords("register, create account, pharmacy registration, MediEase signup, online pharmacy account");
            }
        }

        // Self-contained security methods
        private string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
        }

        private bool IsValidPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password) || password.Length < 8)
                return false;

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }

        private string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return HttpUtility.HtmlEncode(input.Trim());
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Remove all non-digit characters
            var digits = new string(phoneNumber.Where(char.IsDigit).ToArray());

            // Check if it's a valid length (10-15 digits)
            return digits.Length >= 10 && digits.Length <= 15;
        }

        private string GenerateEmailVerificationToken()
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                var bytes = new byte[32];
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
            }
        }

        // Self-contained database methods
        private bool EmailExists(string email)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Email = @Email";
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);
                        int count = (int)command.ExecuteScalar();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error checking email existence: {email}");
                throw;
            }
        }

        private User GetCurrentUser()
        {
            if (!HttpContext.Current.User.Identity.IsAuthenticated)
                return null;

            try
            {
                var ticket = ((FormsIdentity)HttpContext.Current.User.Identity).Ticket;
                var userData = ticket.UserData.Split('|');

                if (userData.Length >= 3)
                {
                    return new User
                    {
                        UserId = int.Parse(userData[0]),
                        Role = userData[1],
                        FirstName = userData[2],
                        Email = ticket.Name
                    };
                }
            }
            catch
            {
                // Return null if unable to parse user data
            }

            return null;
        }

        protected void btnRegister_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                // Validate terms acceptance
                if (!chkTerms.Checked)
                {
                    ShowErrorMessage("You must agree to the terms and conditions to create an account.");
                    return;
                }

                // Validate password strength
                if (!IsValidPassword(txtPassword.Text))
                {
                    ShowErrorMessage("Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.");
                    return;
                }

                // Check if email already exists
                if (EmailExists(txtEmail.Text.Trim().ToLower()))
                {
                    ShowErrorMessage("An account with this email address already exists. Please use a different email or try logging in.");
                    return;
                }

                // Create new user
                var newUser = new User
                {
                    FirstName = SanitizeInput(txtFirstName.Text.Trim()),
                    LastName = SanitizeInput(txtLastName.Text.Trim()),
                    Email = txtEmail.Text.Trim().ToLower(),
                    PasswordHash = HashPassword(txtPassword.Text),
                    PhoneNumber = SanitizeInput(txtPhoneNumber.Text.Trim()),
                    Address = SanitizeInput(txtAddress.Text.Trim()),
                    City = SanitizeInput(txtCity.Text.Trim()),
                    PostalCode = SanitizeInput(txtPostalCode.Text.Trim()),
                    Country = SanitizeInput(txtCountry.Text.Trim()),
                    Role = "Customer", // Default role for registration
                    IsActive = true,
                    IsEmailVerified = false, // Will be verified via email
                    IsPhoneVerified = false,
                    CreatedDate = DateTime.Now,
                    EmailVerificationToken = GenerateEmailVerificationToken(),
                    NotificationPreferences = chkNewsletter.Checked ? "email,sms" : "sms"
                };

                // Set optional fields
                if (!string.IsNullOrEmpty(txtDateOfBirth.Text))
                {
                    if (DateTime.TryParse(txtDateOfBirth.Text, out DateTime dob))
                    {
                        newUser.DateOfBirth = dob;
                    }
                }

                if (!string.IsNullOrEmpty(ddlGender.SelectedValue))
                {
                    newUser.Gender = ddlGender.SelectedValue;
                }

                // Validate phone number format
                if (!IsValidPhoneNumber(newUser.PhoneNumber))
                {
                    ShowErrorMessage("Please enter a valid phone number.");
                    return;
                }

                // Save user to database
                var newUserId = InsertUser(newUser);
                newUser.UserId = newUserId;

                // Log user registration
                LogUserActivity($"New user registered: {newUser.Email}", newUser.UserId);

                // Send welcome email (optional - implement email service)
                try
                {
                    SendWelcomeEmail(newUser);
                }
                catch (Exception ex)
                {
                    LogError(ex, "Error sending welcome email");
                    // Don't fail registration if email fails
                }

                // Create initial loyalty points
                try
                {
                    CreateWelcomeLoyaltyPoints(newUser.UserId);
                }
                catch (Exception ex)
                {
                    LogError(ex, "Error creating welcome loyalty points");
                    // Don't fail registration if loyalty points fail
                }

                // Show success message and redirect
                ShowSuccessMessage("Account created successfully! Please check your email to verify your account.");

                // Clear form
                ClearForm();

                // Redirect to login page with success message
                Response.Redirect("~/Login.aspx?message=Registration successful! Please login to continue.");
            }
            catch (Exception ex)
            {
                LogError(ex, "Error during user registration");
                ShowErrorMessage("An error occurred while creating your account. Please try again.");
            }
        }

        private int InsertUser(User user)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Users (
                            Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, IsActive,
                            IsEmailVerified, EmailVerificationToken, CreatedDate, Address, City, State,
                            PostalCode, Country, DateOfBirth, Gender, LoyaltyPoints
                        )
                        OUTPUT INSERTED.UserId
                        VALUES (
                            @Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber, @Role, @IsActive,
                            @IsEmailVerified, @EmailVerificationToken, @CreatedDate, @Address, @City, @State,
                            @PostalCode, @Country, @DateOfBirth, @Gender, @LoyaltyPoints
                        )";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", (object)user.Email ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PasswordHash", (object)user.PasswordHash ?? DBNull.Value);
                        command.Parameters.AddWithValue("@FirstName", (object)user.FirstName ?? DBNull.Value);
                        command.Parameters.AddWithValue("@LastName", (object)user.LastName ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PhoneNumber", (object)user.PhoneNumber ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Role", (object)user.Role ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", user.IsActive);
                        command.Parameters.AddWithValue("@IsEmailVerified", user.IsEmailVerified);
                        command.Parameters.AddWithValue("@EmailVerificationToken", (object)user.EmailVerificationToken ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedDate", user.CreatedDate);
                        command.Parameters.AddWithValue("@Address", (object)user.Address ?? DBNull.Value);
                        command.Parameters.AddWithValue("@City", (object)user.City ?? DBNull.Value);
                        command.Parameters.AddWithValue("@State", (object)user.State ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PostalCode", (object)user.PostalCode ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Country", (object)user.Country ?? DBNull.Value);
                        command.Parameters.AddWithValue("@DateOfBirth", (object)user.DateOfBirth ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Gender", (object)user.Gender ?? DBNull.Value);
                        command.Parameters.AddWithValue("@LoyaltyPoints", 0);

                        int newUserId = (int)command.ExecuteScalar();
                        return newUserId;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error inserting user: {user.Email}");
                throw;
            }
        }

        private void CreateWelcomeLoyaltyPoints(int userId)
        {
            try
            {
                // Insert 50 welcome loyalty points directly to database
                InsertLoyaltyPoints(userId, 50, "Welcome Bonus");
                LogInfo($"Welcome loyalty points (50) created for user {userId}", "Registration");
            }
            catch (Exception ex)
            {
                LogError(ex, "Error creating welcome loyalty points");
            }
        }

        private void InsertLoyaltyPoints(int userId, int points, string reason)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        UPDATE Users
                        SET LoyaltyPoints = LoyaltyPoints + @Points
                        WHERE UserId = @UserId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@Points", points);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error inserting loyalty points for user {userId}: {reason}");
                throw;
            }
        }

        private void SendWelcomeEmail(User user)
        {
            // Implement email sending logic here
            // This is a placeholder for email service integration

            var emailSubject = "Welcome to MediEase!";
            var emailBody = $@"
                Dear {user.FirstName} {user.LastName},

                Welcome to MediEase! Your account has been created successfully.

                To complete your registration, please verify your email address by clicking the link below:
                [Verification Link - implement this]

                Your account details:
                - Email: {user.Email}
                - Phone: {user.PhoneNumber}
                - Loyalty Points: 50 (Welcome Bonus!)

                Thank you for choosing MediEase for your healthcare needs.

                Best regards,
                The MediEase Team
            ";

            // TODO: Implement actual email sending using SMTP or email service
            LogInfo($"Welcome email prepared for {user.Email} with subject: {emailSubject}", "Email");
        }

        // Self-contained logging methods
        private void LogError(Exception ex, string message)
        {
            try
            {
                // Simple file-based logging
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {message} - {ex.Message}\r\n{ex.StackTrace}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void LogInfo(string message, string category)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO [{category}]: {message}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"info_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void LogUserActivity(string activity, int userId)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] USER ACTIVITY [UserId: {userId}]: {activity}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"activity_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        protected void cvTerms_ServerValidate(object source, ServerValidateEventArgs args)
        {
            args.IsValid = chkTerms.Checked;
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            litErrorMessage.Text = SanitizeInput(message);
        }

        private void ShowSuccessMessage(string message)
        {
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            litSuccessMessage.Text = SanitizeInput(message);
        }

        private void ClearForm()
        {
            txtFirstName.Text = "";
            txtLastName.Text = "";
            txtEmail.Text = "";
            txtPassword.Text = "";
            txtConfirmPassword.Text = "";
            txtPhoneNumber.Text = "";
            txtAddress.Text = "";
            txtCity.Text = "";
            txtPostalCode.Text = "";
            txtDateOfBirth.Text = "";
            ddlGender.SelectedIndex = 0;
            chkTerms.Checked = false;
            chkNewsletter.Checked = true;
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);

            // Clear password fields for security
            txtPassword.Attributes.Add("value", "");
            txtConfirmPassword.Attributes.Add("value", "");
        }
    }
}