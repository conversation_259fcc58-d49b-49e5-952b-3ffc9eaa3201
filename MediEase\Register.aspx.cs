using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class Register : Page
    {
        // Database connection string from web.config
        private string ConnectionString
        {
            get
            {
                string connStr = ConfigurationManager.ConnectionStrings["MediEaseConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(connStr))
                {
                    LogError("Connection string 'MediEaseConnectionString' is missing in Web.config.");
                    throw new InvalidOperationException("Database connection string is not configured.");
                }
                return connStr;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Redirect if already logged in
                if (HttpContext.Current.User.Identity.IsAuthenticated)
                {
                    Response.Redirect("~/Default.aspx");
                    return;
                }

                Page.Title = "Register - MediEase Pharmacy";
            }
        }

        // Password hashing (BCrypt alternative using SHA256 if BCrypt.Net is unavailable)
        private string HashPassword(string password)
        {
            try
            {
                // Preferred: Use BCrypt if available
                return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
            }
            catch
            {
                // Fallback: SHA256 with salt
                string salt = Convert.ToBase64String(RandomBytes(16));
                string saltedPassword = password + salt;
                using (SHA256 sha256 = SHA256.Create())
                {
                    byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                    return Convert.ToBase64String(hashBytes) + ":" + salt;
                }
            }
        }

        private byte[] RandomBytes(int length)
        {
            byte[] bytes = new byte[length];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(bytes);
            }
            return bytes;
        }

        // Input validation
        private bool IsValidPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password) || password.Length < 8)
                return false;

            return password.Any(char.IsUpper) &&
                   password.Any(char.IsLower) &&
                   password.Any(char.IsDigit) &&
                   password.Any(c => !char.IsLetterOrDigit(c));
        }

        private string SanitizeInput(string input)
        {
            return string.IsNullOrEmpty(input) ? string.Empty : HttpUtility.HtmlEncode(input.Trim());
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            var digits = new string(phoneNumber.Where(char.IsDigit).ToArray());
            return digits.Length >= 10 && digits.Length <= 15;
        }

        private string GenerateEmailVerificationToken()
        {
            byte[] bytes = RandomBytes(32);
            return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }

        // Database operations
        private bool EmailExists(string email)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Email = @Email";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);
                        return (int)command.ExecuteScalar() > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error checking email existence: {email}. {ex.Message}");
                return false;
            }
        }

        private bool TestDatabaseConnection(out string errorMessage)
        {
            errorMessage = null;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("SELECT 1", connection))
                    {
                        command.ExecuteScalar();
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Cannot connect to database: {ex.Message}";
                LogError($"Database connection failed: {ex.Message}");
                return false;
            }
        }

        private bool VerifyUsersTableExists(out string errorMessage)
        {
            errorMessage = null;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users'";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        return (int)command.ExecuteScalar() > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error verifying Users table: {ex.Message}";
                LogError($"Error verifying Users table: {ex.Message}");
                return false;
            }
        }

        protected void btnRegister_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
            {
                ShowErrorMessage("Please correct the form errors.");
                return;
            }

            try
            {
                // Test database connection
                string dbError;
                if (!TestDatabaseConnection(out dbError))
                {
                    ShowErrorMessage(dbError);
                    return;
                }

                // Verify Users table
                string tableError;
                if (!VerifyUsersTableExists(out tableError))
                {
                    ShowErrorMessage(tableError);
                    return;
                }

                // Validate terms
                if (!chkTerms.Checked)
                {
                    ShowErrorMessage("You must agree to the terms and conditions.");
                    return;
                }

                // Validate password
                if (!IsValidPassword(txtPassword.Text))
                {
                    ShowErrorMessage("Password must be at least 8 characters with uppercase, lowercase, number, and special character.");
                    return;
                }

                // Check email uniqueness
                string email = SanitizeInput(txtEmail.Text).ToLower();
                if (EmailExists(email))
                {
                    ShowErrorMessage("This email is already registered. Please use a different email or log in.");
                    return;
                }

                // Validate phone number
                string phoneNumber = SanitizeInput(txtPhoneNumber.Text);
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    ShowErrorMessage("Please enter a valid phone number.");
                    return;
                }

                // Collect user data
                string firstName = SanitizeInput(txtFirstName.Text);
                string lastName = SanitizeInput(txtLastName.Text);
                string passwordHash = HashPassword(txtPassword.Text);
                string address = SanitizeInput(txtAddress.Text);
                string city = SanitizeInput(txtCity.Text);
                string postalCode = SanitizeInput(txtPostalCode.Text);
                string country = SanitizeInput(txtCountry.Text);
                string emailVerificationToken = GenerateEmailVerificationToken();

                DateTime? dateOfBirth = null;
                if (!string.IsNullOrEmpty(txtDateOfBirth.Text) && DateTime.TryParse(txtDateOfBirth.Text, out DateTime dob))
                {
                    dateOfBirth = dob;
                }

                string gender = string.IsNullOrEmpty(ddlGender.SelectedValue) ? null : SanitizeInput(ddlGender.SelectedValue);

                // Insert user
                int userId = InsertUser(firstName, lastName, email, passwordHash, phoneNumber, address, city, postalCode, country, dateOfBirth, gender, emailVerificationToken);

                // Log registration
                LogMessage($"User registered: {email} (UserId: {userId})");

                // Add welcome loyalty points
                try
                {
                    InsertLoyaltyPoints(userId, 50, "Welcome Bonus");
                }
                catch (Exception ex)
                {
                    LogError($"Error adding loyalty points for UserId {userId}: {ex.Message}");
                }

                // Show success
                ShowSuccessMessage("Account created successfully! Please check your email to verify your account.");
                ClearForm();
                Response.Redirect("~/Login.aspx?message=Registration successful! Please login to continue.");
            }
            catch (Exception ex)
            {
                LogError($"Registration failed: {ex.Message}");
                ShowErrorMessage($"Registration failed: {ex.Message}");
            }
        }

        private int InsertUser(string firstName, string lastName, string email, string passwordHash, string phoneNumber, string address, string city, string postalCode, string country, DateTime? dateOfBirth, string gender, string emailVerificationToken)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Users (
                            Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, IsActive,
                            IsEmailVerified, EmailVerificationToken, CreatedDate, Address, City,
                            PostalCode, Country, DateOfBirth, Gender, LoyaltyPoints, PreferredLanguage
                        )
                        OUTPUT INSERTED.UserId
                        VALUES (
                            @Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber, 'Customer', 1,
                            0, @EmailVerificationToken, GETDATE(), @Address, @City,
                            @PostalCode, @Country, @DateOfBirth, @Gender, 0, 'en'
                        )";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);
                        command.Parameters.AddWithValue("@PasswordHash", passwordHash);
                        command.Parameters.AddWithValue("@FirstName", firstName);
                        command.Parameters.AddWithValue("@LastName", lastName);
                        command.Parameters.AddWithValue("@PhoneNumber", phoneNumber);
                        command.Parameters.AddWithValue("@EmailVerificationToken", emailVerificationToken);
                        command.Parameters.AddWithValue("@Address", string.IsNullOrEmpty(address) ? (object)DBNull.Value : address);
                        command.Parameters.AddWithValue("@City", string.IsNullOrEmpty(city) ? (object)DBNull.Value : city);
                        command.Parameters.AddWithValue("@PostalCode", string.IsNullOrEmpty(postalCode) ? (object)DBNull.Value : postalCode);
                        command.Parameters.AddWithValue("@Country", string.IsNullOrEmpty(country) ? (object)DBNull.Value : country);
                        command.Parameters.AddWithValue("@DateOfBirth", dateOfBirth.HasValue ? (object)dateOfBirth.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@Gender", string.IsNullOrEmpty(gender) ? (object)DBNull.Value : gender);

                        object result = command.ExecuteScalar();
                        if (result == null)
                        {
                            throw new Exception("Failed to retrieve UserId after insertion.");
                        }
                        return (int)result;
                    }
                }
            }
            catch (SqlException ex)
            {
                LogError($"SQL Error inserting user {email}: {ex.Message}, Error Number: {ex.Number}, Line: {ex.LineNumber}");
                throw new Exception($"Database error: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                LogError($"Error inserting user {email}: {ex.Message}");
                throw;
            }
        }

        private void InsertLoyaltyPoints(int userId, int points, string reason)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO LoyaltyTransactions (UserId, TransactionType, Points, Description, CreatedDate)
                        VALUES (@UserId, 'Credit', @Points, @Description, GETDATE());
                        UPDATE Users SET LoyaltyPoints = LoyaltyPoints + @Points WHERE UserId = @UserId";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@Points", points);
                        command.Parameters.AddWithValue("@Description", reason);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error adding loyalty points for UserId {userId}: {ex.Message}");
            }
        }

        // Logging
        private void LogError(string message)
        {
            try
            {
                string logPath = Server.MapPath("~/App_Data/Logs/");
                if (!Directory.Exists(logPath))
                    Directory.CreateDirectory(logPath);

                string logFile = Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log");
                File.AppendAllText(logFile, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {message}\n");
            }
            catch
            {
                // Silent fail
            }
        }

        private void LogMessage(string message)
        {
            try
            {
                string logPath = Server.MapPath("~/App_Data/Logs/");
                if (!Directory.Exists(logPath))
                    Directory.CreateDirectory(logPath);

                string logFile = Path.Combine(logPath, $"info_{DateTime.Now:yyyyMMdd}.log");
                File.AppendAllText(logFile, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: {message}\n");
            }
            catch
            {
                // Silent fail
            }
        }

        // UI methods
        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            litErrorMessage.Text = SanitizeInput(message);
        }

        private void ShowSuccessMessage(string message)
        {
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            litSuccessMessage.Text = SanitizeInput(message);
        }

        private void ClearForm()
        {
            txtFirstName.Text = "";
            txtLastName.Text = "";
            txtEmail.Text = "";
            txtPassword.Text = "";
            txtConfirmPassword.Text = "";
            txtPhoneNumber.Text = "";
            txtAddress.Text = "";
            txtCity.Text = "";
            txtPostalCode.Text = "";
            txtDateOfBirth.Text = "";
            ddlGender.SelectedIndex = 0;
            chkTerms.Checked = false;
            chkNewsletter.Checked = true;
        }

        protected void cvTerms_ServerValidate(object source, ServerValidateEventArgs args)
        {
            args.IsValid = chkTerms.Checked;
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            txtPassword.Attributes.Add("value", "");
            txtConfirmPassword.Attributes.Add("value", "");
        }
    }
}