using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace MediEase
{
    public partial class Register : Page
    {
        // Database connection string from web.config
        private string ConnectionString
        {
            get
            {
                string connStr = ConfigurationManager.ConnectionStrings["MediEaseConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(connStr))
                {
                    throw new InvalidOperationException("Database connection string 'MediEaseConnectionString' is not configured in Web.config.");
                }
                return connStr;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (HttpContext.Current.User.Identity.IsAuthenticated)
                {
                    Response.Redirect("~/Default.aspx");
                    return;
                }

                SetPageMetadata();
            }
        }

        private void SetPageMetadata()
        {
            Page.Title = "Register - MediEase Pharmacy";
        }

        // Security methods
        private string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
        }

        private bool IsValidPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password) || password.Length < 8)
                return false;

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }

        private string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return HttpUtility.HtmlEncode(input.Trim());
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            var digits = new string(phoneNumber.Where(char.IsDigit).ToArray());
            return digits.Length >= 10 && digits.Length <= 15;
        }

        private string GenerateEmailVerificationToken()
        {
            using (var rng = new System.Security.Cryptography.RNGCryptoServiceProvider())
            {
                var bytes = new byte[32];
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
            }
        }

        // Database methods
        private bool EmailExists(string email)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Email = @Email";
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);
                        int count = (int)command.ExecuteScalar();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error checking email existence: {email}");
                return false; // Assume email doesn't exist to avoid blocking registration
            }
        }

        private bool TestDatabaseConnection()
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SELECT 1", connection))
                    {
                        command.ExecuteScalar();
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogError(ex, "Database connection test failed");
                return false;
            }
        }

        protected void btnRegister_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                // Test database connection
                if (!TestDatabaseConnection())
                {
                    ShowErrorMessage("Unable to connect to the database. Please try again later.");
                    return;
                }

                // Validate terms acceptance
                if (!chkTerms.Checked)
                {
                    ShowErrorMessage("You must agree to the terms and conditions to create an account.");
                    return;
                }

                // Validate password strength
                if (!IsValidPassword(txtPassword.Text))
                {
                    ShowErrorMessage("Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.");
                    return;
                }

                // Check if email already exists
                string email = txtEmail.Text.Trim().ToLower();
                if (EmailExists(email))
                {
                    ShowErrorMessage("An account with this email address already exists. Please use a different email or try logging in.");
                    return;
                }

                // Validate phone number
                string phoneNumber = SanitizeInput(txtPhoneNumber.Text.Trim());
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    ShowErrorMessage("Please enter a valid phone number.");
                    return;
                }

                // Prepare user data
                string firstName = SanitizeInput(txtFirstName.Text.Trim());
                string lastName = SanitizeInput(txtLastName.Text.Trim());
                string passwordHash = HashPassword(txtPassword.Text);
                string address = SanitizeInput(txtAddress.Text.Trim());
                brk
                string city = SanitizeInput(txtCity.Text.Trim());
                string postalCode = SanitizeInput(txtPostalCode.Text.Trim());
                string country = SanitizeInput(txtCountry.Text.Trim());
                string emailVerificationToken = GenerateEmailVerificationToken();

                DateTime? dateOfBirth = null;
                if (!string.IsNullOrEmpty(txtDateOfBirth.Text) && DateTime.TryParse(txtDateOfBirth.Text, out DateTime dob))
                {
                    dateOfBirth = dob;
                }

                string gender = string.IsNullOrEmpty(ddlGender.SelectedValue) ? null : ddlGender.SelectedValue;

                // Insert user into database
                int newUserId = InsertUser(firstName, lastName, email, passwordHash, phoneNumber, address, city, postalCode, country, dateOfBirth, gender, emailVerificationToken);

                // Log user registration
                LogUserActivity($"New user registered: {email}", newUserId);

                // Create welcome loyalty points
                try
                {
                    InsertLoyaltyPoints(newUserId, 50, "Welcome Bonus");
                }
                catch (Exception ex)
                {
                    LogError(ex, "Error creating welcome loyalty points");
                }

                // Show success message
                ShowSuccessMessage("Account created successfully! Please check your email to verify your account.");

                // Clear form
                ClearForm();

                // Redirect to login page
                Response.Redirect("~/Login.aspx?message=Registration successful! Please login to continue.");
            }
            catch (Exception ex)
            {
                LogError(ex, "Error during user registration");
                ShowErrorMessage($"An error occurred while creating your account: {ex.Message}. Please try again.");
            }
        }

        private int InsertUser(string firstName, string lastName, string email, string passwordHash, string phoneNumber, string address, string city, string postalCode, string country, DateTime? dateOfBirth, string gender, string emailVerificationToken)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Users (
                            Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, IsActive,
                            IsEmailVerified, EmailVerificationToken, CreatedDate, Address, City,
                            PostalCode, Country, DateOfBirth, Gender, LoyaltyPoints, PreferredLanguage
                        )
                        OUTPUT INSERTED.UserId
                        VALUES (
                            @Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber, 'Customer', 1,
                            0, @EmailVerificationToken, GETDATE(), @Address, @City,
                            @PostalCode, @Country, @DateOfBirth, @Gender, 0, 'en'
                        )";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);
                        command.Parameters.AddWithValue("@PasswordHash", passwordHash);
                        command.Parameters.AddWithValue("@FirstName", firstName);
                        command.Parameters.AddWithValue("@LastName", lastName);
                        command.Parameters.AddWithValue("@PhoneNumber", phoneNumber);
                        command.Parameters.AddWithValue("@EmailVerificationToken", emailVerificationToken);
                        command.Parameters.AddWithValue("@Address", string.IsNullOrEmpty(address) ? DBNull.Value : address);
                        command.Parameters.AddWithValue("@City", string.IsNullOrEmpty(city) ? DBNull.Value : city);
                        command.Parameters.AddWithValue("@PostalCode", string.IsNullOrEmpty(postalCode) ? DBNull.Value : postalCode);
                        command.Parameters.AddWithValue("@Country", string.IsNullOrEmpty(country) ? DBNull.Value : country);
                        command.Parameters.AddWithValue("@DateOfBirth", dateOfBirth.HasValue ? (object)dateOfBirth.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@Gender", string.IsNullOrEmpty(gender) ? DBNull.Value : gender);

                        object result = command.ExecuteScalar();
                        if (result == null)
                        {
                            throw new Exception("Failed to retrieve new UserId after insertion.");
                        }
                        return (int)result;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error inserting user: {email}");
                throw;
            }
        }

        private void InsertLoyaltyPoints(int userId, int points, string reason)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO LoyaltyTransactions (UserId, TransactionType, Points, Description, CreatedDate)
                        VALUES (@UserId, 'Credit', @Points, @Description, GETDATE());
                        UPDATE Users SET LoyaltyPoints = LoyaltyPoints + @Points WHERE UserId = @UserId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@Points", points);
                        command.Parameters.AddWithValue("@Description", reason);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"Error inserting loyalty points for user {userId}: {reason}");
            }
        }

        // Logging methods
        private void LogError(Exception ex, string message)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {message} - {ex.Message}\n{ex.StackTrace}\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void LogUserActivity(string activity, int userId)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] USER ACTIVITY [UserId: {userId}]: {activity}\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"activity_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        protected void cvTerms_ServerValidate(object source, ServerValidateEventArgs args)
        {
            args.IsValid = chkTerms.Checked;
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            litErrorMessage.Text = SanitizeInput(message);
        }

        private void ShowSuccessMessage(string message)
        {
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            litSuccessMessage.Text = SanitizeInput(message);
        }

        private void ClearForm()
        {
            txtFirstName.Text = "";
            txtLastName.Text = "";
            txtEmail.Text = "";
            txtPassword.Text = "";
            txtConfirmPassword.Text = "";
            txtPhoneNumber.Text = "";
            txtAddress.Text = "";
            txtCity.Text = "";
            txtPostalCode.Text = "";
            txtDateOfBirth.Text = "";
            ddlGender.SelectedIndex = 0;
            chkTerms.Checked = false;
            chkNewsletter.Checked = true;
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            txtPassword.Attributes.Add("value", "");
            txtConfirmPassword.Attributes.Add("value", "");
        }
    }
}