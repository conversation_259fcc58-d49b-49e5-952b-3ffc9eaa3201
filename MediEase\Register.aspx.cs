using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class Register : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Check if user is already logged in
                if (SecurityHelper.GetCurrentUser() != null)
                {
                    Response.Redirect("~/Default.aspx");
                    return;
                }

                SetPageMetadata();
            }
        }

        private void SetPageMetadata()
        {
            Page.Title = "Register - MediEase Pharmacy";

            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Create your MediEase account to access pharmacy services, order medicines online, and manage your health records securely.");
                master.AddMetaKeywords("register, create account, pharmacy registration, MediEase signup, online pharmacy account");
            }
        }

        protected void btnRegister_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            try
            {
                // Validate terms acceptance
                if (!chkTerms.Checked)
                {
                    ShowErrorMessage("You must agree to the terms and conditions to create an account.");
                    return;
                }

                // Validate password strength
                if (!SecurityHelper.IsValidPassword(txtPassword.Text))
                {
                    ShowErrorMessage("Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.");
                    return;
                }

                // Check if email already exists using direct database access
                if (MediEase.DAL.DirectDatabaseAccess.EmailExists(txtEmail.Text.Trim().ToLower()))
                {
                    ShowErrorMessage("An account with this email address already exists. Please use a different email or try logging in.");
                    return;
                }

                // Create new user
                var newUser = new User
                {
                    FirstName = SecurityHelper.SanitizeInput(txtFirstName.Text.Trim()),
                    LastName = SecurityHelper.SanitizeInput(txtLastName.Text.Trim()),
                    Email = txtEmail.Text.Trim().ToLower(),
                    PasswordHash = SecurityHelper.HashPassword(txtPassword.Text),
                    PhoneNumber = SecurityHelper.SanitizeInput(txtPhoneNumber.Text.Trim()),
                    Address = SecurityHelper.SanitizeInput(txtAddress.Text.Trim()),
                    City = SecurityHelper.SanitizeInput(txtCity.Text.Trim()),
                    PostalCode = SecurityHelper.SanitizeInput(txtPostalCode.Text.Trim()),
                    Country = SecurityHelper.SanitizeInput(txtCountry.Text.Trim()),
                    Role = "Customer", // Default role for registration
                    IsActive = true,
                    IsEmailVerified = false, // Will be verified via email
                    IsPhoneVerified = false,
                    CreatedDate = DateTime.Now,
                    EmailVerificationToken = SecurityHelper.GenerateEmailVerificationToken(),
                    NotificationPreferences = chkNewsletter.Checked ? "email,sms" : "sms"
                };

                // Set optional fields
                if (!string.IsNullOrEmpty(txtDateOfBirth.Text))
                {
                    if (DateTime.TryParse(txtDateOfBirth.Text, out DateTime dob))
                    {
                        newUser.DateOfBirth = dob;
                    }
                }

                if (!string.IsNullOrEmpty(ddlGender.SelectedValue))
                {
                    newUser.Gender = ddlGender.SelectedValue;
                }

                // Validate phone number format
                if (!SecurityHelper.IsValidPhoneNumber(newUser.PhoneNumber))
                {
                    ShowErrorMessage("Please enter a valid phone number.");
                    return;
                }

                // Save user to database using direct database access
                var newUserId = MediEase.DAL.DirectDatabaseAccess.InsertUser(newUser);
                newUser.UserId = newUserId;

                // Log user registration
                ErrorLogger.LogUserActivity($"New user registered: {newUser.Email}", newUser.UserId);

                // Send welcome email (optional - implement email service)
                try
                {
                    SendWelcomeEmail(newUser);
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error sending welcome email");
                    // Don't fail registration if email fails
                }

                // Create initial loyalty points
                try
                {
                    CreateWelcomeLoyaltyPoints(newUser.UserId);
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error creating welcome loyalty points");
                    // Don't fail registration if loyalty points fail
                }

                // Show success message and redirect
                ShowSuccessMessage("Account created successfully! Please check your email to verify your account.");

                // Clear form
                ClearForm();

                // Redirect to login page with success message
                Response.Redirect("~/Login.aspx?message=Registration successful! Please login to continue.");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error during user registration");
                ShowErrorMessage("An error occurred while creating your account. Please try again.");
            }
        }

        private void CreateWelcomeLoyaltyPoints(int userId)
        {
            try
            {
                // Insert 50 welcome loyalty points directly to database
                MediEase.DAL.DirectDatabaseAccess.InsertLoyaltyPoints(userId, 50, "Welcome Bonus");
                ErrorLogger.LogInfo($"Welcome loyalty points (50) created for user {userId}", "Registration");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error creating welcome loyalty points");
            }
        }

        private void SendWelcomeEmail(User user)
        {
            // Implement email sending logic here
            // This is a placeholder for email service integration

            var emailSubject = "Welcome to MediEase!";
            var emailBody = $@"
                Dear {user.FirstName} {user.LastName},
                
                Welcome to MediEase! Your account has been created successfully.
                
                To complete your registration, please verify your email address by clicking the link below:
                [Verification Link - implement this]
                
                Your account details:
                - Email: {user.Email}
                - Phone: {user.PhoneNumber}
                - Loyalty Points: 50 (Welcome Bonus!)
                
                Thank you for choosing MediEase for your healthcare needs.
                
                Best regards,
                The MediEase Team
            ";

            // TODO: Implement actual email sending using SMTP or email service
            ErrorLogger.LogInfo($"Welcome email prepared for {user.Email} with subject: {emailSubject}", "Email");
        }

        protected void cvTerms_ServerValidate(object source, ServerValidateEventArgs args)
        {
            args.IsValid = chkTerms.Checked;
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            litErrorMessage.Text = SecurityHelper.SanitizeInput(message);
        }

        private void ShowSuccessMessage(string message)
        {
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
            litSuccessMessage.Text = SecurityHelper.SanitizeInput(message);
        }

        private void ClearForm()
        {
            txtFirstName.Text = "";
            txtLastName.Text = "";
            txtEmail.Text = "";
            txtPassword.Text = "";
            txtConfirmPassword.Text = "";
            txtPhoneNumber.Text = "";
            txtAddress.Text = "";
            txtCity.Text = "";
            txtPostalCode.Text = "";
            txtDateOfBirth.Text = "";
            ddlGender.SelectedIndex = 0;
            chkTerms.Checked = false;
            chkNewsletter.Checked = true;
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);

            // Clear password fields for security
            txtPassword.Attributes.Add("value", "");
            txtConfirmPassword.Attributes.Add("value", "");
        }
    }
}