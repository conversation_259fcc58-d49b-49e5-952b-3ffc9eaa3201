//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Pharmacist
{
    public partial class OrderManagement
    {
        /// <summary>
        /// lblPendingOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPendingOrders;

        /// <summary>
        /// lblProcessingOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblProcessingOrders;

        /// <summary>
        /// lblTodayOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTodayOrders;

        /// <summary>
        /// lblCompletedToday control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblCompletedToday;

        /// <summary>
        /// lblUrgentOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblUrgentOrders;

        /// <summary>
        /// lblTodayRevenue control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTodayRevenue;

        /// <summary>
        /// ddlStatusFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlStatusFilter;

        /// <summary>
        /// ddlPriorityFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlPriorityFilter;

        /// <summary>
        /// ddlDateFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlDateFilter;

        /// <summary>
        /// txtSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtSearch;

        /// <summary>
        /// btnSearch control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnSearch;

        /// <summary>
        /// btnRefresh control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnRefresh;

        /// <summary>
        /// gvOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvOrders;

        /// <summary>
        /// rptPendingOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptPendingOrders;

        /// <summary>
        /// rptProcessingOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptProcessingOrders;

        /// <summary>
        /// rptVerifiedOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptVerifiedOrders;

        /// <summary>
        /// rptPackedOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptPackedOrders;

        /// <summary>
        /// rptShippedOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptShippedOrders;

        /// <summary>
        /// rptDeliveredOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptDeliveredOrders;

        /// <summary>
        /// pnlNoOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlNoOrders;
    }
}
