//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Customer
{
    public partial class OrderTracking
    {
        /// <summary>
        /// txtOrderNumber control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtOrderNumber;

        /// <summary>
        /// btnTrackOrder control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnTrackOrder;

        /// <summary>
        /// ddlStatusFilter control.
        /// </summary>
        protected global::System.Web.UI.WebControls.DropDownList ddlStatusFilter;

        /// <summary>
        /// pnlOrderDetails control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlOrderDetails;

        /// <summary>
        /// lblOrderNumber control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblOrderNumber;

        /// <summary>
        /// lblOrderStatus control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblOrderStatus;

        /// <summary>
        /// lblOrderDate control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblOrderDate;

        /// <summary>
        /// lblTotalAmount control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblTotalAmount;

        /// <summary>
        /// lblPaymentMethod control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblPaymentMethod;

        /// <summary>
        /// lblExpectedDelivery control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblExpectedDelivery;

        /// <summary>
        /// lblDeliveryAddress control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblDeliveryAddress;

        /// <summary>
        /// lblContactNumber control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblContactNumber;

        /// <summary>
        /// rptTrackingTimeline control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Repeater rptTrackingTimeline;

        /// <summary>
        /// gvOrderItems control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvOrderItems;

        /// <summary>
        /// gvOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.GridView gvOrders;

        /// <summary>
        /// pnlNoOrders control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlNoOrders;
    }
}
