<%@ Page Title="Medicine Management" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MedicineManagement.aspx.cs" Inherits="MediEase.Admin.MedicineManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-pills me-2 text-primary"></i>Medicine Management</h2>
                        <p class="text-muted">Manage medicines, categories, and brands</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addMedicineModal">
                            <i class="fas fa-plus me-2"></i>Add Medicine
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-folder-plus me-2"></i>Add Category
                        </button>
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addBrandModal">
                            <i class="fas fa-tag me-2"></i>Add Brand
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="exportMedicines()">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-pills fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalMedicines" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Medicines</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActiveMedicines" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Medicines</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-folder fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalCategories" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Categories</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-tags fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalBrands" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Brands</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Category Filter</label>
                                <asp:DropDownList ID="ddlCategoryFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlCategoryFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Categories</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Brand Filter</label>
                                <asp:DropDownList ID="ddlBrandFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlBrandFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Brands</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Status Filter</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Status</asp:ListItem>
                                    <asp:ListItem Value="true">Active</asp:ListItem>
                                    <asp:ListItem Value="false">Inactive</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search medicines..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Medicines Grid -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Medicines</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="toggleView('table')">
                                <i class="fas fa-table"></i> Table View
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="toggleView('grid')">
                                <i class="fas fa-th"></i> Grid View
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Table View -->
                        <div id="tableView">
                            <div class="table-responsive">
                                <asp:GridView ID="gvMedicines" runat="server" CssClass="table table-hover" 
                                    AutoGenerateColumns="false" EmptyDataText="No medicines found." OnRowCommand="gvMedicines_RowCommand"
                                    AllowPaging="true" PageSize="20" OnPageIndexChanging="gvMedicines_PageIndexChanging">
                                    <Columns>
                                        <asp:TemplateField HeaderText="Image">
                                            <ItemTemplate>
                                                <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' alt="Medicine" class="rounded" width="50" height="50" />
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Medicine Details">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("Name") %></strong><br>
                                                    <small class="text-muted"><%# Eval("GenericName") %></small><br>
                                                    <small class="text-muted"><%# Eval("Strength") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Category & Brand">
                                            <ItemTemplate>
                                                <div>
                                                    <span class="badge bg-info"><%# Eval("Category.Name") %></span><br>
                                                    <span class="badge bg-secondary mt-1"><%# Eval("Brand.Name") %></span>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Price & Stock">
                                            <ItemTemplate>
                                                <div>
                                                    <strong class="text-success">$<%# String.Format("{0:F2}", Eval("Price")) %></strong><br>
                                                    <small class="text-muted">Stock: <%# Eval("StockQuantity") %></small><br>
                                                    <small class="text-muted">Min: <%# Eval("MinimumStockLevel") %></small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Status">
                                            <ItemTemplate>
                                                <div class="text-center">
                                                    <span class="badge bg-<%# Convert.ToBoolean(Eval("IsActive")) ? "success" : "danger" %> fs-6">
                                                        <%# Convert.ToBoolean(Eval("IsActive")) ? "Active" : "Inactive" %>
                                                    </span>
                                                    <%# Convert.ToBoolean(Eval("PrescriptionRequired")) ? 
                                                        "<br><span class='badge bg-warning text-dark mt-1'>Rx Required</span>" : "" %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Actions">
                                            <ItemTemplate>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info btn-sm view-medicine" 
                                                        data-medicine-id='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm edit-medicine mt-1" 
                                                        data-medicine-id='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-edit me-1"></i>Edit
                                                    </button>
                                                    <asp:LinkButton runat="server" CssClass="btn btn-outline-warning btn-sm mt-1" 
                                                        CommandName="ToggleStatus" CommandArgument='<%# Eval("MedicineId") %>'>
                                                        <i class="fas fa-power-off me-1"></i><%# Convert.ToBoolean(Eval("IsActive")) ? "Deactivate" : "Activate" %>
                                                    </asp:LinkButton>
                                                    <asp:LinkButton runat="server" CssClass="btn btn-outline-danger btn-sm mt-1" 
                                                        CommandName="Delete" CommandArgument='<%# Eval("MedicineId") %>'
                                                        OnClientClick="return confirm('Are you sure you want to delete this medicine?');">
                                                        <i class="fas fa-trash me-1"></i>Delete
                                                    </asp:LinkButton>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                    <PagerStyle CssClass="pagination-ys" />
                                </asp:GridView>
                            </div>
                        </div>

                        <!-- Grid View (Hidden by default) -->
                        <div id="gridView" class="row g-4" style="display: none;">
                            <asp:Repeater ID="rptMedicinesGrid" runat="server" OnItemCommand="rptMedicinesGrid_ItemCommand">
                                <ItemTemplate>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card medicine-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span class="badge bg-<%# Convert.ToBoolean(Eval("IsActive")) ? "success" : "danger" %>">
                                                    <%# Convert.ToBoolean(Eval("IsActive")) ? "Active" : "Inactive" %>
                                                </span>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item view-medicine" href="#" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                            <i class="fas fa-eye me-2"></i>View Details</a></li>
                                                        <li><a class="dropdown-item edit-medicine" href="#" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                            <i class="fas fa-edit me-2"></i>Edit Medicine</a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><asp:LinkButton runat="server" CssClass="dropdown-item" 
                                                            CommandName="ToggleStatus" CommandArgument='<%# Eval("MedicineId") %>'>
                                                            <i class="fas fa-power-off me-2"></i><%# Convert.ToBoolean(Eval("IsActive")) ? "Deactivate" : "Activate" %>
                                                        </asp:LinkButton></li>
                                                        <li><asp:LinkButton runat="server" CssClass="dropdown-item text-danger" 
                                                            CommandName="Delete" CommandArgument='<%# Eval("MedicineId") %>'
                                                            OnClientClick="return confirm('Are you sure you want to delete this medicine?');">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </asp:LinkButton></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="card-body text-center">
                                                <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' 
                                                     alt="Medicine" class="rounded mb-3" width="100" height="100" />
                                                <h6 class="text-primary"><%# Eval("Name") %></h6>
                                                <p class="small text-muted"><%# Eval("GenericName") %></p>
                                                <p class="small text-muted"><%# Eval("Strength") %></p>
                                                
                                                <div class="price-info mb-3">
                                                    <h5 class="text-success">$<%# String.Format("{0:F2}", Eval("Price")) %></h5>
                                                    <small class="text-muted">Stock: <%# Eval("StockQuantity") %></small>
                                                </div>
                                                
                                                <div class="category-brand">
                                                    <span class="badge bg-info"><%# Eval("Category.Name") %></span>
                                                    <span class="badge bg-secondary"><%# Eval("Brand.Name") %></span>
                                                    <%# Convert.ToBoolean(Eval("PrescriptionRequired")) ? 
                                                        "<br><span class='badge bg-warning text-dark mt-1'>Rx Required</span>" : "" %>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-grid gap-2">
                                                    <div class="btn-group">
                                                        <button class="btn btn-outline-info btn-sm view-medicine" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                            <i class="fas fa-eye me-1"></i>View
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm edit-medicine" data-medicine-id='<%# Eval("MedicineId") %>'>
                                                            <i class="fas fa-edit me-1"></i>Edit
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Medicine Modal -->
    <div class="modal fade" id="addMedicineModal" tabindex="-1" aria-labelledby="addMedicineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addMedicineModalLabel">Add New Medicine</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="addMedicineForm">
                        <!-- Medicine form will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">Add New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <div class="mb-3">
                            <label class="form-label">Category Name *</label>
                            <asp:TextBox ID="txtCategoryName" runat="server" CssClass="form-control" placeholder="Enter category name" required></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <asp:TextBox ID="txtCategoryDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                placeholder="Enter category description"></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkCategoryActive" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkCategoryActive.ClientID %>">
                                    Active Category
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSaveCategory" runat="server" CssClass="btn btn-info" Text="Save Category" OnClick="btnSaveCategory_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Add Brand Modal -->
    <div class="modal fade" id="addBrandModal" tabindex="-1" aria-labelledby="addBrandModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addBrandModalLabel">Add New Brand</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="brandForm">
                        <div class="mb-3">
                            <label class="form-label">Brand Name *</label>
                            <asp:TextBox ID="txtBrandName" runat="server" CssClass="form-control" placeholder="Enter brand name" required></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Manufacturer</label>
                            <asp:TextBox ID="txtManufacturer" runat="server" CssClass="form-control" placeholder="Enter manufacturer name"></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Country</label>
                            <asp:TextBox ID="txtCountry" runat="server" CssClass="form-control" placeholder="Enter country of origin"></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <asp:TextBox ID="txtBrandDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" 
                                placeholder="Enter brand description"></asp:TextBox>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkBrandActive" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkBrandActive.ClientID %>">
                                    Active Brand
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <asp:Button ID="btnSaveBrand" runat="server" CssClass="btn btn-warning" Text="Save Brand" OnClick="btnSaveBrand_Click" />
                </div>
            </div>
        </div>
    </div>

    <!-- Medicine Details Modal -->
    <div class="modal fade" id="medicineModal" tabindex="-1" aria-labelledby="medicineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="medicineModalLabel">Medicine Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="medicineDetails">
                        <!-- Medicine details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="medicineActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .medicine-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .medicine-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
    </style>

    <script>
        function toggleView(viewType) {
            const tableView = document.getElementById('tableView');
            const gridView = document.getElementById('gridView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'table') {
                tableView.style.display = 'block';
                gridView.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                tableView.style.display = 'none';
                gridView.style.display = 'flex';
                buttons[1].classList.add('active');
            }
        }

        // View medicine details
        $(document).on('click', '.view-medicine', function() {
            const medicineId = $(this).data('medicine-id');
            loadMedicineDetails(medicineId);
        });

        // Edit medicine
        $(document).on('click', '.edit-medicine', function() {
            const medicineId = $(this).data('medicine-id');
            loadEditMedicineForm(medicineId);
        });

        function loadMedicineDetails(medicineId) {
            $.ajax({
                type: 'POST',
                url: 'MedicineManagement.aspx/GetMedicineDetails',
                data: JSON.stringify({ medicineId: medicineId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#medicineDetails').html(response.d.html);
                        $('#medicineActions').html(response.d.actions);
                        $('#medicineModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading medicine details.');
                }
            });
        }

        function loadEditMedicineForm(medicineId) {
            $.ajax({
                type: 'POST',
                url: 'MedicineManagement.aspx/GetEditMedicineForm',
                data: JSON.stringify({ medicineId: medicineId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#addMedicineForm').html(response.d.html);
                        $('#addMedicineModalLabel').text('Edit Medicine');
                        $('#addMedicineModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading medicine form.');
                }
            });
        }

        function exportMedicines() {
            window.open('ExportMedicines.aspx', '_blank');
        }

        // Load add medicine form
        $('#addMedicineModal').on('show.bs.modal', function() {
            if ($('#addMedicineModalLabel').text() === 'Add New Medicine') {
                loadAddMedicineForm();
            }
        });

        function loadAddMedicineForm() {
            $.ajax({
                type: 'POST',
                url: 'MedicineManagement.aspx/GetAddMedicineForm',
                data: JSON.stringify({}),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#addMedicineForm').html(response.d.html);
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading medicine form.');
                }
            });
        }
    </script>
</asp:Content>
