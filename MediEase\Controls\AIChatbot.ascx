<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="AIChatbot.ascx.cs" Inherits="MediEase.Controls.AIChatbot" %>

<!-- Chatbot Toggle Button -->
<div id="chatbotToggle" class="chatbot-toggle" onclick="toggleChatbot()">
    <i class="fas fa-robot"></i>
    <span class="chatbot-badge" id="chatbotBadge" style="display: none;">1</span>
</div>

<!-- Chatbot Container -->
<div id="chatbotContainer" class="chatbot-container" style="display: none;">
    <div class="chatbot-header">
        <div class="d-flex align-items-center">
            <div class="chatbot-avatar me-2">
                <i class="fas fa-robot"></i>
            </div>
            <div>
                <h6 class="mb-0">MediEase AI Assistant</h6>
                <small class="text-muted">
                    <span id="chatbotStatus" class="status-online">
                        <i class="fas fa-circle me-1" style="font-size: 8px; color: #28a745;"></i>
                        Live AI • OpenRouter
                    </span>
                </small>
            </div>
        </div>
        <div class="chatbot-controls">
            <button type="button" class="btn btn-sm btn-outline-light" onclick="minimizeChatbot()" title="Minimize">
                <i class="fas fa-minus"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-light" onclick="closeChatbot()" title="Close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    
    <div class="chatbot-body">
        <div id="chatMessages" class="chat-messages">
            <!-- Welcome Message -->
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-bubble">
                        <p>Hello! I'm your MediEase AI Assistant powered by advanced AI technology. I can help you with medicine information, health questions, and pharmacy services. How can I assist you today?</p>
                        <div class="quick-actions mt-2">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="sendQuickMessage('What are the side effects of aspirin?')">
                                Medicine Info
                            </button>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="sendQuickMessage('How should I store my medications?')">
                                Storage Tips
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="sendQuickMessage('Can you help me understand my prescription?')">
                                Prescription Help
                            </button>
                        </div>
                    </div>
                    <small class="message-time">Just now</small>
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator" style="display: none;">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="chatbot-footer">
        <div class="input-group">
            <input type="text" id="chatInput" class="form-control" placeholder="Type your message..." 
                   onkeypress="handleChatKeyPress(event)" maxlength="500">
            <button class="btn btn-primary" type="button" onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
        <div class="chat-disclaimer">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                AI responses are for informational purposes only. Consult healthcare professionals for medical advice.
            </small>
        </div>
    </div>
</div>

<style>
.chatbot-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    z-index: 1000;
    transition: all 0.3s ease;
    color: white;
    font-size: 24px;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.chatbot-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-container {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chatbot-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chatbot-avatar {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-online {
    color: #28a745;
}

.chatbot-controls button {
    border: none;
    background: transparent;
    color: white;
    margin-left: 5px;
}

.chatbot-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 15px;
    animation: fadeInUp 0.3s ease;
}

.user-message {
    justify-content: flex-end;
}

.bot-message .message-avatar {
    width: 30px;
    height: 30px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    margin-right: 10px;
}

.message-content {
    max-width: 80%;
}

.user-message .message-content {
    text-align: right;
}

.message-bubble {
    background: white;
    padding: 10px 15px;
    border-radius: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 5px;
}

.user-message .message-bubble {
    background: #007bff;
    color: white;
}

.message-time {
    color: #6c757d;
    font-size: 11px;
}

.quick-actions button {
    font-size: 11px;
    padding: 4px 8px;
    margin-bottom: 5px;
}

.typing-dots {
    display: flex;
    align-items: center;
    padding: 10px 15px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #007bff;
    margin: 0 2px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

.chatbot-footer {
    padding: 15px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-disclaimer {
    margin-top: 8px;
    text-align: center;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

@media (max-width: 768px) {
    .chatbot-container {
        width: calc(100vw - 40px);
        height: calc(100vh - 140px);
        bottom: 90px;
        right: 20px;
        left: 20px;
    }
}
</style>

<script>
let chatbotOpen = false;
let messageCount = 0;

function toggleChatbot() {
    const container = document.getElementById('chatbotContainer');
    const badge = document.getElementById('chatbotBadge');
    
    if (chatbotOpen) {
        closeChatbot();
    } else {
        container.style.display = 'flex';
        chatbotOpen = true;
        badge.style.display = 'none';
        
        // Focus on input
        setTimeout(() => {
            document.getElementById('chatInput').focus();
        }, 100);
    }
}

function closeChatbot() {
    document.getElementById('chatbotContainer').style.display = 'none';
    chatbotOpen = false;
}

function minimizeChatbot() {
    closeChatbot();
}

function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

function sendMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    
    if (message) {
        addUserMessage(message);
        input.value = '';
        
        // Show typing indicator
        showTypingIndicator();
        
        // Send to AI service
        sendToAI(message);
    }
}

function sendQuickMessage(message) {
    addUserMessage(message);
    showTypingIndicator();
    sendToAI(message);
}

function addUserMessage(message) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message user-message';
    messageDiv.innerHTML = `
        <div class="message-content">
            <div class="message-bubble">
                <p>${escapeHtml(message)}</p>
            </div>
            <small class="message-time">${getCurrentTime()}</small>
        </div>
    `;
    messagesContainer.appendChild(messageDiv);
    scrollToBottom();
}

function addBotMessage(message) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message bot-message';
    messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <p>${message}</p>
            </div>
            <small class="message-time">${getCurrentTime()}</small>
        </div>
    `;
    messagesContainer.appendChild(messageDiv);
    scrollToBottom();
}

function showTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'block';
    scrollToBottom();
}

function hideTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'none';
}

function sendToAI(message) {
    // Call the AI service using form data
    const formData = new FormData();
    formData.append('message', message);

    fetch('Handlers/ChatHandler.ashx', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideTypingIndicator();
        if (data.success) {
            addBotMessage(data.message);
        } else {
            addBotMessage("I'm sorry, I'm having trouble processing your request right now. Please try again later.");
        }
    })
    .catch(error => {
        hideTypingIndicator();
        addBotMessage("I'm sorry, I'm having trouble connecting right now. Please try again later.");
        console.error('Chatbot error:', error);
    });
}

function scrollToBottom() {
    const messagesContainer = document.getElementById('chatMessages');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function getCurrentTime() {
    return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Initialize chatbot
document.addEventListener('DOMContentLoaded', function() {
    // Show welcome notification for new users
    if (!localStorage.getItem('chatbotSeen')) {
        setTimeout(() => {
            document.getElementById('chatbotBadge').style.display = 'flex';
            localStorage.setItem('chatbotSeen', 'true');
        }, 3000);
    }
});
</script>
