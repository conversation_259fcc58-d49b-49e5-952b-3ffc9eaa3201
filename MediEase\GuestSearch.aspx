<%@ Page Title="Search Medicines - Guest" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="GuestSearch.aspx.cs" Inherits="MediEase.GuestSearch" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="mb-2">Search Medicines</h2>
                <p class="text-muted">Browse our extensive collection of medicines and healthcare products</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Guest Access:</strong> You can browse medicines, but to purchase or get personalized recommendations, please 
                    <a href="Login.aspx" class="alert-link">login</a> or <a href="Register.aspx" class="alert-link">register</a>.
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search medicines by name, generic name, or symptoms..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlCategory_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Categories</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-3">
                                <asp:DropDownList ID="ddlSortBy" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlSortBy_SelectedIndexChanged">
                                    <asp:ListItem Value="name">Name A-Z</asp:ListItem>
                                    <asp:ListItem Value="name_desc">Name Z-A</asp:ListItem>
                                    <asp:ListItem Value="price">Price Low to High</asp:ListItem>
                                    <asp:ListItem Value="price_desc">Price High to Low</asp:ListItem>
                                    <asp:ListItem Value="popular">Most Popular</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Search Results (<asp:Label ID="lblResultCount" runat="server" Text="0"></asp:Label> items)</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary active" onclick="toggleView('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleView('list')">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- Grid View -->
                <div id="gridView" class="row g-4">
                    <asp:Repeater ID="rptMedicines" runat="server">
                        <ItemTemplate>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="card medicine-card h-100 border-0 shadow-sm">
                                    <div class="position-relative">
                                        <img src='<%# GetMedicineImage(Eval("ImagePath")) %>' 
                                             alt='<%# Eval("Name") %>' 
                                             class="card-img-top medicine-image" 
                                             style="height: 200px; object-fit: cover;" />
                                        <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                            "<span class=\"discount-badge position-absolute top-0 end-0 bg-danger text-white px-2 py-1 m-2 rounded\">" + 
                                            Eval("DiscountPercentage") + "% OFF</span>" : "" %>
                                        <%# !(bool)Eval("IsActive") || Convert.ToInt32(Eval("StockQuantity")) == 0 ?
                                            "<div class=\"position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50\">" +
                                            "<span class=\"text-white fw-bold\">OUT OF STOCK</span></div>" : "" %>
                                    </div>
                                    <div class="card-body d-flex flex-column">
                                        <h6 class="card-title"><%# Eval("Name") %></h6>
                                        <p class="card-text text-muted small mb-1"><%# Eval("GenericName") %></p>
                                        <p class="card-text small mb-2"><%# Eval("DosageForm") %> - <%# Eval("Strength") %></p>
                                        <p class="card-text small text-muted mb-2"><%# Eval("Manufacturer") %></p>
                                        
                                        <div class="mt-auto">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <div>
                                                    <span class="price-tag fw-bold text-primary">$<%# String.Format("{0:F2}", Eval("FinalPrice")) %></span>
                                                    <%# Convert.ToDecimal(Eval("DiscountPercentage")) > 0 ?
                                                        "<small class=\"original-price text-muted text-decoration-line-through ms-1\">$" + 
                                                        String.Format("{0:F2}", Eval("Price")) + "</small>" : "" %>
                                                </div>
                                                <small class="text-muted">Stock: <%# Eval("StockQuantity") %></small>
                                            </div>
                                            
                                            <%# (bool)Eval("PrescriptionRequired") ? 
                                                "<div class=\"mb-2\"><span class=\"badge bg-warning text-dark\">Prescription Required</span></div>" : "" %>
                                            
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-primary btn-sm" onclick="showLoginPrompt()">
                                                    <i class="fas fa-eye me-1"></i>View Details
                                                </button>
                                                <%# (bool)Eval("IsActive") && Convert.ToInt32(Eval("StockQuantity")) > 0 ?
                                                    "<button class=\"btn btn-primary btn-sm\" onclick=\"showLoginPrompt()\">" +
                                                    "<i class=\"fas fa-cart-plus me-1\"></i>Add to Cart</button>" :
                                                    "<button class=\"btn btn-secondary btn-sm\" disabled>Out of Stock</button>" %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:Repeater>
                </div>

                <!-- No Results -->
                <asp:Panel ID="pnlNoResults" runat="server" Visible="false" CssClass="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>No medicines found</h4>
                    <p class="text-muted">Try adjusting your search criteria or browse all categories.</p>
                    <a href="GuestSearch.aspx" class="btn btn-primary">View All Medicines</a>
                </asp:Panel>

                <!-- Pagination -->
                <asp:Panel ID="pnlPagination" runat="server" CssClass="d-flex justify-content-center mt-4">
                    <nav>
                        <ul class="pagination">
                            <asp:Repeater ID="rptPagination" runat="server">
                                <ItemTemplate>
                                    <li class="page-item <%# (int)Eval("PageNumber") == CurrentPage ? "active" : "" %>">
                                        <asp:LinkButton ID="lnkPage" runat="server" CssClass="page-link" 
                                            CommandArgument='<%# Eval("PageNumber") %>' OnClick="lnkPage_Click">
                                            <%# Eval("PageNumber") %>
                                        </asp:LinkButton>
                                    </li>
                                </ItemTemplate>
                            </asp:Repeater>
                        </ul>
                    </nav>
                </asp:Panel>
            </div>
        </div>
    </div>

    <!-- Login Prompt Modal -->
    <div class="modal fade" id="loginPromptModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Login Required</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>To purchase medicines or get personalized recommendations, please login or create an account.</p>
                    <div class="d-grid gap-2">
                        <a href="Login.aspx" class="btn btn-primary">Login</a>
                        <a href="Register.aspx" class="btn btn-outline-primary">Create Account</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showLoginPrompt() {
            var modal = new bootstrap.Modal(document.getElementById('loginPromptModal'));
            modal.show();
        }

        function toggleView(viewType) {
            // Toggle between grid and list view
            const gridView = document.getElementById('gridView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'grid') {
                gridView.className = 'row g-4';
                buttons[0].classList.add('active');
            } else {
                gridView.className = 'row g-2';
                buttons[1].classList.add('active');
            }
        }
    </script>
</asp:Content>
