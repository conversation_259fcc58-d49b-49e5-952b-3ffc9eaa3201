using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase.Pharmacist
{
    public partial class InvoiceGeneration : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is pharmacist
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null || (currentUser.Role != "Pharmacist" && currentUser.Role != "Admin"))
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadPendingOrders();
                LoadRecentInvoices();
                LoadInvoiceStatistics();
            }
        }

        private void LoadPendingOrders()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var pendingOrders = db.Orders
                        .Where(o => o.Status == "Processing" && (o.InvoiceGenerated == false || o.InvoiceGenerated == null))
                        .OrderBy(o => o.OrderDate)
                        .Select(o => new
                        {
                            o.OrderId,
                            o.OrderNumber,
                            CustomerName = o.Customer.FirstName + " " + o.Customer.LastName,
                            o.TotalAmount,
                            o.OrderDate,
                            ItemCount = o.OrderItems.Count()
                        })
                        .ToList();

                    // Implementation would bind to GridView
                    // gvPendingOrders.DataSource = pendingOrders;
                    // gvPendingOrders.DataBind();

                    // lblPendingCount.Text = pendingOrders.Count.ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading pending orders");
                ShowErrorMessage("Error loading pending orders. Please try again.");
            }
        }

        private void LoadRecentInvoices()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var recentInvoices = GetRecentInvoices(db);
                    // Implementation would bind to GridView
                    // gvRecentInvoices.DataSource = recentInvoices;
                    // gvRecentInvoices.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading recent invoices");
                ShowErrorMessage("Error loading recent invoices.");
            }
        }

        private void LoadInvoiceStatistics()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var today = DateTime.Today;
                    var thisMonth = new DateTime(today.Year, today.Month, 1);

                    // Calculate statistics
                    var todayInvoices = GetInvoiceCount(db, today, today.AddDays(1));
                    var monthlyInvoices = GetInvoiceCount(db, thisMonth, thisMonth.AddMonths(1));
                    var todayRevenue = GetInvoiceRevenue(db, today, today.AddDays(1));
                    var monthlyRevenue = GetInvoiceRevenue(db, thisMonth, thisMonth.AddMonths(1));

                    // Implementation would display statistics
                    // lblTodayInvoices.Text = todayInvoices.ToString();
                    // lblMonthlyInvoices.Text = monthlyInvoices.ToString();
                    // lblTodayRevenue.Text = todayRevenue.ToString("C");
                    // lblMonthlyRevenue.Text = monthlyRevenue.ToString("C");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading invoice statistics");
                ShowErrorMessage("Error loading statistics.");
            }
        }

        private List<object> GetRecentInvoices(MediEaseContext db)
        {
            // Implementation would return recent invoices
            // For now, return sample data
            return new List<object>
            {
                new
                {
                    InvoiceId = 1,
                    InvoiceNumber = "INV-2024-001",
                    CustomerName = "John Doe",
                    Amount = 125.50m,
                    InvoiceDate = DateTime.Now.AddDays(-1),
                    Status = "Paid"
                },
                new
                {
                    InvoiceId = 2,
                    InvoiceNumber = "INV-2024-002",
                    CustomerName = "Jane Smith",
                    Amount = 89.75m,
                    InvoiceDate = DateTime.Now.AddDays(-2),
                    Status = "Pending"
                }
            };
        }

        private int GetInvoiceCount(MediEaseContext db, DateTime startDate, DateTime endDate)
        {
            // Implementation would count invoices in date range
            return 0; // Placeholder
        }

        private decimal GetInvoiceRevenue(MediEaseContext db, DateTime startDate, DateTime endDate)
        {
            // Implementation would sum invoice amounts in date range
            return 0; // Placeholder
        }

        protected void gvPendingOrders_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "GenerateInvoice")
                {
                    var orderId = Convert.ToInt32(e.CommandArgument);
                    GenerateInvoice(orderId);
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error processing order command");
                ShowErrorMessage("Error processing request. Please try again.");
            }
        }

        protected void gvRecentInvoices_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                var invoiceId = Convert.ToInt32(e.CommandArgument);

                switch (e.CommandName)
                {
                    case "ViewInvoice":
                        ViewInvoice(invoiceId);
                        break;
                    case "DownloadInvoice":
                        DownloadInvoice(invoiceId);
                        break;
                    case "EmailInvoice":
                        EmailInvoice(invoiceId);
                        break;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error processing invoice command");
                ShowErrorMessage("Error processing request. Please try again.");
            }
        }

        private void GenerateInvoice(int orderId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.Find(orderId);
                    if (order == null)
                    {
                        ShowErrorMessage("Order not found.");
                        return;
                    }

                    // Generate invoice
                    var invoice = CreateInvoice(order);
                    
                    // Save invoice to database
                    // db.Invoices.Add(invoice);
                    
                    // Mark order as invoiced
                    order.InvoiceGenerated = true;
                    order.InvoiceDate = DateTime.Now;
                    
                    db.SaveChanges();

                    ShowSuccessMessage($"Invoice generated successfully for Order #{order.OrderNumber}");
                    LoadPendingOrders();
                    LoadRecentInvoices();
                    LoadInvoiceStatistics();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error generating invoice");
                ShowErrorMessage("Error generating invoice. Please try again.");
            }
        }

        private object CreateInvoice(Order order)
        {
            // Implementation would create invoice object
            return new
            {
                InvoiceNumber = GenerateInvoiceNumber(),
                OrderId = order.OrderId,
                CustomerId = order.CustomerId,
                InvoiceDate = DateTime.Now,
                DueDate = DateTime.Now.AddDays(30),
                SubTotal = order.TotalAmount,
                TaxAmount = order.TotalAmount * 0.1m, // 10% tax
                TotalAmount = order.TotalAmount * 1.1m,
                Status = "Generated"
            };
        }

        private string GenerateInvoiceNumber()
        {
            return $"INV-{DateTime.Now:yyyy}-{DateTime.Now:MMdd}-{DateTime.Now:HHmmss}";
        }

        private void ViewInvoice(int invoiceId)
        {
            // Redirect to invoice view page
            Response.Redirect($"ViewInvoice.aspx?id={invoiceId}");
        }

        private void DownloadInvoice(int invoiceId)
        {
            try
            {
                // Implementation would generate PDF and download
                ShowSuccessMessage("Invoice download started.");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error downloading invoice");
                ShowErrorMessage("Error downloading invoice.");
            }
        }

        private void EmailInvoice(int invoiceId)
        {
            try
            {
                // Implementation would email invoice to customer
                ShowSuccessMessage("Invoice emailed successfully.");
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error emailing invoice");
                ShowErrorMessage("Error emailing invoice.");
            }
        }

        protected void btnBulkGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedOrders = GetSelectedOrders();
                if (selectedOrders.Count == 0)
                {
                    ShowErrorMessage("Please select orders to generate invoices for.");
                    return;
                }

                var successCount = 0;
                foreach (var orderId in selectedOrders)
                {
                    try
                    {
                        GenerateInvoice(orderId);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        ErrorLogger.LogError(ex, $"Error generating invoice for order {orderId}");
                    }
                }

                ShowSuccessMessage($"Generated {successCount} invoices successfully.");
                LoadPendingOrders();
                LoadRecentInvoices();
                LoadInvoiceStatistics();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error in bulk invoice generation");
                ShowErrorMessage("Error in bulk generation. Please try again.");
            }
        }

        private List<int> GetSelectedOrders()
        {
            var selectedOrders = new List<int>();
            
            // Implementation would get selected orders from GridView
            // foreach (GridViewRow row in gvPendingOrders.Rows)
            // {
            //     var checkbox = row.FindControl("chkSelect") as CheckBox;
            //     if (checkbox != null && checkbox.Checked)
            //     {
            //         var orderId = Convert.ToInt32(gvPendingOrders.DataKeys[row.RowIndex].Value);
            //         selectedOrders.Add(orderId);
            //     }
            // }
            
            return selectedOrders;
        }

        [WebMethod]
        public static object GetInvoicePreview(int orderId)
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var order = db.Orders.Find(orderId);
                    if (order == null)
                        return new { success = false, message = "Order not found" };

                    // Generate preview data
                    var preview = new
                    {
                        OrderNumber = order.OrderNumber,
                        CustomerName = order.Customer.FirstName + " " + order.Customer.LastName,
                        OrderDate = order.OrderDate.ToString("yyyy-MM-dd"),
                        Items = order.OrderItems.Select(oi => new
                        {
                            MedicineName = oi.Medicine.Name,
                            Quantity = oi.Quantity,
                            UnitPrice = oi.UnitPrice,
                            Total = oi.Quantity * oi.UnitPrice
                        }).ToList(),
                        SubTotal = order.TotalAmount,
                        Tax = order.TotalAmount * 0.1m,
                        Total = order.TotalAmount * 1.1m
                    };

                    return new { success = true, data = preview };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting invoice preview");
                return new { success = false, message = "Error generating preview" };
            }
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }
    }
}
