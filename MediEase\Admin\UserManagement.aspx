<%@ Page Title="User Management" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="UserManagement.aspx.cs" Inherits="MediEase.Admin.UserManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-users me-2"></i>User Management</h2>
                        <p class="text-muted">Manage customers, pharmacists, and admin accounts</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-2"></i>Add New User
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-friends fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalUsers" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Users</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActiveCustomers" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Customers</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-md fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActivePharmacists" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Pharmacists</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-shield fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActiveAdmins" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Admins</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">Role Filter</label>
                                <asp:DropDownList ID="ddlRoleFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlRoleFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Roles</asp:ListItem>
                                    <asp:ListItem Value="Customer">Customers</asp:ListItem>
                                    <asp:ListItem Value="Pharmacist">Pharmacists</asp:ListItem>
                                    <asp:ListItem Value="Admin">Admins</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status Filter</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="">All Status</asp:ListItem>
                                    <asp:ListItem Value="Active">Active</asp:ListItem>
                                    <asp:ListItem Value="Inactive">Inactive</asp:ListItem>
                                    <asp:ListItem Value="Locked">Locked</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Email Verified</label>
                                <asp:DropDownList ID="ddlEmailVerified" runat="server" CssClass="form-select" AutoPostBack="true" OnSelectedIndexChanged="ddlEmailVerified_SelectedIndexChanged">
                                    <asp:ListItem Value="">All</asp:ListItem>
                                    <asp:ListItem Value="True">Verified</asp:ListItem>
                                    <asp:ListItem Value="False">Not Verified</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search by name, email, or phone..."></asp:TextBox>
                                    <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <asp:Button ID="btnRefresh" runat="server" CssClass="btn btn-outline-secondary" Text="Refresh" OnClick="btnRefresh_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users List -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Users</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="toggleView('table')">
                                <i class="fas fa-table"></i> Table View
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="toggleView('card')">
                                <i class="fas fa-th"></i> Card View
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Table View -->
                        <div id="tableView">
                            <div class="table-responsive">
                                <asp:GridView ID="gvUsers" runat="server" CssClass="table table-hover" 
                                    AutoGenerateColumns="false" EmptyDataText="No users found." OnRowCommand="gvUsers_RowCommand">
                                    <Columns>
                                        <asp:TemplateField HeaderText="User Info">
                                            <ItemTemplate>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <img src='<%# GetProfileImage(Eval("ProfileImagePath")) %>' 
                                                             alt="Profile" class="rounded-circle" width="40" height="40" />
                                                    </div>
                                                    <div>
                                                        <strong><%# Eval("FullName") %></strong><br>
                                                        <small class="text-muted">ID: <%# Eval("UserId") %></small>
                                                    </div>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Contact">
                                            <ItemTemplate>
                                                <div>
                                                    <strong><%# Eval("Email") %></strong>
                                                    <%# Convert.ToBoolean(Eval("IsEmailVerified")) ? 
                                                        "<i class=\"fas fa-check-circle text-success ms-1\" title=\"Email Verified\"></i>" : 
                                                        "<i class=\"fas fa-exclamation-circle text-warning ms-1\" title=\"Email Not Verified\"></i>" %>
                                                    <br>
                                                    <small class="text-muted"><%# Eval("PhoneNumber") %></small>
                                                    <%# Convert.ToBoolean(Eval("IsPhoneVerified")) ? 
                                                        "<i class=\"fas fa-check-circle text-success ms-1\" title=\"Phone Verified\"></i>" : 
                                                        "<i class=\"fas fa-exclamation-circle text-warning ms-1\" title=\"Phone Not Verified\"></i>" %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Role">
                                            <ItemTemplate>
                                                <span class="badge bg-<%# GetRoleColor(Eval("Role").ToString()) %> fs-6">
                                                    <%# Eval("Role") %>
                                                </span>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Status">
                                            <ItemTemplate>
                                                <div>
                                                    <%# Convert.ToBoolean(Eval("IsActive")) ? 
                                                        "<span class=\"badge bg-success\">Active</span>" : 
                                                        "<span class=\"badge bg-danger\">Inactive</span>" %>
                                                    <%# Convert.ToBoolean(Eval("IsAccountLocked")) ? 
                                                        "<br><span class=\"badge bg-warning mt-1\">Locked</span>" : "" %>
                                                    <%# Convert.ToBoolean(Eval("TwoFactorEnabled")) ? 
                                                        "<br><span class=\"badge bg-info mt-1\">2FA</span>" : "" %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Last Activity">
                                            <ItemTemplate>
                                                <div>
                                                    <small class="text-muted">
                                                        Last Login:<br>
                                                        <%# Eval("LastLogin") != null ? 
                                                            Convert.ToDateTime(Eval("LastLogin")).ToString("MMM dd, yyyy") : "Never" %>
                                                    </small><br>
                                                    <small class="text-muted">
                                                        Joined: <%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd, yyyy") %>
                                                    </small>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        
                                        <asp:TemplateField HeaderText="Actions">
                                            <ItemTemplate>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info btn-sm view-user" 
                                                        data-user-id='<%# Eval("UserId") %>'>
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm edit-user mt-1" 
                                                        data-user-id='<%# Eval("UserId") %>'>
                                                        <i class="fas fa-edit me-1"></i>Edit
                                                    </button>
                                                    <%# Convert.ToBoolean(Eval("IsActive")) ? 
                                                        "<asp:LinkButton runat=\"server\" CssClass=\"btn btn-outline-warning btn-sm mt-1\" CommandName=\"Deactivate\" CommandArgument=\"" + Eval("UserId") + "\"><i class=\"fas fa-ban me-1\"></i>Deactivate</asp:LinkButton>" :
                                                        "<asp:LinkButton runat=\"server\" CssClass=\"btn btn-outline-success btn-sm mt-1\" CommandName=\"Activate\" CommandArgument=\"" + Eval("UserId") + "\"><i class=\"fas fa-check me-1\"></i>Activate</asp:LinkButton>" %>
                                                    <%# Convert.ToBoolean(Eval("IsAccountLocked")) ? 
                                                        "<asp:LinkButton runat=\"server\" CssClass=\"btn btn-outline-info btn-sm mt-1\" CommandName=\"Unlock\" CommandArgument=\"" + Eval("UserId") + "\"><i class=\"fas fa-unlock me-1\"></i>Unlock</asp:LinkButton>" : "" %>
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>

                        <!-- Card View (Hidden by default) -->
                        <div id="cardView" class="row g-4" style="display: none;">
                            <asp:Repeater ID="rptUsersCard" runat="server">
                                <ItemTemplate>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card user-card h-100 border-0 shadow-sm">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <span class="badge bg-<%# GetRoleColor(Eval("Role").ToString()) %>">
                                                    <%# Eval("Role") %>
                                                </span>
                                                <div>
                                                    <%# Convert.ToBoolean(Eval("IsActive")) ? 
                                                        "<span class=\"badge bg-success\">Active</span>" : 
                                                        "<span class=\"badge bg-danger\">Inactive</span>" %>
                                                </div>
                                            </div>
                                            <div class="card-body text-center">
                                                <img src='<%# GetProfileImage(Eval("ProfileImagePath")) %>' 
                                                     alt="Profile" class="rounded-circle mb-3" width="80" height="80" />
                                                <h6 class="card-title"><%# Eval("FullName") %></h6>
                                                <p class="card-text small text-muted">
                                                    <%# Eval("Email") %>
                                                    <%# Convert.ToBoolean(Eval("IsEmailVerified")) ? 
                                                        "<i class=\"fas fa-check-circle text-success ms-1\"></i>" : 
                                                        "<i class=\"fas fa-exclamation-circle text-warning ms-1\"></i>" %>
                                                </p>
                                                <p class="card-text small text-muted">
                                                    <%# Eval("PhoneNumber") %>
                                                    <%# Convert.ToBoolean(Eval("IsPhoneVerified")) ? 
                                                        "<i class=\"fas fa-check-circle text-success ms-1\"></i>" : 
                                                        "<i class=\"fas fa-exclamation-circle text-warning ms-1\"></i>" %>
                                                </p>
                                                <div class="mb-2">
                                                    <%# Convert.ToBoolean(Eval("IsAccountLocked")) ? 
                                                        "<span class=\"badge bg-warning\">Account Locked</span>" : "" %>
                                                    <%# Convert.ToBoolean(Eval("TwoFactorEnabled")) ? 
                                                        "<span class=\"badge bg-info ms-1\">2FA Enabled</span>" : "" %>
                                                </div>
                                                <small class="text-muted">
                                                    Last Login: <%# Eval("LastLogin") != null ? 
                                                        Convert.ToDateTime(Eval("LastLogin")).ToString("MMM dd, yyyy") : "Never" %>
                                                </small>
                                            </div>
                                            <div class="card-footer bg-transparent">
                                                <div class="d-grid gap-2">
                                                    <div class="btn-group">
                                                        <button class="btn btn-outline-info btn-sm view-user" data-user-id='<%# Eval("UserId") %>'>
                                                            <i class="fas fa-eye me-1"></i>View
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm edit-user" data-user-id='<%# Eval("UserId") %>'>
                                                            <i class="fas fa-edit me-1"></i>Edit
                                                        </button>
                                                    </div>
                                                    <%# Convert.ToBoolean(Eval("IsActive")) ? 
                                                        "<button class=\"btn btn-outline-warning btn-sm deactivate-user\" data-user-id=\"" + Eval("UserId") + "\"><i class=\"fas fa-ban me-1\"></i>Deactivate</button>" :
                                                        "<button class=\"btn btn-outline-success btn-sm activate-user\" data-user-id=\"" + Eval("UserId") + "\"><i class=\"fas fa-check me-1\"></i>Activate</button>" %>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Users Message -->
        <asp:Panel ID="pnlNoUsers" runat="server" Visible="false">
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h4>No users found</h4>
                <p class="text-muted">No users match your current filter criteria.</p>
            </div>
        </asp:Panel>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="addUserForm">
                        <!-- Add user form will be loaded here via AJAX -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">User Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="userDetails">
                        <!-- User details will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div id="userActions">
                        <!-- Action buttons will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .user-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .user-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
    </style>

    <script>
        function toggleView(viewType) {
            const tableView = document.getElementById('tableView');
            const cardView = document.getElementById('cardView');
            const buttons = document.querySelectorAll('.btn-group button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'table') {
                tableView.style.display = 'block';
                cardView.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                tableView.style.display = 'none';
                cardView.style.display = 'flex';
                buttons[1].classList.add('active');
            }
        }

        // View user details
        $(document).on('click', '.view-user', function() {
            const userId = $(this).data('user-id');
            loadUserDetails(userId);
        });

        // Edit user
        $(document).on('click', '.edit-user', function() {
            const userId = $(this).data('user-id');
            loadEditUserForm(userId);
        });

        function loadUserDetails(userId) {
            $.ajax({
                type: 'POST',
                url: 'UserManagement.aspx/GetUserDetails',
                data: JSON.stringify({ userId: userId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#userDetails').html(response.d.html);
                        $('#userActions').html(response.d.actions);
                        $('#userModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading user details.');
                }
            });
        }

        function loadEditUserForm(userId) {
            $.ajax({
                type: 'POST',
                url: 'UserManagement.aspx/GetEditUserForm',
                data: JSON.stringify({ userId: userId }),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#userDetails').html(response.d.html);
                        $('#userModalLabel').text('Edit User');
                        $('#userModal').modal('show');
                    } else {
                        showErrorMessage(response.d.message);
                    }
                },
                error: function() {
                    showErrorMessage('Error loading edit form.');
                }
            });
        }
    </script>
</asp:Content>
