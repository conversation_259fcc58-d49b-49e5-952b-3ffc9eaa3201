using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using MediEase.Models;

namespace MediEase
{
    public partial class _Default : Page
    {
        // Database connection string
        private string ConnectionString
        {
            get
            {
                string connStr = ConfigurationManager.ConnectionStrings["MediEaseConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(connStr))
                {
                    throw new InvalidOperationException("Database connection string 'MediEaseConnectionString' is not configured in Web.config.");
                }
                return connStr;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadPageData();
                SetupUserInterface();
            }
        }

        // Self-contained user methods
        private User GetCurrentUser()
        {
            if (!HttpContext.Current.User.Identity.IsAuthenticated)
                return null;

            try
            {
                var ticket = ((FormsIdentity)HttpContext.Current.User.Identity).Ticket;
                var userData = ticket.UserData.Split('|');

                if (userData.Length >= 3)
                {
                    return new User
                    {
                        UserId = int.Parse(userData[0]),
                        Role = userData[1],
                        FirstName = userData[2],
                        Email = ticket.Name
                    };
                }
            }
            catch
            {
                // Return null if unable to parse user data
            }

            return null;
        }

        private void LoadPageData()
        {
            try
            {
                LoadFeaturedMedicines();
                LoadStatistics();
            }
            catch (Exception ex)
            {
                LogError(ex, "Error loading home page data");
                // Show user-friendly message
                var master = Master as SiteMaster;
                master?.ShowErrorMessage("Some content may not be available at the moment. Please try refreshing the page.");
            }
        }

        private void LoadFeaturedMedicines()
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        SELECT TOP 8
                            MedicineId, Name, GenericName, Strength, PackSize, Unit, Price,
                            DiscountAmount, DiscountPercentage, StockQuantity, ImagePath,
                            (Price - CASE
                                WHEN DiscountAmount > 0 THEN DiscountAmount
                                WHEN DiscountPercentage > 0 THEN Price * (DiscountPercentage / 100)
                                ELSE 0
                            END) AS FinalPrice
                        FROM Medicines
                        WHERE IsActive = 1 AND IsFeatured = 1 AND StockQuantity > 0
                        ORDER BY PurchaseCount DESC, AverageRating DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            var featuredMedicines = new System.Collections.Generic.List<object>();

                            while (reader.Read())
                            {
                                featuredMedicines.Add(new
                                {
                                    MedicineId = (int)reader["MedicineId"],
                                    Name = reader["Name"].ToString(),
                                    GenericName = reader["GenericName"].ToString(),
                                    Strength = reader["Strength"].ToString(),
                                    PackSize = (int)reader["PackSize"],
                                    Unit = reader["Unit"].ToString(),
                                    Price = (decimal)reader["Price"],
                                    DiscountAmount = (decimal)reader["DiscountAmount"],
                                    DiscountPercentage = (decimal)reader["DiscountPercentage"],
                                    StockQuantity = (int)reader["StockQuantity"],
                                    ImagePath = reader["ImagePath"].ToString(),
                                    FinalPrice = (decimal)reader["FinalPrice"]
                                });
                            }

                            rptFeaturedMedicines.DataSource = featuredMedicines;
                            rptFeaturedMedicines.DataBind();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, "Error loading featured medicines");
                // Continue without featured medicines
            }
        }

        private void LoadStatistics()
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();

                    // Get total medicines count
                    string medicinesQuery = "SELECT COUNT(*) FROM Medicines WHERE IsActive = 1";
                    using (var command = new SqlCommand(medicinesQuery, connection))
                    {
                        var totalMedicines = (int)command.ExecuteScalar();
                        litTotalMedicines.Text = totalMedicines > 0 ? $"{totalMedicines}+" : "500+";
                    }

                    // Get total customers count
                    string customersQuery = "SELECT COUNT(*) FROM Users WHERE Role = 'Customer' AND IsActive = 1";
                    using (var command = new SqlCommand(customersQuery, connection))
                    {
                        var totalCustomers = (int)command.ExecuteScalar();
                        litTotalCustomers.Text = totalCustomers > 0 ? $"{totalCustomers}+" : "1000+";
                    }

                    // Get total orders count
                    string ordersQuery = "SELECT COUNT(*) FROM Orders";
                    using (var command = new SqlCommand(ordersQuery, connection))
                    {
                        var totalOrders = (int)command.ExecuteScalar();
                        litTotalOrders.Text = totalOrders > 0 ? $"{totalOrders}+" : "5000+";
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, "Error loading statistics");
                // Keep default values if database query fails
                litTotalMedicines.Text = "500+";
                litTotalCustomers.Text = "1000+";
                litTotalOrders.Text = "5000+";
            }
        }

        private void SetupUserInterface()
        {
            var currentUser = GetCurrentUser();

            if (currentUser != null)
            {
                // User is logged in
                phGuestCTA.Visible = false;
                phUserCTA.Visible = true;

                // Log user activity
                LogUserActivity("Visited home page", currentUser.UserId);
            }
            else
            {
                // Guest user
                phGuestCTA.Visible = true;
                phUserCTA.Visible = false;
            }

            // Set page metadata
            SetPageMetadata();
        }

        // Self-contained logging methods
        private void LogError(Exception ex, string message)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {message} - {ex.Message}\r\n{ex.StackTrace}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void LogUserActivity(string activity, int userId)
        {
            try
            {
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] USER ACTIVITY [UserId: {userId}]: {activity}\r\n";
                var logPath = Server.MapPath("~/App_Data/Logs/");
                if (!System.IO.Directory.Exists(logPath))
                    System.IO.Directory.CreateDirectory(logPath);

                System.IO.File.AppendAllText(System.IO.Path.Combine(logPath, $"activity_{DateTime.Now:yyyyMMdd}.log"), logMessage);
            }
            catch
            {
                // Silent fail for logging
            }
        }

        private void SetPageMetadata()
        {
            // Set page title and meta tags for SEO
            Page.Title = "MediEase - Smart Pharmacy Management System";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("MediEase is a smart pharmacy management system with AI-powered features. Order medicines online, upload prescriptions, and get expert consultation with home delivery.");
                master.AddMetaKeywords("pharmacy, medicines, online pharmacy, prescription, AI healthcare, medicine delivery, health management");
            }
        }

        protected void btnGetStarted_Click(object sender, EventArgs e)
        {
            var currentUser = GetCurrentUser();

            if (currentUser != null)
            {
                // User is already logged in, redirect based on role
                switch (currentUser.Role.ToLower())
                {
                    case "admin":
                        Response.Redirect("~/Admin/Dashboard.aspx");
                        break;
                    case "pharmacist":
                        Response.Redirect("~/Pharmacist/Dashboard.aspx");
                        break;
                    case "customer":
                        Response.Redirect("~/Customer/Dashboard.aspx");
                        break;
                    default:
                        Response.Redirect("~/Medicines.aspx");
                        break;
                }
            }
            else
            {
                // Guest user, redirect to registration
                Response.Redirect("~/Register.aspx");
            }
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Add structured data for SEO
            AddStructuredData();
        }

        private void AddStructuredData()
        {
            var structuredData = @"
            <script type=""application/ld+json"">
            {
                ""@context"": ""https://schema.org"",
                ""@type"": ""Pharmacy"",
                ""name"": ""MediEase"",
                ""description"": ""Smart Pharmacy Management System with AI-powered features"",
                ""url"": """ + Request.Url.GetLeftPart(UriPartial.Authority) + @""",
                ""telephone"": ""******-123-4567"",
                ""address"": {
                    ""@type"": ""PostalAddress"",
                    ""streetAddress"": ""123 Pharmacy Street"",
                    ""addressLocality"": ""Health City"",
                    ""addressRegion"": ""HC"",
                    ""postalCode"": ""12345"",
                    ""addressCountry"": ""US""
                },
                ""openingHours"": ""Mo-Su 00:00-23:59"",
                ""priceRange"": ""$"",
                ""hasOfferCatalog"": {
                    ""@type"": ""OfferCatalog"",
                    ""name"": ""Medicine Catalog"",
                    ""itemListElement"": [
                        {
                            ""@type"": ""Offer"",
                            ""itemOffered"": {
                                ""@type"": ""Product"",
                                ""name"": ""Prescription Medicines""
                            }
                        },
                        {
                            ""@type"": ""Offer"",
                            ""itemOffered"": {
                                ""@type"": ""Product"",
                                ""name"": ""Over-the-Counter Medicines""
                            }
                        }
                    ]
                }
            }
            </script>";

            Page.Header.Controls.Add(new LiteralControl(structuredData));
        }
    }
}
