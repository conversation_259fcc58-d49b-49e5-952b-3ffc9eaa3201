using System;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Utilities;

namespace MediEase
{
    public partial class _Default : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadPageData();
                SetupUserInterface();
            }
        }

        private void LoadPageData()
        {
            try
            {
                LoadFeaturedMedicines();
                LoadStatistics();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading home page data");
                // Show user-friendly message
                var master = Master as SiteMaster;
                master?.ShowErrorMessage("Some content may not be available at the moment. Please try refreshing the page.");
            }
        }

        private void LoadFeaturedMedicines()
        {
            using (var db = new MediEaseContext())
            {
                var featuredMedicines = db.Medicines
                    .Where(m => m.IsActive && m.IsFeatured && m.StockQuantity > 0)
                    .OrderByDescending(m => m.PurchaseCount)
                    .ThenByDescending(m => m.AverageRating)
                    .Take(8)
                    .Select(m => new
                    {
                        m.MedicineId,
                        m.Name,
                        m.GenericName,
                        m.Strength,
                        m.PackSize,
                        m.Unit,
                        m.Price,
                        m.DiscountAmount,
                        m.DiscountPercentage,
                        m.StockQuantity,
                        m.ImagePath,
                        FinalPrice = m.Price - (m.DiscountAmount > 0 ? m.DiscountAmount : 
                                   m.DiscountPercentage > 0 ? m.Price * (m.DiscountPercentage / 100) : 0)
                    })
                    .ToList();

                rptFeaturedMedicines.DataSource = featuredMedicines;
                rptFeaturedMedicines.DataBind();
            }
        }

        private void LoadStatistics()
        {
            using (var db = new MediEaseContext())
            {
                try
                {
                    // Get total medicines count
                    var totalMedicines = db.Medicines.Count(m => m.IsActive);
                    litTotalMedicines.Text = totalMedicines > 0 ? $"{totalMedicines}+" : "500+";

                    // Get total customers count
                    var totalCustomers = db.Users.Count(u => u.Role == "Customer" && u.IsActive);
                    litTotalCustomers.Text = totalCustomers > 0 ? $"{totalCustomers}+" : "1000+";

                    // Get total orders count
                    var totalOrders = db.Orders.Count();
                    litTotalOrders.Text = totalOrders > 0 ? $"{totalOrders}+" : "5000+";
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error loading statistics");
                    // Keep default values if database query fails
                }
            }
        }

        private void SetupUserInterface()
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            
            if (currentUser != null)
            {
                // User is logged in
                phGuestCTA.Visible = false;
                phUserCTA.Visible = true;
                
                // Log user activity
                ErrorLogger.LogUserActivity("Visited home page", currentUser.UserId);
            }
            else
            {
                // Guest user
                phGuestCTA.Visible = true;
                phUserCTA.Visible = false;
            }

            // Set page metadata
            SetPageMetadata();
        }

        private void SetPageMetadata()
        {
            // Set page title and meta tags for SEO
            Page.Title = "MediEase - Smart Pharmacy Management System";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("MediEase is a smart pharmacy management system with AI-powered features. Order medicines online, upload prescriptions, and get expert consultation with home delivery.");
                master.AddMetaKeywords("pharmacy, medicines, online pharmacy, prescription, AI healthcare, medicine delivery, health management");
            }
        }

        protected void btnGetStarted_Click(object sender, EventArgs e)
        {
            var currentUser = SecurityHelper.GetCurrentUser();
            
            if (currentUser != null)
            {
                // User is already logged in, redirect based on role
                switch (currentUser.Role.ToLower())
                {
                    case "admin":
                        Response.Redirect("~/Admin/Dashboard.aspx");
                        break;
                    case "pharmacist":
                        Response.Redirect("~/Pharmacist/Dashboard.aspx");
                        break;
                    case "customer":
                        Response.Redirect("~/Customer/Dashboard.aspx");
                        break;
                    default:
                        Response.Redirect("~/Medicines.aspx");
                        break;
                }
            }
            else
            {
                // Guest user, redirect to registration
                Response.Redirect("~/Register.aspx");
            }
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Add structured data for SEO
            AddStructuredData();
        }

        private void AddStructuredData()
        {
            var structuredData = @"
            <script type=""application/ld+json"">
            {
                ""@context"": ""https://schema.org"",
                ""@type"": ""Pharmacy"",
                ""name"": ""MediEase"",
                ""description"": ""Smart Pharmacy Management System with AI-powered features"",
                ""url"": """ + Request.Url.GetLeftPart(UriPartial.Authority) + @""",
                ""telephone"": ""+1-************"",
                ""address"": {
                    ""@type"": ""PostalAddress"",
                    ""streetAddress"": ""123 Pharmacy Street"",
                    ""addressLocality"": ""Health City"",
                    ""addressRegion"": ""HC"",
                    ""postalCode"": ""12345"",
                    ""addressCountry"": ""US""
                },
                ""openingHours"": ""Mo-Su 00:00-23:59"",
                ""priceRange"": ""$"",
                ""hasOfferCatalog"": {
                    ""@type"": ""OfferCatalog"",
                    ""name"": ""Medicine Catalog"",
                    ""itemListElement"": [
                        {
                            ""@type"": ""Offer"",
                            ""itemOffered"": {
                                ""@type"": ""Product"",
                                ""name"": ""Prescription Medicines""
                            }
                        },
                        {
                            ""@type"": ""Offer"",
                            ""itemOffered"": {
                                ""@type"": ""Product"",
                                ""name"": ""Over-the-Counter Medicines""
                            }
                        }
                    ]
                }
            }
            </script>";

            Page.Header.Controls.Add(new LiteralControl(structuredData));
        }
    }
}
