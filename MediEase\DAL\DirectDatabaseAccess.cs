using MediEase.Models;
using MediEase.Utilities;
using System;
using System.Configuration;
using System.Data.SqlClient;

namespace MediEase.DAL
{
    public static class DirectDatabaseAccess
    {
        private static string ConnectionString
        {
            get
            {
                // Ensure the connection string is configured in Web.config
                string connStr = ConfigurationManager.ConnectionStrings["MediEaseConnectionString"]?.ConnectionString;
                if (string.IsNullOrEmpty(connStr))
                {
                    throw new InvalidOperationException("Database connection string 'MediEaseConnectionString' is not configured in Web.config.");
                }
                return connStr;
            }
        }

        public static bool EmailExists(string email)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Email = @Email";
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);
                        int count = (int)command.ExecuteScalar();
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, $"Error checking email existence: {email}");
                throw;
            }
        }

        public static int InsertUser(User user)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Users (
                            Email, PasswordHash, FirstName, LastName, PhoneNumber, Role, IsActive,
                            IsEmailVerified, EmailVerificationToken, CreatedDate, Address, City, State,
                            PostalCode, Country, DateOfBirth, Gender, LoyaltyPoints
                        )
                        OUTPUT INSERTED.UserId
                        VALUES (
                            @Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber, @Role, @IsActive,
                            @IsEmailVerified, @EmailVerificationToken, @CreatedDate, @Address, @City, @State,
                            @PostalCode, @Country, @DateOfBirth, @Gender, @LoyaltyPoints
                        )";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", (object)user.Email ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PasswordHash", (object)user.PasswordHash ?? DBNull.Value);
                        command.Parameters.AddWithValue("@FirstName", (object)user.FirstName ?? DBNull.Value);
                        command.Parameters.AddWithValue("@LastName", (object)user.LastName ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PhoneNumber", (object)user.PhoneNumber ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Role", (object)user.Role ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", user.IsActive);
                        command.Parameters.AddWithValue("@IsEmailVerified", user.IsEmailVerified);
                        command.Parameters.AddWithValue("@EmailVerificationToken", (object)user.EmailVerificationToken ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedDate", user.CreatedDate);
                        command.Parameters.AddWithValue("@Address", (object)user.Address ?? DBNull.Value);
                        command.Parameters.AddWithValue("@City", (object)user.City ?? DBNull.Value);
                        command.Parameters.AddWithValue("@State", (object)user.State ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PostalCode", (object)user.PostalCode ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Country", (object)user.Country ?? DBNull.Value);
                        command.Parameters.AddWithValue("@DateOfBirth", (object)user.DateOfBirth ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Gender", (object)user.Gender ?? DBNull.Value);
                        command.Parameters.AddWithValue("@LoyaltyPoints", 0); // Initialize to 0, updated separately

                        int newUserId = (int)command.ExecuteScalar();
                        return newUserId;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, $"Error inserting user: {user.Email}");
                throw;
            }
        }

        public static void InsertLoyaltyPoints(int userId, int points, string reason)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        UPDATE Users
                        SET LoyaltyPoints = LoyaltyPoints + @Points
                        WHERE UserId = @UserId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@Points", points);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, $"Error inserting loyalty points for user {userId}: {reason}");
                throw;
            }
        }

        /// <summary>
        /// Get user by email
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>User object or null</returns>
        public static User GetUserByEmail(string email)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        SELECT UserId, FirstName, LastName, Email, PasswordHash, PhoneNumber,
                               Address, City, State, PostalCode, Country, Role, DateOfBirth,
                               Gender, IsActive, LoyaltyPoints, CreatedDate, IsEmailVerified,
                               LastLoginDate, EmailVerificationToken, PasswordResetToken
                        FROM Users
                        WHERE Email = @Email AND IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Email", email);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new User
                                {
                                    UserId = (int)reader["UserId"],
                                    FirstName = reader["FirstName"] as string,
                                    LastName = reader["LastName"] as string,
                                    Email = reader["Email"] as string,
                                    PasswordHash = reader["PasswordHash"] as string,
                                    PhoneNumber = reader["PhoneNumber"] as string,
                                    Address = reader["Address"] as string,
                                    City = reader["City"] as string,
                                    State = reader["State"] as string,
                                    PostalCode = reader["PostalCode"] as string,
                                    Country = reader["Country"] as string,
                                    Role = (reader["Role"] as string) ?? "Customer",
                                    DateOfBirth = reader["DateOfBirth"] as DateTime?,
                                    Gender = reader["Gender"] as string,
                                    IsActive = (bool)reader["IsActive"],
                                    LoyaltyPoints = reader["LoyaltyPoints"] != DBNull.Value ? (int)reader["LoyaltyPoints"] : 0,
                                    CreatedDate = (DateTime)reader["CreatedDate"],
                                    IsEmailVerified = (bool)reader["IsEmailVerified"],
                                    LastLogin = reader["LastLoginDate"] as DateTime?,
                                    EmailVerificationToken = reader["EmailVerificationToken"] as string,
                                    PasswordResetToken = reader["PasswordResetToken"] as string,
                                    // Set default values for properties not in database
                                    FailedLoginAttempts = 0,
                                    IsPhoneVerified = false
                                };
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "DirectDatabaseAccess.GetUserByEmail");
                throw;
            }
        }

        /// <summary>
        /// Update user login information
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="lastLogin">Last login time</param>
        /// <param name="failedAttempts">Failed login attempts (ignored - not in DB)</param>
        /// <param name="lockedUntil">Account locked until (ignored - not in DB)</param>
        public static void UpdateUserLogin(int userId, DateTime? lastLogin = null, int? failedAttempts = null, DateTime? lockedUntil = null)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    string query = @"
                        UPDATE Users SET
                            LastLoginDate = COALESCE(@LastLogin, LastLoginDate),
                            ModifiedDate = GETDATE()
                        WHERE UserId = @UserId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);
                        command.Parameters.AddWithValue("@LastLogin", lastLogin ?? (object)DBNull.Value);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "DirectDatabaseAccess.UpdateUserLogin");
                throw;
            }
        }


    }
}