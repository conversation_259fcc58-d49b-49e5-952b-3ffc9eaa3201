<%@ Page Title="Reset Password" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ResetPassword.aspx.cs" Inherits="MediEase.ResetPassword" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-lock me-2"></i>Create New Password
                        </h3>
                        <p class="mb-0 mt-2">Enter your new password below</p>
                    </div>
                    <div class="card-body p-4">
                        <!-- Success Panel -->
                        <asp:Panel ID="pnlSuccess" runat="server" CssClass="alert alert-success" Visible="false">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Password Reset Successful!</strong> 
                            <p class="mb-0 mt-2">Your password has been updated successfully. You can now log in with your new password.</p>
                        </asp:Panel>

                        <!-- Error Panel -->
                        <asp:Panel ID="pnlError" runat="server" CssClass="alert alert-danger" Visible="false">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <asp:Label ID="lblError" runat="server"></asp:Label>
                        </asp:Panel>

                        <!-- Reset Form -->
                        <asp:Panel ID="pnlResetForm" runat="server">
                            <div class="mb-3">
                                <label for="txtNewPassword" class="form-label">New Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <asp:TextBox ID="txtNewPassword" runat="server" CssClass="form-control" 
                                        TextMode="Password" placeholder="Enter new password" Required="true"></asp:TextBox>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('txtNewPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvNewPassword" runat="server" ControlToValidate="txtNewPassword" 
                                    ErrorMessage="New password is required" CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revNewPassword" runat="server" ControlToValidate="txtNewPassword"
                                    ValidationExpression="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
                                    ErrorMessage="Password must be at least 8 characters with uppercase, lowercase, number, and special character" 
                                    CssClass="text-danger small" Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>

                            <div class="mb-3">
                                <label for="txtConfirmPassword" class="form-label">Confirm New Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <asp:TextBox ID="txtConfirmPassword" runat="server" CssClass="form-control" 
                                        TextMode="Password" placeholder="Confirm new password" Required="true"></asp:TextBox>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('txtConfirmPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvConfirmPassword" runat="server" ControlToValidate="txtConfirmPassword" 
                                    ErrorMessage="Please confirm your password" CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:CompareValidator ID="cvPasswords" runat="server" ControlToValidate="txtConfirmPassword" 
                                    ControlToCompare="txtNewPassword" ErrorMessage="Passwords do not match" 
                                    CssClass="text-danger small" Display="Dynamic"></asp:CompareValidator>
                            </div>

                            <!-- Password Requirements -->
                            <div class="mb-4">
                                <small class="text-muted">
                                    <strong>Password Requirements:</strong>
                                    <ul class="small mt-1 mb-0">
                                        <li>At least 8 characters long</li>
                                        <li>Include uppercase and lowercase letters</li>
                                        <li>Include at least one number</li>
                                        <li>Include at least one special character (@$!%*?&)</li>
                                    </ul>
                                </small>
                            </div>

                            <div class="d-grid mb-3">
                                <asp:Button ID="btnUpdatePassword" runat="server" CssClass="btn btn-success btn-lg" 
                                    Text="Update Password" OnClick="btnUpdatePassword_Click" />
                            </div>
                        </asp:Panel>

                        <!-- Success Actions -->
                        <asp:Panel ID="pnlSuccessActions" runat="server" Visible="false">
                            <div class="text-center">
                                <div class="mb-3">
                                    <i class="fas fa-check-circle fa-3x text-success"></i>
                                </div>
                                <p class="text-muted mb-3">Your password has been successfully updated</p>
                                <div class="d-grid">
                                    <a href="~/Login.aspx" runat="server" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login Now
                                    </a>
                                </div>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="card mt-4 border-0">
                    <div class="card-body text-center">
                        <h6 class="text-muted">
                            <i class="fas fa-shield-alt me-2"></i>Security Notice
                        </h6>
                        <p class="small text-muted mb-0">
                            For your security, this password reset link can only be used once and will expire after 24 hours.
                            If you didn't request this password reset, please contact our support team immediately.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function togglePassword(inputId, button) {
            const input = document.getElementById('<%= txtNewPassword.ClientID %>');
            const confirmInput = document.getElementById('<%= txtConfirmPassword.ClientID %>');
            const icon = button.querySelector('i');
            
            let targetInput;
            if (inputId === 'txtNewPassword') {
                targetInput = input;
            } else {
                targetInput = confirmInput;
            }
            
            if (targetInput.type === 'password') {
                targetInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                targetInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Password strength indicator
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('<%= txtNewPassword.ClientID %>');
            
            passwordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value);
            });
        });

        function checkPasswordStrength(password) {
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[@$!%*?&]/.test(password)
            };
            
            const strength = Object.values(requirements).filter(Boolean).length;
            
            // You could add visual feedback here
            // For example, change border color based on strength
            const input = document.getElementById('<%= txtNewPassword.ClientID %>');
            if (strength < 3) {
                input.classList.remove('is-valid');
                input.classList.add('is-invalid');
            } else if (strength < 5) {
                input.classList.remove('is-invalid', 'is-valid');
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        }
    </script>
</asp:Content>
