/* MediEase Custom Styles */

/* Global Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    padding-top: 76px; /* Account for fixed navbar */
}

/* Navigation Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
    border: none;
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Medicine Cards */
.medicine-card {
    transition: var(--transition);
    height: 100%;
}

.medicine-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15);
}

.medicine-image {
    height: 200px;
    object-fit: cover;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.price-tag {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--success-color);
}

.original-price {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: bold;
}

/* Stock Status */
.stock-status {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
}

.stock-in {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.stock-low {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.stock-out {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Chatbot Styles */
.chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chatbot-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: var(--transition);
    font-size: 1.5rem;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.chatbot-window {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chatbot-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chatbot-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: #f8f9fa;
}

.bot-message, .user-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    max-width: 80%;
}

.bot-message {
    background: white;
    border: 1px solid #dee2e6;
    margin-right: auto;
}

.user-message {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.chatbot-input {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    background: white;
}

/* Dashboard Styles */
.dashboard-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
}

.dashboard-card .card-body {
    padding: 2rem;
}

.dashboard-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.dashboard-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
}

.dashboard-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Prescription Upload */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.spinner-border-lg {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem 0;
    }
    
    .chatbot-window {
        width: 300px;
        height: 400px;
    }
    
    .dashboard-card .card-body {
        padding: 1.5rem;
    }
    
    .dashboard-number {
        font-size: 2rem;
    }
    
    .dashboard-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    body {
        padding-top: 70px;
    }
    
    .chatbot-window {
        width: calc(100vw - 40px);
        right: -10px;
    }
    
    .medicine-image {
        height: 150px;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .navbar,
    .chatbot-container,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        padding-top: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
.shadow { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175); }
