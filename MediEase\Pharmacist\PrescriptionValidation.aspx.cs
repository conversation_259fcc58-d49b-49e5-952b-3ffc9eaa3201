using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;
using Prescription = MediEase.Models.Prescription;

namespace MediEase.Pharmacist
{
    public partial class PrescriptionValidation : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a pharmacist
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null)
            {
                Response.Redirect("~/Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.PathAndQuery));
                return;
            }

            if (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin())
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadPrescriptions();
                UpdatePendingCount();
            }
        }

        private void LoadPrescriptions()
        {
            try
            {
                var statusFilter = ddlStatusFilter.SelectedValue;
                var priorityFilter = ddlPriorityFilter.SelectedValue;
                var searchTerm = txtSearch.Text.Trim();

                using (var db = new MediEaseContext())
                {
                    var query = db.Prescriptions.AsQueryable();

                    // Apply status filter
                    if (!string.IsNullOrEmpty(statusFilter))
                    {
                        query = query.Where(p => p.Status == statusFilter);
                    }

                    // Apply priority filter
                    if (!string.IsNullOrEmpty(priorityFilter))
                    {
                        var isEmergency = priorityFilter == "Emergency";
                        var isUrgent = priorityFilter == "Urgent";
                        
                        if (isEmergency)
                            query = query.Where(p => p.IsEmergency);
                        else if (isUrgent)
                            query = query.Where(p => !p.IsEmergency && p.RequiresConsultation);
                        else
                            query = query.Where(p => !p.IsEmergency && !p.RequiresConsultation);
                    }

                    // Apply search filter
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(p =>
                            p.Patient.FirstName.Contains(searchTerm) ||
                            p.Patient.LastName.Contains(searchTerm) ||
                            p.DoctorName.Contains(searchTerm) ||
                            p.PrescriptionNumber.Contains(searchTerm));
                    }

                    var prescriptions = query
                        .OrderByDescending(p => p.IsEmergency)
                        .ThenByDescending(p => p.RequiresConsultation)
                        .ThenBy(p => p.CreatedDate)
                        .Select(p => new
                        {
                            p.PrescriptionId,
                            p.PrescriptionNumber,
                            PatientName = p.Patient.FirstName + " " + p.Patient.LastName,
                            PatientDOB = p.Patient.DateOfBirth,
                            PatientPhone = p.Patient.PhoneNumber,
                            p.DoctorName,
                            p.DoctorLicense,
                            p.HospitalClinic,
                            p.PrescriptionDate,
                            p.ValidUntil,
                            Diagnosis = p.PatientDiagnosis,
                            p.Status,
                            p.IsEmergency,
                            p.RequiresConsultation,
                            UploadDate = p.CreatedDate,
                            Priority = p.IsEmergency ? "Emergency" :
                                      p.RequiresConsultation ? "Urgent" : "Normal"
                        })
                        .ToList();

                    if (prescriptions.Any())
                    {
                        gvPrescriptions.DataSource = prescriptions;
                        gvPrescriptions.DataBind();
                        
                        rptPrescriptionsCard.DataSource = prescriptions;
                        rptPrescriptionsCard.DataBind();

                        pnlNoPrescriptions.Visible = false;
                    }
                    else
                    {
                        gvPrescriptions.DataSource = null;
                        gvPrescriptions.DataBind();
                        
                        rptPrescriptionsCard.DataSource = null;
                        rptPrescriptionsCard.DataBind();

                        pnlNoPrescriptions.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error loading prescriptions for validation");
                ShowErrorMessage("Error loading prescriptions. Please try again.");
            }
        }

        private void UpdatePendingCount()
        {
            try
            {
                using (var db = new MediEaseContext())
                {
                    var pendingCount = db.Prescriptions.Count(p => p.Status == "Pending");
                    lblPendingCount.Text = pendingCount.ToString();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating pending count");
            }
        }

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadPrescriptions();
        }

        protected void ddlPriorityFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadPrescriptions();
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadPrescriptions();
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            // Clear filters and reload
            ddlStatusFilter.SelectedValue = "Pending";
            ddlPriorityFilter.SelectedValue = "";
            txtSearch.Text = "";
            LoadPrescriptions();
            UpdatePendingCount();
        }

        protected void gvPrescriptions_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (int.TryParse(e.CommandArgument.ToString(), out int prescriptionId))
            {
                switch (e.CommandName)
                {
                    case "Validate":
                        ValidatePrescriptionInternal(prescriptionId);
                        break;
                    case "Reject":
                        // For GridView, we'll use a simple rejection without reason
                        RejectPrescriptionInternal(prescriptionId, "Rejected by pharmacist");
                        break;
                }
            }
        }

        private void ValidatePrescriptionInternal(int prescriptionId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var prescription = db.Prescriptions.Find(prescriptionId);
                    if (prescription != null && prescription.Status == "Pending")
                    {
                        prescription.Status = "Verified";
                        prescription.VerifiedBy = currentUser.UserId;
                        prescription.VerificationDate = DateTime.Now;
                        prescription.VerificationNotes = "Validated by pharmacist";

                        db.SaveChanges();

                        ErrorLogger.LogUserActivity($"Validated prescription {prescription.PrescriptionNumber}", currentUser.UserId);
                        ShowSuccessMessage("Prescription validated successfully!");
                        
                        LoadPrescriptions();
                        UpdatePendingCount();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error validating prescription");
                ShowErrorMessage("Error validating prescription. Please try again.");
            }
        }

        private void RejectPrescriptionInternal(int prescriptionId, string reason)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null) return;

                using (var db = new MediEaseContext())
                {
                    var prescription = db.Prescriptions.Find(prescriptionId);
                    if (prescription != null && prescription.Status == "Pending")
                    {
                        prescription.Status = "Rejected";
                        prescription.VerifiedBy = currentUser.UserId;
                        prescription.VerificationDate = DateTime.Now;
                        prescription.RejectionReason = reason;

                        db.SaveChanges();

                        ErrorLogger.LogUserActivity($"Rejected prescription {prescription.PrescriptionNumber}: {reason}", currentUser.UserId);
                        ShowSuccessMessage("Prescription rejected successfully!");
                        
                        LoadPrescriptions();
                        UpdatePendingCount();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error rejecting prescription");
                ShowErrorMessage("Error rejecting prescription. Please try again.");
            }
        }

        [WebMethod]
        public static object GetPrescriptionDetails(int prescriptionId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin()))
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var prescription = db.Prescriptions.Find(prescriptionId);
                    if (prescription == null)
                    {
                        return new { success = false, message = "Prescription not found" };
                    }

                    var html = GeneratePrescriptionDetailsHtml(prescription);
                    return new { success = true, html = html };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error getting prescription details");
                return new { success = false, message = "Error loading prescription details" };
            }
        }

        [WebMethod]
        public static object ValidatePrescription(int prescriptionId)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin()))
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var prescription = db.Prescriptions.Find(prescriptionId);
                    if (prescription == null)
                    {
                        return new { success = false, message = "Prescription not found" };
                    }

                    if (prescription.Status != "Pending")
                    {
                        return new { success = false, message = "Prescription is not pending validation" };
                    }

                    prescription.Status = "Verified";
                    prescription.VerifiedBy = currentUser.UserId;
                    prescription.VerificationDate = DateTime.Now;
                    prescription.VerificationNotes = "Validated by pharmacist";

                    db.SaveChanges();

                    ErrorLogger.LogUserActivity($"Validated prescription {prescription.PrescriptionNumber}", currentUser.UserId);
                    return new { success = true, message = "Prescription validated successfully" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error validating prescription");
                return new { success = false, message = "Error validating prescription" };
            }
        }

        [WebMethod]
        public static object RejectPrescription(int prescriptionId, string reason)
        {
            try
            {
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser == null || (!SecurityHelper.IsPharmacist() && !SecurityHelper.IsAdmin()))
                {
                    return new { success = false, message = "Unauthorized access" };
                }

                using (var db = new MediEaseContext())
                {
                    var prescription = db.Prescriptions.Find(prescriptionId);
                    if (prescription == null)
                    {
                        return new { success = false, message = "Prescription not found" };
                    }

                    if (prescription.Status != "Pending")
                    {
                        return new { success = false, message = "Prescription is not pending validation" };
                    }

                    prescription.Status = "Rejected";
                    prescription.VerifiedBy = currentUser.UserId;
                    prescription.VerificationDate = DateTime.Now;
                    prescription.RejectionReason = reason;

                    db.SaveChanges();

                    ErrorLogger.LogUserActivity($"Rejected prescription {prescription.PrescriptionNumber}: {reason}", currentUser.UserId);
                    return new { success = true, message = "Prescription rejected successfully" };
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error rejecting prescription");
                return new { success = false, message = "Error rejecting prescription" };
            }
        }

        private static string GeneratePrescriptionDetailsHtml(Prescription prescription)
        {
            return $@"
                <div class='row'>
                    <div class='col-md-6'>
                        <h6>Patient Information</h6>
                        <p><strong>Name:</strong> {prescription.Patient.FirstName} {prescription.Patient.LastName}</p>
                        <p><strong>Date of Birth:</strong> {prescription.Patient.DateOfBirth:MM/dd/yyyy}</p>
                        <p><strong>Allergies:</strong> {prescription.PatientAllergies ?? "None specified"}</p>
                        <p><strong>Current Medications:</strong> {prescription.CurrentMedications ?? "None specified"}</p>
                    </div>
                    <div class='col-md-6'>
                        <h6>Doctor Information</h6>
                        <p><strong>Name:</strong> {prescription.DoctorName}</p>
                        <p><strong>License:</strong> {prescription.DoctorLicense}</p>
                        <p><strong>Hospital/Clinic:</strong> {prescription.HospitalClinic}</p>
                        <p><strong>Phone:</strong> {prescription.DoctorPhone}</p>
                    </div>
                </div>
                <div class='row mt-3'>
                    <div class='col-12'>
                        <h6>Prescription Details</h6>
                        <p><strong>Prescription Date:</strong> {prescription.PrescriptionDate:MM/dd/yyyy}</p>
                        <p><strong>Valid Until:</strong> {prescription.ValidUntil:MM/dd/yyyy}</p>
                        <p><strong>Diagnosis:</strong> {prescription.PatientDiagnosis ?? "Not specified"}</p>
                        <p><strong>Symptoms:</strong> {prescription.PatientSymptoms ?? "Not specified"}</p>
                        <p><strong>Special Instructions:</strong> {prescription.SpecialInstructions ?? "None"}</p>
                    </div>
                </div>
                {(prescription.IsEmergency ? "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle me-2'></i>EMERGENCY PRESCRIPTION</div>" : "")}
                {(prescription.RequiresConsultation ? "<div class='alert alert-warning'><i class='fas fa-user-md me-2'></i>Requires Consultation</div>" : "")}
                <div class='row mt-3'>
                    <div class='col-12'>
                        <h6>Prescription Image</h6>
                        <img src='{(string.IsNullOrEmpty(prescription.PrescriptionImage) ? "/Images/no-image.png" : prescription.PrescriptionImage)}'
                             alt='Prescription Image' class='img-fluid border rounded' style='max-height: 400px;' />
                    </div>
                </div>";
        }

        // Helper methods for data binding
        protected string GetPriorityColor(string priority)
        {
            switch (priority?.ToLower())
            {
                case "emergency": return "danger";
                case "urgent": return "warning";
                case "normal": return "secondary";
                default: return "secondary";
            }
        }

        protected string GetStatusColor(string status)
        {
            switch (status?.ToLower())
            {
                case "pending": return "warning";
                case "validated": return "success";
                case "rejected": return "danger";
                case "expired": return "secondary";
                default: return "secondary";
            }
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            
            // Set page metadata
            Page.Title = "Prescription Validation - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Validate customer prescriptions and ensure medication safety with MediEase pharmacist tools.");
                master.AddMetaKeywords("prescription validation, pharmacist tools, medication safety, prescription review");
            }
        }
    }
}
