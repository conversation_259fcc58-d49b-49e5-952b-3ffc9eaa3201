<%@ Page Title="Bulk Upload" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="BulkUpload.aspx.cs" Inherits="MediEase.Admin.BulkUpload" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-upload me-2 text-primary"></i>Bulk Upload System</h2>
                        <p class="text-muted">Import medicines, categories, brands, and offers from CSV/Excel files</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-info" onclick="downloadTemplates()">
                            <i class="fas fa-download me-2"></i>Download Templates
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="viewUploadHistory()">
                            <i class="fas fa-history me-2"></i>Upload History
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-file-upload fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalUploads" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Uploads</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblSuccessfulUploads" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Successful</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblFailedUploads" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Failed</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblPendingUploads" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Processing</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Sections -->
        <div class="row">
            <!-- Medicines Upload -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-pills me-2"></i>Medicines Upload</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Upload medicines data from CSV or Excel file</p>
                        
                        <div class="upload-area border-2 border-dashed border-primary rounded p-4 text-center mb-3" id="medicinesUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h6>Drag & Drop CSV/Excel file here</h6>
                            <p class="text-muted">or click to browse</p>
                            <asp:FileUpload ID="fuMedicines" runat="server" CssClass="form-control d-none" accept=".csv,.xlsx,.xls" />
                            <button type="button" class="btn btn-outline-primary" onclick="$('#<%= fuMedicines.ClientID %>').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>
                        
                        <div class="upload-options mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkValidateOnly" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkValidateOnly.ClientID %>">
                                    Validate only (don't import)
                                </label>
                            </div>
                            <div class="form-check">
                                <asp:CheckBox ID="chkUpdateExisting" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkUpdateExisting.ClientID %>">
                                    Update existing medicines
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnUploadMedicines" runat="server" CssClass="btn btn-primary" 
                                Text="Upload Medicines" OnClick="btnUploadMedicines_Click" />
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadTemplate('medicines')">
                                <i class="fas fa-download me-1"></i>Download Template
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories & Brands Upload -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Categories & Brands Upload</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Upload categories and brands data</p>
                        
                        <div class="mb-3">
                            <label class="form-label">Upload Type</label>
                            <asp:DropDownList ID="ddlUploadType" runat="server" CssClass="form-select">
                                <asp:ListItem Value="categories">Categories</asp:ListItem>
                                <asp:ListItem Value="brands">Brands</asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        
                        <div class="upload-area border-2 border-dashed border-info rounded p-4 text-center mb-3" id="categoriesUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-info mb-3"></i>
                            <h6>Drag & Drop CSV/Excel file here</h6>
                            <p class="text-muted">or click to browse</p>
                            <asp:FileUpload ID="fuCategories" runat="server" CssClass="form-control d-none" accept=".csv,.xlsx,.xls" />
                            <button type="button" class="btn btn-outline-info" onclick="$('#<%= fuCategories.ClientID %>').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnUploadCategories" runat="server" CssClass="btn btn-info" 
                                Text="Upload Data" OnClick="btnUploadCategories_Click" />
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadCategoryTemplate()">
                                <i class="fas fa-download me-1"></i>Download Template
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Offers Upload -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-percentage me-2"></i>Offers & Discounts Upload</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Upload discount offers and promotional data</p>
                        
                        <div class="upload-area border-2 border-dashed border-warning rounded p-4 text-center mb-3" id="offersUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-warning mb-3"></i>
                            <h6>Drag & Drop CSV/Excel file here</h6>
                            <p class="text-muted">or click to browse</p>
                            <asp:FileUpload ID="fuOffers" runat="server" CssClass="form-control d-none" accept=".csv,.xlsx,.xls" />
                            <button type="button" class="btn btn-outline-warning" onclick="$('#<%= fuOffers.ClientID %>').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>
                        
                        <div class="upload-options mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkActivateOffers" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= chkActivateOffers.ClientID %>">
                                    Activate offers immediately
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnUploadOffers" runat="server" CssClass="btn btn-warning" 
                                Text="Upload Offers" OnClick="btnUploadOffers_Click" />
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadTemplate('offers')">
                                <i class="fas fa-download me-1"></i>Download Template
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stock Updates -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>Stock Updates</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Bulk update medicine stock quantities</p>
                        
                        <div class="upload-area border-2 border-dashed border-success rounded p-4 text-center mb-3" id="stockUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                            <h6>Drag & Drop CSV/Excel file here</h6>
                            <p class="text-muted">or click to browse</p>
                            <asp:FileUpload ID="fuStock" runat="server" CssClass="form-control d-none" accept=".csv,.xlsx,.xls" />
                            <button type="button" class="btn btn-outline-success" onclick="$('#<%= fuStock.ClientID %>').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>
                        
                        <div class="upload-options mb-3">
                            <div class="form-check">
                                <asp:CheckBox ID="chkAddToExisting" runat="server" CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkAddToExisting.ClientID %>">
                                    Add to existing stock (instead of replace)
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnUploadStock" runat="server" CssClass="btn btn-success" 
                                Text="Update Stock" OnClick="btnUploadStock_Click" />
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadTemplate('stock')">
                                <i class="fas fa-download me-1"></i>Download Template
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Progress -->
        <asp:Panel ID="pnlUploadProgress" runat="server" Visible="false">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-spinner fa-spin me-2"></i>Upload Progress</h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%" id="uploadProgressBar">
                                    <span id="progressText">0%</span>
                                </div>
                            </div>
                            <div id="uploadStatus">
                                <p class="mb-0">Preparing upload...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <!-- Upload Results -->
        <asp:Panel ID="pnlUploadResults" runat="server" Visible="false">
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Upload Results</h5>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadErrorReport()">
                                <i class="fas fa-download me-1"></i>Download Error Report
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row text-center mb-4">
                                <div class="col-md-3">
                                    <div class="result-stat">
                                        <h3 class="text-primary"><asp:Label ID="lblTotalProcessed" runat="server" Text="0"></asp:Label></h3>
                                        <p class="text-muted">Total Processed</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="result-stat">
                                        <h3 class="text-success"><asp:Label ID="lblSuccessCount" runat="server" Text="0"></asp:Label></h3>
                                        <p class="text-muted">Successful</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="result-stat">
                                        <h3 class="text-warning"><asp:Label ID="lblWarningCount" runat="server" Text="0"></asp:Label></h3>
                                        <p class="text-muted">Warnings</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="result-stat">
                                        <h3 class="text-danger"><asp:Label ID="lblErrorCount" runat="server" Text="0"></asp:Label></h3>
                                        <p class="text-muted">Errors</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="upload-log">
                                <h6>Upload Log:</h6>
                                <div class="log-container bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                                    <asp:Literal ID="litUploadLog" runat="server"></asp:Literal>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>
    </div>

    <!-- Upload History Modal -->
    <div class="modal fade" id="uploadHistoryModal" tabindex="-1" aria-labelledby="uploadHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadHistoryModalLabel">Upload History</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="uploadHistoryContent">
                        <!-- Upload history will be loaded here via AJAX -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .upload-area {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            background-color: #f8f9fa;
        }
        
        .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        
        .result-stat {
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .log-container {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>

    <script>
        // Drag and drop functionality
        $(document).ready(function() {
            setupDragAndDrop('medicinesUploadArea', '<%= fuMedicines.ClientID %>');
            setupDragAndDrop('categoriesUploadArea', '<%= fuCategories.ClientID %>');
            setupDragAndDrop('offersUploadArea', '<%= fuOffers.ClientID %>');
            setupDragAndDrop('stockUploadArea', '<%= fuStock.ClientID %>');
        });

        function setupDragAndDrop(areaId, fileInputId) {
            const uploadArea = document.getElementById(areaId);
            const fileInput = document.getElementById(fileInputId);
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelection(files[0], areaId);
                }
            });
            
            uploadArea.addEventListener('click', function() {
                fileInput.click();
            });
            
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFileSelection(this.files[0], areaId);
                }
            });
        }

        function handleFileSelection(file, areaId) {
            const area = document.getElementById(areaId);
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2) + ' MB';
            
            area.innerHTML = `
                <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                <h6>${fileName}</h6>
                <p class="text-muted">${fileSize}</p>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearFile('${areaId}')">
                    <i class="fas fa-times me-1"></i>Remove
                </button>
            `;
        }

        function clearFile(areaId) {
            location.reload(); // Simple way to reset the form
        }

        function downloadTemplates() {
            window.open('DownloadTemplates.aspx', '_blank');
        }

        function downloadTemplate(type) {
            window.open(`DownloadTemplate.aspx?type=${type}`, '_blank');
        }

        function downloadCategoryTemplate() {
            const type = $('#<%= ddlUploadType.ClientID %>').val();
            window.open(`DownloadTemplate.aspx?type=${type}`, '_blank');
        }

        function viewUploadHistory() {
            loadUploadHistory();
            $('#uploadHistoryModal').modal('show');
        }

        function loadUploadHistory() {
            $.ajax({
                type: 'POST',
                url: 'BulkUpload.aspx/GetUploadHistory',
                data: JSON.stringify({}),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function(response) {
                    if (response.d.success) {
                        $('#uploadHistoryContent').html(response.d.html);
                    } else {
                        $('#uploadHistoryContent').html('<p class="text-danger">Error loading upload history.</p>');
                    }
                },
                error: function() {
                    $('#uploadHistoryContent').html('<p class="text-danger">Error loading upload history.</p>');
                }
            });
        }

        function downloadErrorReport() {
            window.open('DownloadErrorReport.aspx', '_blank');
        }

        // File validation
        function validateFile(fileInput, allowedTypes) {
            const file = fileInput.files[0];
            if (!file) return false;
            
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                alert('Please select a valid file type: ' + allowedTypes.join(', '));
                fileInput.value = '';
                return false;
            }
            
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                alert('File size must be less than 10MB');
                fileInput.value = '';
                return false;
            }
            
            return true;
        }

        // Progress simulation (in real app, this would be updated via SignalR or polling)
        function simulateProgress() {
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                }
                
                $('#uploadProgressBar').css('width', progress + '%');
                $('#progressText').text(Math.round(progress) + '%');
                
                if (progress < 30) {
                    $('#uploadStatus').html('<p class="mb-0">Reading file...</p>');
                } else if (progress < 60) {
                    $('#uploadStatus').html('<p class="mb-0">Validating data...</p>');
                } else if (progress < 90) {
                    $('#uploadStatus').html('<p class="mb-0">Importing records...</p>');
                } else {
                    $('#uploadStatus').html('<p class="mb-0">Finalizing...</p>');
                }
            }, 500);
        }
    </script>
</asp:Content>
