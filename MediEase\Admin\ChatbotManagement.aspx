<%@ Page Title="Chatbot Management" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ChatbotManagement.aspx.cs" Inherits="MediEase.Admin.ChatbotManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-robot me-2 text-primary"></i>AI Chatbot Management</h2>
                        <p class="text-muted">Monitor, configure, and optimize the AI chatbot system</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" onclick="testChatbot()">
                            <i class="fas fa-play me-2"></i>Test Chatbot
                        </button>
                        <button type="button" class="btn btn-info" onclick="exportChatLogs()">
                            <i class="fas fa-download me-2"></i>Export Logs
                        </button>
                        <button type="button" class="btn btn-warning" onclick="clearChatHistory()">
                            <i class="fas fa-trash me-2"></i>Clear History
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-comments fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblTotalChats" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Total Conversations</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblActiveUsers" runat="server" Text="0"></asp:Label></h4>
                        <p class="mb-0">Active Users Today</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblAvgResponseTime" runat="server" Text="0s"></asp:Label></h4>
                        <p class="mb-0">Avg Response Time</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-thumbs-up fa-2x mb-2"></i>
                        <h4><asp:Label ID="lblSatisfactionRate" runat="server" Text="0%"></asp:Label></h4>
                        <p class="mb-0">Satisfaction Rate</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Chatbot Configuration -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Chatbot Configuration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Chatbot Status</label>
                                    <div class="form-check form-switch">
                                        <asp:CheckBox ID="chkChatbotEnabled" runat="server" CssClass="form-check-input" Checked="true" />
                                        <label class="form-check-label" for="<%= chkChatbotEnabled.ClientID %>">
                                            Enable Chatbot
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">AI Model</label>
                                    <asp:DropDownList ID="ddlAiModel" runat="server" CssClass="form-select">
                                        <asp:ListItem Value="deepseek/deepseek-r1-0528-qwen3-8b:free">DeepSeek R1 (Free)</asp:ListItem>
                                        <asp:ListItem Value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</asp:ListItem>
                                        <asp:ListItem Value="openai/gpt-4">GPT-4</asp:ListItem>
                                        <asp:ListItem Value="anthropic/claude-3-haiku">Claude 3 Haiku</asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Response Timeout (seconds)</label>
                                    <asp:TextBox ID="txtResponseTimeout" runat="server" CssClass="form-control" TextMode="Number" placeholder="30"></asp:TextBox>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Max Response Length</label>
                                    <asp:TextBox ID="txtMaxResponseLength" runat="server" CssClass="form-control" TextMode="Number" placeholder="500"></asp:TextBox>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Rate Limit (requests/minute)</label>
                                    <asp:TextBox ID="txtRateLimit" runat="server" CssClass="form-control" TextMode="Number" placeholder="60"></asp:TextBox>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <asp:CheckBox ID="chkGuestAccess" runat="server" CssClass="form-check-input" Checked="true" />
                                        <label class="form-check-label" for="<%= chkGuestAccess.ClientID %>">
                                            Allow Guest Access
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">System Prompt</label>
                                    <asp:TextBox ID="txtSystemPrompt" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="4" 
                                        placeholder="Enter the system prompt that defines the chatbot's behavior and personality..."></asp:TextBox>
                                </div>
                                <asp:Button ID="btnSaveConfiguration" runat="server" CssClass="btn btn-primary" Text="Save Configuration" OnClick="btnSaveConfiguration_Click" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Analytics -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Chat Analytics</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="changeAnalyticsPeriod('today')">Today</button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeAnalyticsPeriod('week')">Week</button>
                            <button type="button" class="btn btn-outline-primary" onclick="changeAnalyticsPeriod('month')">Month</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="chatAnalyticsChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Recent Chat Logs -->
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Recent Chat Logs</h5>
                        <div class="input-group" style="width: 300px;">
                            <asp:TextBox ID="txtSearchLogs" runat="server" CssClass="form-control form-control-sm" placeholder="Search chat logs..."></asp:TextBox>
                            <asp:Button ID="btnSearchLogs" runat="server" CssClass="btn btn-outline-primary btn-sm" Text="Search" OnClick="btnSearchLogs_Click" />
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <asp:GridView ID="gvChatLogs" runat="server" CssClass="table table-hover" 
                                AutoGenerateColumns="false" EmptyDataText="No chat logs found." OnRowCommand="gvChatLogs_RowCommand"
                                AllowPaging="true" PageSize="15" OnPageIndexChanging="gvChatLogs_PageIndexChanging">
                                <Columns>
                                    <asp:TemplateField HeaderText="User & Session">
                                        <ItemTemplate>
                                            <div>
                                                <strong><%# GetUserName(Eval("UserId")) %></strong><br>
                                                <small class="text-muted">Session: <%# Eval("SessionId").ToString().Substring(0, 8) %>...</small><br>
                                                <span class="badge bg-<%# Convert.ToBoolean(Eval("IsGuest")) ? "warning" : "info" %>">
                                                    <%# Convert.ToBoolean(Eval("IsGuest")) ? "Guest" : "User" %>
                                                </span>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Conversation">
                                        <ItemTemplate>
                                            <div class="chat-preview">
                                                <div class="message mb-1">
                                                    <small class="text-muted"><%# Eval("Sender") %>:</small>
                                                    <span><%# TruncateText(Eval("Message").ToString(), 100) %></span>
                                                </div>
                                                <%# !string.IsNullOrEmpty(Eval("Response")?.ToString()) ?
                                                    "<div class='response'><small class='text-muted'>Response:</small> " +
                                                    TruncateText(Eval("Response").ToString(), 100) + "</div>" : "" %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Date & Time">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <strong><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("MMM dd") %></strong><br>
                                                <small class="text-muted"><%# Convert.ToDateTime(Eval("CreatedDate")).ToString("hh:mm tt") %></small>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Message Type">
                                        <ItemTemplate>
                                            <div class="text-center">
                                                <span class="badge bg-info">
                                                    <%# Eval("MessageType") ?? "Text" %>
                                                </span>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    
                                    <asp:TemplateField HeaderText="Actions">
                                        <ItemTemplate>
                                            <div class="btn-group-vertical btn-group-sm">
                                                <button type="button" class="btn btn-outline-info btn-sm view-conversation" 
                                                    data-session-id='<%# Eval("SessionId") %>'>
                                                    <i class="fas fa-eye me-1"></i>View
                                                </button>
                                                <asp:LinkButton runat="server" CssClass="btn btn-outline-danger btn-sm mt-1"
                                                    CommandName="Delete" CommandArgument='<%# Eval("ChatMessageId") %>'
                                                    OnClientClick="return confirm('Are you sure you want to delete this chat log?');">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </asp:LinkButton>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                                <PagerStyle CssClass="pagination-ys" />
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Quick Actions -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="testChatbotResponse()">
                                <i class="fas fa-play me-2"></i>Test Response
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="viewSystemHealth()">
                                <i class="fas fa-heartbeat me-2"></i>System Health
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="resetChatbot()">
                                <i class="fas fa-redo me-2"></i>Reset Chatbot
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="downloadConfiguration()">
                                <i class="fas fa-download me-2"></i>Export Config
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Common Queries -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>Common Queries</h6>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptCommonQueries" runat="server">
                            <ItemTemplate>
                                <div class="query-item mb-3 p-2 border rounded">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <small class="text-muted">Query:</small>
                                            <p class="mb-1 small"><%# TruncateText(Eval("Query").ToString(), 80) %></p>
                                            <span class="badge bg-primary"><%# Eval("Count") %> times</span>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="improveResponse('<%# Eval("Query") %>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                    </div>
                </div>

                <!-- System Status -->
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-server me-2"></i>System Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="status-item d-flex justify-content-between mb-2">
                            <span>AI Service:</span>
                            <span class="badge bg-success">Online</span>
                        </div>
                        <div class="status-item d-flex justify-content-between mb-2">
                            <span>API Quota:</span>
                            <span class="badge bg-warning">75% Used</span>
                        </div>
                        <div class="status-item d-flex justify-content-between mb-2">
                            <span>Response Quality:</span>
                            <span class="badge bg-success">Excellent</span>
                        </div>
                        <div class="status-item d-flex justify-content-between mb-3">
                            <span>Last Update:</span>
                            <span class="small text-muted">2 hours ago</span>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="refreshSystemStatus()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Conversation Modal -->
    <div class="modal fade" id="conversationModal" tabindex="-1" aria-labelledby="conversationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="conversationModalLabel">Chat Conversation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="conversationContent" style="max-height: 400px; overflow-y: auto;">
                        <!-- Conversation will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportConversation()">Export</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Chatbot Modal -->
    <div class="modal fade" id="testChatbotModal" tabindex="-1" aria-labelledby="testChatbotModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="testChatbotModalLabel">Test Chatbot</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Test Message</label>
                        <textarea class="form-control" id="testMessage" rows="3" placeholder="Enter a test message for the chatbot..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Response</label>
                        <div id="testResponse" class="form-control" style="min-height: 100px; background-color: #f8f9fa;">
                            <em class="text-muted">Response will appear here...</em>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="sendTestMessage()">Send Test</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let analyticsChart;

        $(document).ready(function() {
            initializeAnalyticsChart();
            loadChatAnalytics('today');
        });

        function initializeAnalyticsChart() {
            const ctx = document.getElementById('chatAnalyticsChart').getContext('2d');
            analyticsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Chat Volume',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function changeAnalyticsPeriod(period) {
            $('.btn-group button').removeClass('active');
            event.target.classList.add('active');
            loadChatAnalytics(period);
        }

        function loadChatAnalytics(period) {
            $.post('ChatbotManagement.aspx/GetChatAnalytics', 
                { period: period }, 
                function(response) {
                    if (response.d.success) {
                        analyticsChart.data.labels = response.d.labels;
                        analyticsChart.data.datasets[0].data = response.d.data;
                        analyticsChart.update();
                    }
                });
        }

        function testChatbot() {
            $('#testChatbotModal').modal('show');
        }

        function sendTestMessage() {
            const message = $('#testMessage').val();
            if (!message.trim()) {
                alert('Please enter a test message.');
                return;
            }

            $('#testResponse').html('<i class="fas fa-spinner fa-spin"></i> Processing...');

            $.post('ChatbotManagement.aspx/TestChatbotResponse', 
                { message: message }, 
                function(response) {
                    if (response.d.success) {
                        $('#testResponse').html(response.d.response);
                    } else {
                        $('#testResponse').html('<span class="text-danger">Error: ' + response.d.message + '</span>');
                    }
                });
        }

        function exportChatLogs() {
            window.open('ExportChatLogs.aspx', '_blank');
        }

        function clearChatHistory() {
            if (confirm('Are you sure you want to clear all chat history? This action cannot be undone.')) {
                $.post('ChatbotManagement.aspx/ClearChatHistory', {}, function(response) {
                    if (response.d.success) {
                        alert('Chat history cleared successfully!');
                        location.reload();
                    } else {
                        alert('Error clearing chat history: ' + response.d.message);
                    }
                });
            }
        }

        // View conversation
        $(document).on('click', '.view-conversation', function() {
            const sessionId = $(this).data('session-id');
            loadConversation(sessionId);
        });

        function loadConversation(sessionId) {
            $.post('ChatbotManagement.aspx/GetConversation', 
                { sessionId: sessionId }, 
                function(response) {
                    if (response.d.success) {
                        $('#conversationContent').html(response.d.html);
                        $('#conversationModal').modal('show');
                    } else {
                        alert('Error loading conversation: ' + response.d.message);
                    }
                });
        }

        function testChatbotResponse() {
            $('#testChatbotModal').modal('show');
        }

        function viewSystemHealth() {
            // System health check
            $.post('ChatbotManagement.aspx/CheckSystemHealth', {}, function(response) {
                if (response.d.success) {
                    alert('System Health: ' + response.d.status);
                } else {
                    alert('Error checking system health: ' + response.d.message);
                }
            });
        }

        function resetChatbot() {
            if (confirm('Are you sure you want to reset the chatbot? This will clear all conversation context.')) {
                $.post('ChatbotManagement.aspx/ResetChatbot', {}, function(response) {
                    if (response.d.success) {
                        alert('Chatbot reset successfully!');
                    } else {
                        alert('Error resetting chatbot: ' + response.d.message);
                    }
                });
            }
        }

        function downloadConfiguration() {
            window.open('ExportChatbotConfig.aspx', '_blank');
        }

        function improveResponse(query) {
            // Open improvement interface
            alert('Improvement interface for: ' + query);
        }

        function refreshSystemStatus() {
            location.reload();
        }

        function exportConversation() {
            // Export current conversation
            alert('Exporting conversation...');
        }
    </script>
</asp:Content>
