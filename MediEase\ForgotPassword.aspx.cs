using System;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class ForgotPassword : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SetPageMetadata();
                
                // Check if user is already logged in
                var currentUser = SecurityHelper.GetCurrentUser();
                if (currentUser != null)
                {
                    // Redirect to appropriate dashboard
                    Response.Redirect("~/Default.aspx");
                }
            }
        }

        protected void btnResetPassword_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                try
                {
                    var email = txtEmail.Text.Trim().ToLower();
                    
                    using (var db = new MediEaseContext())
                    {
                        var user = db.Users.FirstOrDefault(u => u.Email.ToLower() == email && u.IsActive);
                        
                        if (user != null)
                        {
                            // Generate reset token
                            var resetToken = GenerateResetToken();
                            var resetExpiry = DateTime.Now.AddHours(24); // Token valid for 24 hours
                            
                            // Save reset token to database
                            var passwordReset = new PasswordReset
                            {
                                UserId = user.UserId,
                                ResetToken = resetToken,
                                ExpiryDate = resetExpiry,
                                IsUsed = false,
                                CreatedDate = DateTime.Now,
                                IPAddress = Request.UserHostAddress
                            };
                            
                            db.PasswordResets.Add(passwordReset);
                            db.SaveChanges();
                            
                            // Send reset email
                            SendResetEmail(user, resetToken);
                            
                            // Log the password reset request
                            ErrorLogger.LogUserActivity($"Password reset requested for email: {email}", user.UserId);
                            
                            // Show success message
                            ShowSuccessMessage();
                        }
                        else
                        {
                            // For security, don't reveal if email exists or not
                            // Show the same success message
                            ShowSuccessMessage();
                            
                            // Log the failed attempt
                            ErrorLogger.LogUserActivity($"Password reset attempted for non-existent email: {email}", null);
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error processing password reset request");
                    ShowErrorMessage("An error occurred while processing your request. Please try again later.");
                }
            }
        }

        protected void btnResendEmail_Click(object sender, EventArgs e)
        {
            // Allow user to resend email
            pnlSuccessActions.Visible = false;
            pnlResetForm.Visible = true;
            pnlSuccess.Visible = false;
            pnlError.Visible = false;
        }

        private string GenerateResetToken()
        {
            // Generate a secure random token
            return Guid.NewGuid().ToString("N") + Guid.NewGuid().ToString("N");
        }

        private void SendResetEmail(User user, string resetToken)
        {
            try
            {
                // Create reset URL
                var resetUrl = $"{Request.Url.Scheme}://{Request.Url.Authority}/ResetPassword.aspx?token={resetToken}";
                
                // Email content
                var body = $@"
                    <html>
                    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                            <div style='background-color: #007bff; color: white; padding: 20px; text-align: center;'>
                                <h2>Password Reset Request</h2>
                            </div>
                            <div style='padding: 20px; background-color: #f8f9fa;'>
                                <p>Hello {user.FirstName},</p>
                                <p>We received a request to reset your password for your MediEase account.</p>
                                <p>Click the button below to reset your password:</p>
                                <div style='text-align: center; margin: 30px 0;'>
                                    <a href='{resetUrl}' style='background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Reset Password</a>
                                </div>
                                <p>Or copy and paste this link into your browser:</p>
                                <p style='word-break: break-all; background-color: #e9ecef; padding: 10px; border-radius: 3px;'>{resetUrl}</p>
                                <p><strong>Important:</strong></p>
                                <ul>
                                    <li>This link will expire in 24 hours</li>
                                    <li>If you didn't request this reset, please ignore this email</li>
                                    <li>For security, this link can only be used once</li>
                                </ul>
                                <p>If you have any questions, please contact our support team.</p>
                                <p>Best regards,<br>The MediEase Team</p>
                            </div>
                            <div style='background-color: #6c757d; color: white; padding: 15px; text-align: center; font-size: 12px;'>
                                <p>This is an automated email. Please do not reply to this message.</p>
                                <p>© 2024 MediEase. All rights reserved.</p>
                            </div>
                        </div>
                    </body>
                    </html>
                ";

                // For now, we'll log the email content since SMTP might not be configured
                // In production, you would send the actual email
                ErrorLogger.LogUserActivity($"Password reset email would be sent to {user.Email} with token {resetToken}", user.UserId);
                
                // TODO: Implement actual email sending using SMTP
                // var mailMessage = new MailMessage();
                // mailMessage.To.Add(user.Email);
                // mailMessage.Subject = subject;
                // mailMessage.Body = body;
                // mailMessage.IsBodyHtml = true;
                // smtpClient.Send(mailMessage);
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error sending password reset email");
                throw; // Re-throw to be handled by calling method
            }
        }

        private void ShowSuccessMessage()
        {
            pnlResetForm.Visible = false;
            pnlSuccess.Visible = true;
            pnlSuccessActions.Visible = true;
            pnlError.Visible = false;
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            lblError.Text = message;
        }

        private void SetPageMetadata()
        {
            Page.Title = "Forgot Password - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Reset your MediEase account password securely. Enter your email address to receive a password reset link.");
                master.AddMetaKeywords("forgot password, reset password, MediEase login, account recovery, password help");
            }
        }
    }
}
