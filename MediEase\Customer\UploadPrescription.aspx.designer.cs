//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase.Customer
{
    public partial class UploadPrescription
    {
        /// <summary>
        /// txtDoctorName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtDoctorName;

        /// <summary>
        /// rfvDoctorName control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvDoctorName;

        /// <summary>
        /// txtDoctorLicense control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtDoctorLicense;

        /// <summary>
        /// txtHospitalClinic control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtHospitalClinic;

        /// <summary>
        /// txtDoctorPhone control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtDoctorPhone;

        /// <summary>
        /// txtPrescriptionDate control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtPrescriptionDate;

        /// <summary>
        /// rfvPrescriptionDate control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvPrescriptionDate;

        /// <summary>
        /// txtValidUntil control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtValidUntil;

        /// <summary>
        /// txtPatientDiagnosis control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtPatientDiagnosis;

        /// <summary>
        /// txtPatientSymptoms control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtPatientSymptoms;

        /// <summary>
        /// txtPatientAllergies control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtPatientAllergies;

        /// <summary>
        /// txtCurrentMedications control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtCurrentMedications;

        /// <summary>
        /// txtSpecialInstructions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtSpecialInstructions;

        /// <summary>
        /// chkIsEmergency control.
        /// </summary>
        protected global::System.Web.UI.WebControls.CheckBox chkIsEmergency;

        /// <summary>
        /// chkRequiresConsultation control.
        /// </summary>
        protected global::System.Web.UI.WebControls.CheckBox chkRequiresConsultation;

        /// <summary>
        /// chkIsRepeatPrescription control.
        /// </summary>
        protected global::System.Web.UI.WebControls.CheckBox chkIsRepeatPrescription;

        /// <summary>
        /// fuPrescriptionImage control.
        /// </summary>
        protected global::System.Web.UI.WebControls.FileUpload fuPrescriptionImage;

        /// <summary>
        /// rfvPrescriptionImage control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvPrescriptionImage;

        /// <summary>
        /// fuAdditionalDocument control.
        /// </summary>
        protected global::System.Web.UI.WebControls.FileUpload fuAdditionalDocument;

        /// <summary>
        /// txtNotes control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtNotes;

        /// <summary>
        /// btnSubmitPrescription control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnSubmitPrescription;
    }
}
