<%@ Page Title="Customer Dashboard" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Dashboard.aspx.cs" Inherits="MediEase.Customer.Dashboard" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h3 class="mb-1">
                                    <i class="fas fa-user-circle me-2"></i>Welcome back, <asp:Literal ID="litUserName" runat="server" />!
                                </h3>
                                <p class="mb-0 opacity-75">Manage your health and medications with ease</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex justify-content-end gap-2">
                                    <span class="badge bg-light text-primary fs-6">
                                        <i class="fas fa-coins me-1"></i>
                                        <asp:Literal ID="litLoyaltyPoints" runat="server" /> Points
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card dashboard-card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <div class="dashboard-icon mb-2">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <h3 class="dashboard-number">
                            <asp:Literal ID="litTotalOrders" runat="server" Text="0" />
                        </h3>
                        <p class="dashboard-label mb-0">Total Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card dashboard-card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <div class="dashboard-icon mb-2">
                            <i class="fas fa-prescription-bottle"></i>
                        </div>
                        <h3 class="dashboard-number">
                            <asp:Literal ID="litActivePrescriptions" runat="server" Text="0" />
                        </h3>
                        <p class="dashboard-label mb-0">Active Prescriptions</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card dashboard-card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <div class="dashboard-icon mb-2">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h3 class="dashboard-number">
                            <asp:Literal ID="litPendingDeliveries" runat="server" Text="0" />
                        </h3>
                        <p class="dashboard-label mb-0">Pending Deliveries</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card dashboard-card bg-danger text-white h-100">
                    <div class="card-body text-center">
                        <div class="dashboard-icon mb-2">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="dashboard-number">
                            <asp:Literal ID="litReminders" runat="server" Text="0" />
                        </h3>
                        <p class="dashboard-label mb-0">Medication Reminders</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <a href="~/Medicines.aspx" runat="server" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                    <i class="fas fa-search fa-2x mb-2"></i>
                                    <span>Browse Medicines</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <a href="UploadPrescription.aspx" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                    <i class="fas fa-upload fa-2x mb-2"></i>
                                    <span>Upload Prescription</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <a href="AIRecommendations.aspx" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                    <i class="fas fa-robot fa-2x mb-2"></i>
                                    <span>AI Recommendations</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <a href="PriceComparison.aspx" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                    <i class="fas fa-balance-scale fa-2x mb-2"></i>
                                    <span>Price Comparison</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <a href="OrderTracking.aspx" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                    <i class="fas fa-truck fa-2x mb-2"></i>
                                    <span>Track Orders</span>
                                </a>
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-6">
                                <a href="LoyaltyProgram.aspx" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                    <i class="fas fa-star fa-2x mb-2"></i>
                                    <span>Loyalty Program</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders and Prescriptions -->
        <div class="row g-4">
            <!-- Recent Orders -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-bag me-2"></i>Recent Orders
                        </h5>
                        <a href="~/Customer/Orders.aspx" runat="server" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptRecentOrders" runat="server">
                            <ItemTemplate>
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <div>
                                        <h6 class="mb-1">Order #<%# Eval("OrderNumber") %></h6>
                                        <small class="text-muted"><%# Eval("OrderDate", "{0:MMM dd, yyyy}") %></small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold">$<%# Eval("TotalAmount", "{0:F2}") %></div>
                                        <span class="badge bg-<%# GetStatusBadgeClass(Eval("Status").ToString()) %>">
                                            <%# Eval("Status") %>
                                        </span>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                        
                        <asp:Panel ID="pnlNoOrders" runat="server" Visible="false" CssClass="text-center py-4">
                            <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No orders yet. Start shopping to see your orders here!</p>
                            <a href="~/Medicines.aspx" runat="server" class="btn btn-primary">Browse Medicines</a>
                        </asp:Panel>
                    </div>
                </div>
            </div>

            <!-- Recent Prescriptions -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-prescription-bottle me-2"></i>Recent Prescriptions
                        </h5>
                        <a href="~/Customer/Prescriptions.aspx" runat="server" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptRecentPrescriptions" runat="server">
                            <ItemTemplate>
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <div>
                                        <h6 class="mb-1">Prescription #<%# Eval("PrescriptionNumber") %></h6>
                                        <small class="text-muted">Dr. <%# Eval("DoctorName") %></small>
                                        <br>
                                        <small class="text-muted"><%# Eval("PrescriptionDate", "{0:MMM dd, yyyy}") %></small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-<%# GetPrescriptionStatusBadgeClass(Eval("Status").ToString()) %>">
                                            <%# Eval("Status") %>
                                        </span>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                        
                        <asp:Panel ID="pnlNoPrescriptions" runat="server" Visible="false" CssClass="text-center py-4">
                            <i class="fas fa-prescription-bottle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No prescriptions uploaded yet.</p>
                            <a href="~/Prescription.aspx" runat="server" class="btn btn-primary">Upload Prescription</a>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>

        <!-- Health Reminders -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>Health Reminders
                        </h5>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptHealthReminders" runat="server">
                            <ItemTemplate>
                                <div class="alert alert-info d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-pills me-2"></i>
                                        <strong><%# Eval("MedicineName") %></strong> - <%# Eval("Dosage") %>
                                        <br>
                                        <small>Next dose: <%# Eval("NextDose", "{0:MMM dd, yyyy hh:mm tt}") %></small>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary">Mark Taken</button>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                        
                        <asp:Panel ID="pnlNoReminders" runat="server" Visible="false" CssClass="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">No pending medication reminders. You're all caught up!</p>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>

<asp:Content ID="ScriptContent" ContentPlaceHolderID="ScriptContent" runat="server">
    <script>
        $(document).ready(function() {
            // Initialize dashboard
            initializeDashboard();
            
            // Animate dashboard numbers
            animateDashboardNumbers();
        });

        function initializeDashboard() {
            // Add click handlers for quick actions
            $('.dashboard-card').hover(
                function() { $(this).addClass('shadow-lg'); },
                function() { $(this).removeClass('shadow-lg'); }
            );

            // Auto-refresh dashboard data every 5 minutes
            setInterval(function() {
                // You can implement auto-refresh here if needed
                console.log('Dashboard auto-refresh check');
            }, 300000); // 5 minutes
        }

        function animateDashboardNumbers() {
            $('.dashboard-number').each(function() {
                const $this = $(this);
                const target = parseInt($this.text()) || 0;
                let current = 0;
                const increment = target / 30;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    $this.text(Math.floor(current));
                }, 50);
            });
        }
    </script>
</asp:Content>
