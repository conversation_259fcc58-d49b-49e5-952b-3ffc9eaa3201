//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace MediEase
{
    public partial class ForgotPassword
    {
        /// <summary>
        /// pnlSuccess control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlSuccess;

        /// <summary>
        /// pnlError control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlError;

        /// <summary>
        /// lblError control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Label lblError;

        /// <summary>
        /// pnlResetForm control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlResetForm;

        /// <summary>
        /// txtEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.TextBox txtEmail;

        /// <summary>
        /// rfvEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator rfvEmail;

        /// <summary>
        /// revEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.RegularExpressionValidator revEmail;

        /// <summary>
        /// btnResetPassword control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnResetPassword;

        /// <summary>
        /// pnlSuccessActions control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Panel pnlSuccessActions;

        /// <summary>
        /// btnResendEmail control.
        /// </summary>
        protected global::System.Web.UI.WebControls.Button btnResendEmail;
    }
}
