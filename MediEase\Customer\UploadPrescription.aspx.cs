using System;
using System.IO;
using System.Linq;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;
using Prescription = MediEase.Models.Prescription;

namespace MediEase.Customer
{
    public partial class UploadPrescription : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Ensure user is authenticated and is a customer
            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null || currentUser.Role.ToLower() != "customer")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            if (!IsPostBack)
            {
                InitializePage();
            }
        }

        private void InitializePage()
        {
            // Set default prescription date to today
            txtPrescriptionDate.Text = DateTime.Today.ToString("yyyy-MM-dd");
            
            // Set default valid until date (30 days from today)
            txtValidUntil.Text = DateTime.Today.AddDays(30).ToString("yyyy-MM-dd");
        }

        protected void btnSubmitPrescription_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid) return;

            var currentUser = SecurityHelper.GetCurrentUser();
            if (currentUser == null) return;

            try
            {
                using (var db = new MediEaseContext())
                {
                    // Create new prescription
                    var prescription = new Prescription
                    {
                        PrescriptionNumber = GeneratePrescriptionNumber(),
                        PatientId = currentUser.UserId,
                        DoctorName = txtDoctorName.Text.Trim(),
                        DoctorLicense = txtDoctorLicense.Text.Trim(),
                        HospitalClinic = txtHospitalClinic.Text.Trim(),
                        DoctorPhone = txtDoctorPhone.Text.Trim(),
                        PrescriptionDate = DateTime.Parse(txtPrescriptionDate.Text),
                        ValidUntil = string.IsNullOrEmpty(txtValidUntil.Text) ? (DateTime?)null : DateTime.Parse(txtValidUntil.Text),
                        PatientDiagnosis = txtPatientDiagnosis.Text.Trim(),
                        PatientSymptoms = txtPatientSymptoms.Text.Trim(),
                        PatientAllergies = txtPatientAllergies.Text.Trim(),
                        CurrentMedications = txtCurrentMedications.Text.Trim(),
                        SpecialInstructions = txtSpecialInstructions.Text.Trim(),
                        IsEmergency = chkIsEmergency.Checked,
                        RequiresConsultation = chkRequiresConsultation.Checked,
                        IsRepeatPrescription = chkIsRepeatPrescription.Checked,
                        Notes = txtNotes.Text.Trim(),
                        Status = "Pending",
                        CreatedDate = DateTime.Now,
                        CreatedBy = currentUser.UserId
                    };

                    // Handle file uploads
                    if (fuPrescriptionImage.HasFile)
                    {
                        var imagePath = SavePrescriptionFile(fuPrescriptionImage, "prescription");
                        if (!string.IsNullOrEmpty(imagePath))
                        {
                            prescription.PrescriptionImage = imagePath;
                        }
                    }

                    if (fuAdditionalDocument.HasFile)
                    {
                        var docPath = SavePrescriptionFile(fuAdditionalDocument, "additional");
                        if (!string.IsNullOrEmpty(docPath))
                        {
                            prescription.AdditionalDocument = docPath;
                        }
                    }

                    db.Prescriptions.Add(prescription);
                    db.SaveChanges();

                    // Log the activity
                    ErrorLogger.LogUserActivity($"Prescription uploaded: {prescription.PrescriptionNumber}", currentUser.UserId);

                    // Show success message and redirect
                    ShowSuccessMessage("Prescription uploaded successfully! Our pharmacist will review it shortly.");
                    
                    // Clear form
                    ClearForm();
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error uploading prescription");
                ShowErrorMessage("Error uploading prescription. Please try again.");
            }
        }

        private string SavePrescriptionFile(System.Web.UI.WebControls.FileUpload fileUpload, string fileType)
        {
            try
            {
                if (!fileUpload.HasFile) return null;

                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".pdf" };
                var fileExtension = Path.GetExtension(fileUpload.FileName).ToLower();
                
                if (!allowedExtensions.Contains(fileExtension))
                {
                    ShowErrorMessage($"Invalid file type for {fileType}. Please upload JPG, PNG, GIF, or PDF files only.");
                    return null;
                }

                // Check file size (5MB limit)
                if (fileUpload.PostedFile.ContentLength > 5 * 1024 * 1024)
                {
                    ShowErrorMessage($"File size for {fileType} must be less than 5MB.");
                    return null;
                }

                var fileName = $"{fileType}_{DateTime.Now.Ticks}_{Path.GetFileName(fileUpload.FileName)}";
                var uploadPath = Server.MapPath("~/Uploads/Prescriptions/");
                
                // Create directory if it doesn't exist
                if (!Directory.Exists(uploadPath))
                    Directory.CreateDirectory(uploadPath);
                
                var filePath = Path.Combine(uploadPath, fileName);
                fileUpload.SaveAs(filePath);
                
                return fileName;
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, $"Error saving {fileType} file");
                ShowErrorMessage($"Error uploading {fileType} file.");
                return null;
            }
        }

        private string GeneratePrescriptionNumber()
        {
            return "RX" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(100, 999);
        }

        private void ClearForm()
        {
            txtDoctorName.Text = "";
            txtDoctorLicense.Text = "";
            txtHospitalClinic.Text = "";
            txtDoctorPhone.Text = "";
            txtPrescriptionDate.Text = DateTime.Today.ToString("yyyy-MM-dd");
            txtValidUntil.Text = DateTime.Today.AddDays(30).ToString("yyyy-MM-dd");
            txtPatientDiagnosis.Text = "";
            txtPatientSymptoms.Text = "";
            txtPatientAllergies.Text = "";
            txtCurrentMedications.Text = "";
            txtSpecialInstructions.Text = "";
            txtNotes.Text = "";
            chkIsEmergency.Checked = false;
            chkRequiresConsultation.Checked = false;
            chkIsRepeatPrescription.Checked = false;
        }

        private void ShowSuccessMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowSuccessMessage(message);
        }

        private void ShowErrorMessage(string message)
        {
            var master = Master as SiteMaster;
            master?.ShowErrorMessage(message);
        }
    }
}
