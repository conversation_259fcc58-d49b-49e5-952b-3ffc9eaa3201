   .winmd.dll.exe X   3C:\Users\<USER>\Desktop\Project\App_Data\MediEase.mdf7C:\Users\<USER>\Desktop\Project\App_Data\MediEase_log.ldfFC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjax.jsYC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.jsTC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.jsJC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxCore.jsSC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.jsMC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.jsMC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.jsSC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.jsKC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.jsNC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.jsQC:\Users\<USER>\Desktop\Project\Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js(C:\Users\<USER>\Desktop\Project\Web.config2C:\Users\<USER>\Desktop\Project\Admin\Dashboard.aspx'C:\Users\<USER>\Desktop\Project\Cart.aspx+C:\Users\<USER>\Desktop\Project\Checkout.aspx5C:\Users\<USER>\Desktop\Project\Controls\AIChatbot.ascx=C:\Users\<USER>\Desktop\Project\Customer\AIRecommendations.aspx5C:\Users\<USER>\Desktop\Project\Customer\Dashboard.aspx3C:\Users\<USER>\Desktop\Project\Customer\Profile.aspx>C:\Users\<USER>\Desktop\Project\Customer\UploadPrescription.aspx;C:\Users\<USER>\Desktop\Project\Customer\PriceComparison.aspx:C:\Users\<USER>\Desktop\Project\Customer\LoyaltyProgram.aspx9C:\Users\<USER>\Desktop\Project\Customer\OrderTracking.aspx*C:\Users\<USER>\Desktop\Project\Default.aspx.C:\Users\<USER>\Desktop\Project\GuestSearch.aspx(C:\Users\<USER>\Desktop\Project\Login.aspx4C:\Users\<USER>\Desktop\Project\OrderConfirmation.aspx/C:\Users\<USER>\Desktop\Project\Prescription.aspx+C:\Users\<USER>\Desktop\Project\Register.aspx,C:\Users\<USER>\Desktop\Project\Medicines.aspx(C:\Users\<USER>\Desktop\Project\About.aspx*C:\Users\<USER>\Desktop\Project\Contact.aspx&C:\Users\<USER>\Desktop\Project\FAQ.aspx1C:\Users\<USER>\Desktop\Project\ForgotPassword.aspx0C:\Users\<USER>\Desktop\Project\ResetPassword.aspx7C:\Users\<USER>\Desktop\Project\Pharmacist\Dashboard.aspxDC:\Users\<USER>\Desktop\Project\Pharmacist\PrescriptionValidation.aspx=C:\Users\<USER>\Desktop\Project\Pharmacist\OrderManagement.aspxAC:\Users\<USER>\Desktop\Project\Pharmacist\InventoryManagement.aspx7C:\Users\<USER>\Desktop\Project\Admin\UserManagement.aspx:C:\Users\<USER>\Desktop\Project\Customer\FamilyProfiles.aspxAC:\Users\<USER>\Desktop\Project\Customer\AccessibilitySettings.aspx6C:\Users\<USER>\Desktop\Project\Customer\AutoRefill.aspx;C:\Users\<USER>\Desktop\Project\Customer\HealthReminders.aspx;C:\Users\<USER>\Desktop\Project\Customer\FeedbackReviews.aspx?C:\Users\<USER>\Desktop\Project\Pharmacist\InvoiceGeneration.aspx5C:\Users\<USER>\Desktop\Project\Pharmacist\Reports.aspx=C:\Users\<USER>\Desktop\Project\Pharmacist\PriceManagement.aspx;C:\Users\<USER>\Desktop\Project\Admin\MedicineManagement.aspx3C:\Users\<USER>\Desktop\Project\Admin\BulkUpload.aspx<C:\Users\<USER>\Desktop\Project\Admin\SystemConfiguration.aspx6C:\Users\<USER>\Desktop\Project\Admin\BackupRestore.aspx:C:\Users\<USER>\Desktop\Project\Admin\ChatbotManagement.aspx;C:\Users\<USER>\Desktop\Project\Shared\InternalMessaging.aspx)C:\Users\<USER>\Desktop\Project\Site.Master)C:\Users\<USER>\Desktop\Project\Global.asax.C:\Users\<USER>\Desktop\Project\Content\Site.css-C:\Users\<USER>\Desktop\Project\Scripts\Site.js7C:\Users\<USER>\Desktop\Project\Handlers\ChatHandler.ashx-C:\Users\<USER>\Desktop\Project\packages.config.C:\Users\<USER>\Desktop\Project\Web.Debug.config0C:\Users\<USER>\Desktop\Project\Web.Release.configZC:\Users\<USER>\Desktop\Project\packages\BCrypt.Net-Next.4.0.3\lib\net48\BCrypt.Net-Next.dllZC:\Users\<USER>\Desktop\Project\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dlldC:\Users\<USER>\Desktop\Project\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Microsoft.CSharp.dllC:\Users\<USER>\Desktop\Project\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dllvC:\Users\<USER>\Desktop\Project\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll[C:\Users\<USER>\Desktop\Project\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll|C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ComponentModel.DataAnnotations.dllkC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Configuration.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dlltC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.DataSetExtensions.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dlleC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dllpC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.EnterpriseServices.dlluC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.ApplicationServices.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dllmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.DynamicData.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Entity.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dlljC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Services.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName}+C:\Users\<USER>\Desktop\Project\MediEase\bin\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}ZC:\Users\<USER>\Desktop\Project\MediEase\obj\Debug\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         