using System;
using System.Net.Mail;
using System.Web.UI;
using MediEase.DAL;
using MediEase.Models;
using MediEase.Utilities;

namespace MediEase
{
    public partial class Contact : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SetPageMetadata();
            }
        }

        protected void btnSend_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                try
                {
                    // Save contact message to database
                    SaveContactMessage();
                    
                    // Send email notification (optional)
                    SendEmailNotification();
                    
                    // Show success message
                    ShowSuccessMessage();
                    
                    // Clear form
                    ClearForm();
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError(ex, "Error processing contact form submission");
                    ShowErrorMessage("There was an error sending your message. Please try again or contact us directly.");
                }
            }
        }

        private void SaveContactMessage()
        {
            using (var db = new MediEaseContext())
            {
                var contactMessage = new ContactMessage
                {
                    Name = txtName.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Subject = ddlSubject.SelectedValue,
                    Message = txtMessage.Text.Trim(),
                    SubscribeToNewsletter = chkNewsletter.Checked,
                    CreatedDate = DateTime.Now,
                    Status = "New",
                    IPAddress = Request.UserHostAddress
                };

                db.ContactMessages.Add(contactMessage);
                db.SaveChanges();

                // Log the contact form submission
                ErrorLogger.LogUserActivity($"Contact form submitted by {contactMessage.Email}", null);
            }
        }

        private void SendEmailNotification()
        {
            try
            {
                // This is a basic email notification - you would configure SMTP settings in web.config
                var subject = $"New Contact Form Submission: {ddlSubject.SelectedValue}";
                var body = $@"
                    <h3>New Contact Form Submission</h3>
                    <p><strong>Name:</strong> {txtName.Text}</p>
                    <p><strong>Email:</strong> {txtEmail.Text}</p>
                    <p><strong>Phone:</strong> {txtPhone.Text}</p>
                    <p><strong>Subject:</strong> {ddlSubject.SelectedValue}</p>
                    <p><strong>Message:</strong></p>
                    <p>{txtMessage.Text.Replace("\n", "<br>")}</p>
                    <p><strong>Newsletter Subscription:</strong> {(chkNewsletter.Checked ? "Yes" : "No")}</p>
                    <p><strong>Submitted:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>
                ";

                // Note: You would need to configure SMTP settings in web.config for this to work
                // For now, we'll just log that an email would be sent
                ErrorLogger.LogUserActivity($"Email notification would be sent for contact form from {txtEmail.Text}", null);
            }
            catch (Exception ex)
            {
                // Don't fail the entire process if email fails
                ErrorLogger.LogError(ex, "Error sending email notification for contact form");
            }
        }

        private void ShowSuccessMessage()
        {
            pnlSuccess.Visible = true;
            pnlError.Visible = false;
        }

        private void ShowErrorMessage(string message)
        {
            pnlError.Visible = true;
            pnlSuccess.Visible = false;
            lblError.Text = message;
        }

        private void ClearForm()
        {
            txtName.Text = "";
            txtEmail.Text = "";
            txtPhone.Text = "";
            ddlSubject.SelectedIndex = 0;
            txtMessage.Text = "";
            chkNewsletter.Checked = false;
        }

        private void SetPageMetadata()
        {
            Page.Title = "Contact Us - MediEase";
            
            var master = Master as SiteMaster;
            if (master != null)
            {
                master.AddMetaDescription("Contact MediEase for any questions about our pharmacy services, prescriptions, or online orders. We're here to help with your healthcare needs.");
                master.AddMetaKeywords("contact, pharmacy support, customer service, MediEase contact, pharmacy help, prescription support");
            }
        }
    }
}
